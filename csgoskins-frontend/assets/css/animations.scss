// 动画样式文件 - 遵循 Vue3 + Nuxt3 + TypeScript 开发规范
// CSGO 主题动画效果，桌面端优先，移动端适配

// ===================
// 基础动画变量
// ===================
:root {
  // 动画持续时间
  --animation-fast: 0.15s;
  --animation-normal: 0.3s;
  --animation-slow: 0.5s;
  --animation-slower: 0.8s;
  
  // 缓动函数
  --ease-out-quad: cubic-bezier(0.25, 0.46, 0.45, 0.94);
  --ease-out-cubic: cubic-bezier(0.215, 0.61, 0.355, 1);
  --ease-in-out-cubic: cubic-bezier(0.645, 0.045, 0.355, 1);
  --ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

// ===================
// 关键帧动画
// ===================

// 发光脉冲动画
@keyframes glow-pulse {
  0%, 100% {
    box-shadow: 0 0 20px var(--color-primary-30);
  }
  50% {
    box-shadow: 0 0 30px var(--color-primary-50), 0 0 40px var(--color-primary-30);
  }
}

// 稀有度发光动画
@keyframes rarity-glow {
  0%, 100% {
    filter: drop-shadow(0 0 10px currentColor);
  }
  50% {
    filter: drop-shadow(0 0 20px currentColor) drop-shadow(0 0 30px currentColor);
  }
}

// 悬浮动画
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

// 旋转动画
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// 骨架屏脉冲动画
@keyframes skeleton-pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.4;
  }
  100% {
    opacity: 1;
  }
}

// 渐入动画
@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

// 从下方滑入
@keyframes slide-in-up {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 从左侧滑入
@keyframes slide-in-left {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

// 从右侧滑入
@keyframes slide-in-right {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

// 缩放动画
@keyframes scale-in {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

// 弹跳动画
@keyframes bounce-in {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

// ===================
// 动画工具类
// ===================

// 基础动画类
.animate-glow-pulse {
  animation: glow-pulse var(--animation-slow) infinite;
}

.animate-rarity-glow {
  animation: rarity-glow 2s ease-in-out infinite;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

.animate-skeleton-pulse {
  animation: skeleton-pulse 1.5s ease-in-out infinite;
}

// 进入动画类
.animate-fade-in {
  animation: fade-in var(--animation-normal) var(--ease-out-cubic);
}

.animate-slide-in-up {
  animation: slide-in-up var(--animation-normal) var(--ease-out-cubic);
}

.animate-slide-in-left {
  animation: slide-in-left var(--animation-normal) var(--ease-out-cubic);
}

.animate-slide-in-right {
  animation: slide-in-right var(--animation-normal) var(--ease-out-cubic);
}

.animate-scale-in {
  animation: scale-in var(--animation-normal) var(--ease-out-cubic);
}

.animate-bounce-in {
  animation: bounce-in var(--animation-slow) var(--ease-bounce);
}

// 延迟动画类
.animate-delay-100 {
  animation-delay: 0.1s;
}

.animate-delay-200 {
  animation-delay: 0.2s;
}

.animate-delay-300 {
  animation-delay: 0.3s;
}

.animate-delay-500 {
  animation-delay: 0.5s;
}

// ===================
// 过渡效果
// ===================

// 通用过渡
.transition-all {
  transition: all var(--animation-normal) var(--ease-out-cubic);
}

.transition-fast {
  transition: all var(--animation-fast) var(--ease-out-quad);
}

.transition-slow {
  transition: all var(--animation-slow) var(--ease-out-cubic);
}

// 悬停效果
.hover-scale {
  transition: transform var(--animation-normal) var(--ease-out-cubic);
  
  &:hover {
    transform: scale(1.05);
  }
}

.hover-lift {
  transition: transform var(--animation-normal) var(--ease-out-cubic);
  
  &:hover {
    transform: translateY(-2px);
  }
}

.hover-glow {
  transition: box-shadow var(--animation-normal) var(--ease-out-cubic);
  
  &:hover {
    box-shadow: 0 0 25px var(--color-primary-30);
  }
}

// ===================
// 开箱特效动画
// ===================

// 开箱转盘旋转
@keyframes case-spin {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-100%);
  }
}

.animate-case-spin {
  animation: case-spin 3s cubic-bezier(0.25, 0.1, 0.25, 1);
}

// 皮肤出现特效
@keyframes skin-reveal {
  0% {
    opacity: 0;
    transform: scale(0.5) rotateY(90deg);
    filter: blur(10px);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.1) rotateY(0deg);
    filter: blur(2px);
  }
  100% {
    opacity: 1;
    transform: scale(1) rotateY(0deg);
    filter: blur(0px);
  }
}

.animate-skin-reveal {
  animation: skin-reveal 1s var(--ease-bounce);
}

// 稀有度光芒特效
@keyframes rarity-burst {
  0% {
    opacity: 0;
    transform: scale(0);
  }
  30% {
    opacity: 1;
    transform: scale(1.2);
  }
  100% {
    opacity: 0;
    transform: scale(2);
  }
}

.animate-rarity-burst {
  animation: rarity-burst 0.8s ease-out;
}

// ===================
// 移动端动画优化
// ===================

@media (max-width: 768px) {
  // 移动端减少动画复杂度
  .animate-glow-pulse,
  .animate-rarity-glow {
    animation-duration: 1s; // 加快动画速度
  }
  
  .animate-float {
    animation-duration: 2s; // 减少浮动幅度
  }
  
  // 移动端禁用复杂动画
  @media (prefers-reduced-motion: reduce) {
    .animate-glow-pulse,
    .animate-rarity-glow,
    .animate-float {
      animation: none;
    }
    
    .hover-scale,
    .hover-lift {
      transform: none !important;
    }
  }
}

// ===================
// 性能优化
// ===================

// 启用硬件加速的元素
.gpu-accelerated {
  transform: translateZ(0);
  will-change: transform;
}

// 动画性能优化
.animate-optimized {
  backface-visibility: hidden;
  perspective: 1000px;
  transform: translateZ(0);
}

// 减少重绘的动画
.animate-efficient {
  will-change: transform, opacity;
}

// ===================
// 对战动画
// ===================

// 箱子开启动画
@keyframes case-opening {
  0% {
    transform: scale(1) rotate(0deg);
    box-shadow: 0 0 0 rgba(255, 215, 0, 0);
  }
  30% {
    transform: scale(1.1) rotate(360deg);
    box-shadow: 0 0 20px rgba(255, 215, 0, 0.6);
  }
  70% {
    transform: scale(1.2) rotate(720deg);
    box-shadow: 0 0 30px rgba(255, 215, 0, 0.8);
  }
  100% {
    transform: scale(1) rotate(1080deg);
    box-shadow: 0 0 0 rgba(255, 215, 0, 0);
  }
}

.case-opening-animation {
  animation: case-opening 8s ease-in-out;
  transform-origin: center;
}

// 玩家状态动画
.player-waiting {
  opacity: 0.7;
  transform: scale(0.95);
  transition: all 0.3s ease;
}

.player-opening {
  opacity: 1;
  transform: scale(1.05);
  box-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
  transition: all 0.3s ease;
  animation: pulse-glow 2s infinite;
}

.player-completed {
  opacity: 1;
  transform: scale(1);
  box-shadow: 0 0 15px rgba(0, 255, 0, 0.5);
  transition: all 0.3s ease;
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
  }
  50% {
    box-shadow: 0 0 30px rgba(0, 255, 255, 0.8);
  }
}
