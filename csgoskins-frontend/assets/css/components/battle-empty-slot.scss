// 空位卡片样式 - 与BattlePlayerCard协调
.empty-slot-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.03) 0%, rgba(255, 255, 255, 0.01) 100%);
  border: 1px dashed rgba(255, 255, 255, 0.15);
  opacity: 0.8;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  &:hover {
    opacity: 1;
    border-color: rgba(255, 255, 255, 0.25);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.02) 100%);
  }
  
  // 简化的背景装饰
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.03) 0%, rgba(147, 51, 234, 0.03) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
  }
  
  &:hover::before {
    opacity: 1;
  }

  // 🎨 等待状态下的特殊样式
  &.waiting-state {
    min-height: 280px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 2rem 1.5rem;
    
    // 等待状态下的背景动效
    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.05), transparent);
      opacity: 0;
      animation: waiting-glow 3s ease-in-out infinite;
      pointer-events: none;
    }
  }
}

// 🎨 等待状态下的紧凑布局
.waiting-layout {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  
  .compact-header {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1.5rem;
    text-align: center;
    
    .avatar-section {
      position: relative;
      
      .empty-avatar {
        width: 4rem;
        height: 4rem;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.05);
        border: 2px dashed rgba(255, 255, 255, 0.2);
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
        
        &:hover {
          background: rgba(255, 255, 255, 0.08);
          border-color: rgba(255, 255, 255, 0.3);
        }
      }
      
      // 简化的脉冲光环
      .pulse-rings {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        
        .pulse-ring {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          width: 4rem;
          height: 4rem;
          border: 1px solid rgba(59, 130, 246, 0.3);
          border-radius: 50%;
          animation: pulse-ring 2s ease-out infinite;
          
          &.ring-1 {
            animation-delay: 0s;
          }
          
          &.ring-2 {
            animation-delay: 1s;
          }
        }
      }
    }
    
    .text-section {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 1rem;
      
      .empty-player-name {
        font-size: 1rem;
        color: rgba(255, 255, 255, 0.7);
        font-weight: 500;
      }
    }
  }
}

// 🎨 对战开始后的完整布局
.battle-layout {
  display: flex;
  flex-direction: column;
  height: 100%;
  
  .player-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;

    &.player-header-center {
      justify-content: center;
      text-align: center;
    }
  }

  .player-avatar-container {
    position: relative;
    flex-shrink: 0;
  }

  .player-info-center {
    flex: 1;
    min-width: 0;
  }
}

// 空位头像
.empty-avatar {
  width: 4rem;
  height: 4rem;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.05);
  border: 2px dashed rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  position: relative;
  
  &:hover {
    background: rgba(255, 255, 255, 0.08);
    border-color: rgba(255, 255, 255, 0.3);
  }
}

// 空位玩家名称
.empty-player-name {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.6);
  margin-bottom: 0.5rem;
  text-align: center;
}

// 现代化空位轮次网格
.empty-round-slot-modern {
  opacity: 0.7;
  transition: all 0.3s ease;
  
  &:hover {
    opacity: 0.9;
  }
}

// 现代化空位物品槽
.empty-item-slot-modern {
  position: relative;
  width: 100%;
  height: 4rem;
  background: rgba(255, 255, 255, 0.02);
  border: 1px dashed rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-md, 0.5rem);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  overflow: hidden;

  &:hover {
    background: rgba(255, 255, 255, 0.05);
    border-color: rgba(255, 255, 255, 0.2);
    transform: scale(1.02);
  }
  
  // 槽位发光效果
  .slot-glow {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at center, rgba(59, 130, 246, 0.1) 0%, transparent 70%);
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
  }
  
  &:hover .slot-glow {
    opacity: 1;
  }
}

// 现代化加入按钮
.join-battle-btn-modern {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.25rem;
  background: linear-gradient(135deg, var(--color-primary, #3b82f6), var(--color-primary-dark, #2563eb));
  color: white;
  border: none;
  border-radius: var(--radius-md, 0.5rem);
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
  overflow: hidden;
  min-width: 140px;
  
  .btn-content {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    z-index: 2;
    position: relative;
  }
  
  .btn-glow {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at center, rgba(255, 255, 255, 0.2) 0%, transparent 70%);
    opacity: 0;
    transition: opacity 0.3s ease;
  }
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
    
    .btn-glow {
      opacity: 1;
    }
  }
  
  &:active {
    transform: translateY(0);
  }
  
  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.2),
      transparent
    );
    transition: left 0.5s ease;
    z-index: 1;
  }
  
  &:hover::before {
    left: 100%;
  }
}

// 现代化空位消息
.empty-slot-message-modern {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-md, 0.5rem);
  backdrop-filter: blur(5px);
  transition: all 0.3s ease;
  
  &:hover {
    background: rgba(255, 255, 255, 0.08);
    border-color: rgba(255, 255, 255, 0.2);
  }
}

// 传统加入按钮（保留向后兼容）
.join-battle-btn {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  padding: 0.5rem 0.875rem;
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  border: none;
  border-radius: var(--radius-md, 0.5rem);
  font-size: 0.8rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
  position: relative;
  overflow: hidden;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.2),
      transparent
    );
    transition: left 0.5s ease;
  }

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);

    &::before {
      left: 100%;
    }
  }

  &:active {
    transform: translateY(0);
  }
}

// 轮次区域样式
.rounds-section {
  margin-top: 1rem;
}

.rounds-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: var(--radius-md);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.rounds-title {
  font-size: 0.875rem;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.8);
  flex: 1;
}

.rounds-grid {
  display: grid;
  gap: 0.5rem;
  grid-template-columns: repeat(auto-fit, minmax(3rem, 1fr));

  &[data-players="2"] {
    grid-template-columns: repeat(3, 1fr);
  }

  &[data-players="3"] {
    grid-template-columns: repeat(3, 1fr);
  }

  &[data-players="4"] {
    grid-template-columns: repeat(4, 1fr);
  }
}

.round-slot {
  aspect-ratio: 1;
  border-radius: var(--radius-md);
  border: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(255, 255, 255, 0.02);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  overflow: hidden;
  position: relative;

  &:hover {
    border-color: rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.05);
  }

  &.empty-round-slot {
    background: rgba(255, 255, 255, 0.02);
    border-style: dashed;
  }
}

// 动画定义
@keyframes pulse-ring {
  0% {
    width: 4rem;
    height: 4rem;
    opacity: 1;
  }
  100% {
    width: 6rem;
    height: 6rem;
    opacity: 0;
  }
}

// 🎨 等待状态下的发光动画
@keyframes waiting-glow {
  0%, 100% {
    opacity: 0;
    transform: translateX(-100%);
  }
  50% {
    opacity: 1;
    transform: translateX(100%);
  }
} 