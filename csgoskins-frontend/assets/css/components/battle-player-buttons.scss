// 玩家按钮容器
.player-buttons {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

// 操作按钮基础样式
.start-battle-btn,
.view-result-btn {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  padding: 0.5rem 0.875rem;
  border: none;
  border-radius: var(--radius-md, 0.5rem);
  font-size: 0.8rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  position: relative;
  overflow: hidden;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.2),
      transparent
    );
    transition: left 0.5s ease;
  }

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);

    &::before {
      left: 100%;
    }
  }

  &:active {
    transform: translateY(0);
  }
}

// 开始对战按钮
.start-battle-btn {
  background: linear-gradient(135deg, #ff6b35, #f7931e);
  color: white;
  box-shadow: 0 2px 8px rgba(255, 107, 53, 0.3);

  &:hover {
    box-shadow: 0 4px 12px rgba(255, 107, 53, 0.4);
  }
}

// 查看结果按钮
.view-result-btn {
  background: linear-gradient(135deg, #8e44ad, #9b59b6);
  color: white;
  box-shadow: 0 2px 8px rgba(142, 68, 173, 0.3);

  &:hover {
    box-shadow: 0 4px 12px rgba(142, 68, 173, 0.4);
  }
} 