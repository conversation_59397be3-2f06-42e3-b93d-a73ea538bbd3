@use "~/assets/css/theme-variables.scss" as *;

// 玩家卡片基础样式
.player-card {
  position: relative;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.02) 100%);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(147, 51, 234, 0.05) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
  }

  &:hover::before {
    opacity: 1;
  }

  &.is-host {
    border-color: rgba(251, 191, 36, 0.3);
    background: linear-gradient(135deg, rgba(251, 191, 36, 0.05) 0%, rgba(255, 255, 255, 0.02) 100%);

    &::before {
      background: linear-gradient(135deg, rgba(251, 191, 36, 0.1) 0%, rgba(147, 51, 234, 0.05) 100%);
    }
  }

  &.is-winner {
    border-color: rgba(34, 197, 94, 0.3);
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.05) 0%, rgba(255, 255, 255, 0.02) 100%);

    &::before {
      background: linear-gradient(135deg, rgba(34, 197, 94, 0.1) 0%, rgba(147, 51, 234, 0.05) 100%);
    }
  }

  &.is-opening {
    border-color: rgba(59, 130, 246, 0.3);
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(255, 255, 255, 0.02) 100%);

    &::before {
      background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(147, 51, 234, 0.05) 100%);
    }
  }

  // 🎨 等待状态下的特殊样式
  &.waiting-state {
    min-height: 280px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 2rem 1.5rem;
    
    // 等待状态下的背景动效
    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.05), transparent);
      opacity: 0;
      animation: waiting-glow 3s ease-in-out infinite;
      pointer-events: none;
    }
    
    // 等待状态下的头像容器
    .player-avatar-container {
      margin-bottom: 1.5rem;
    }
    
    // 等待状态下的信息区域
    .player-info-center {
      text-align: center;
      
      .player-name {
        font-size: 1.125rem;
        margin-bottom: 1rem;
        color: rgba(255, 255, 255, 0.9);
      }
    }
  }
}

// 🎨 等待状态下的紧凑布局
.waiting-layout {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  
  .compact-header {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1.5rem;
    text-align: center;
    
    .avatar-section {
      position: relative;
      
      .player-avatar {
        width: 4rem;
        height: 4rem;
        border-radius: 50%;
        object-fit: cover;
        border: 2px solid rgba(255, 255, 255, 0.1);
        transition: all 0.3s ease;
      }
      
      .player-badges {
        position: absolute;
        top: -0.25rem;
        right: -0.25rem;
        display: flex;
        gap: 0.25rem;
      }

      .host-badge,
      .winner-badge,
      .opening-badge {
        width: 1.5rem;
        height: 1.5rem;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 2px solid rgba(255, 255, 255, 0.9);
      }

      .host-badge {
        background: linear-gradient(135deg, #fbbf24, #f59e0b);
        color: #1f2937;
      }

      .winner-badge {
        background: linear-gradient(135deg, #22c55e, #16a34a);
        color: white;
      }

      .opening-badge {
        background: linear-gradient(135deg, #3b82f6, #2563eb);
        color: white;
        animation: pulse 2s infinite;
      }

      .player-actions {
        position: absolute;
        bottom: -0.25rem;
        right: -0.25rem;
        display: flex;
        gap: 0.25rem;
      }

      .action-btn {
        width: 1.75rem;
        height: 1.75rem;
        border-radius: 50%;
        border: none;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.2s ease;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);

        &:hover {
          transform: scale(1.1);
        }

        &.start-btn {
          background: linear-gradient(135deg, #22c55e, #16a34a);
          color: white;

          &:hover {
            background: linear-gradient(135deg, #16a34a, #15803d);
          }
        }

        &.leave-btn {
          background: linear-gradient(135deg, #ef4444, #dc2626);
          color: white;

          &:hover {
            background: linear-gradient(135deg, #dc2626, #b91c1c);
          }
        }

        &.dismiss-btn {
          background: linear-gradient(135deg, #f97316, #ea580c);
          color: white;

          &:hover {
            background: linear-gradient(135deg, #ea580c, #c2410c);
          }
        }

        &.view-btn {
          background: linear-gradient(135deg, #3b82f6, #2563eb);
          color: white;

          &:hover {
            background: linear-gradient(135deg, #2563eb, #1d4ed8);
          }
        }
      }
    }
    
    .text-section {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 0.75rem;
      
      .player-name {
        font-size: 1.125rem;
        font-weight: 600;
        color: rgba(255, 255, 255, 0.9);
        margin: 0;
      }
      
      .player-status-tags {
        display: flex;
        gap: 0.5rem;
        
        .status-tag {
          padding: 0.25rem 0.5rem;
          border-radius: var(--radius-sm, 0.25rem);
          font-size: 0.75rem;
          font-weight: 500;
          border: 1px solid;
          
          &.host-tag {
            background: rgba(251, 191, 36, 0.1);
            color: #fbbf24;
            border-color: rgba(251, 191, 36, 0.3);
          }
          
          &.joined-tag {
            background: rgba(34, 197, 94, 0.1);
            color: #22c55e;
            border-color: rgba(34, 197, 94, 0.3);
          }
        }
      }
    }
  }
}

// 🎨 对战进行中的完整布局
.battle-layout {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.player-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;

  &.player-header-center {
    justify-content: center;
    text-align: center;
  }
}

.player-avatar-container {
  position: relative;
  flex-shrink: 0;
}

.player-avatar {
  width: 4rem;
  height: 4rem;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.player-badges {
  position: absolute;
  top: -0.25rem;
  right: -0.25rem;
  display: flex;
  gap: 0.25rem;
}

.host-badge,
.winner-badge,
.opening-badge {
  width: 1.5rem;
  height: 1.5rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid rgba(255, 255, 255, 0.9);
}

.host-badge {
  background: linear-gradient(135deg, #fbbf24, #f59e0b);
  color: #1f2937;
}

.winner-badge {
  background: linear-gradient(135deg, #22c55e, #16a34a);
  color: white;
}

.opening-badge {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  color: white;
  animation: pulse 2s infinite;
}

.player-actions {
  position: absolute;
  bottom: -0.25rem;
  right: -0.25rem;
  display: flex;
  gap: 0.25rem;
}

.action-btn {
  width: 1.75rem;
  height: 1.75rem;
  border-radius: 50%;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);

  &:hover {
    transform: scale(1.1);
  }

  &.start-btn {
    background: linear-gradient(135deg, #22c55e, #16a34a);
    color: white;

    &:hover {
      background: linear-gradient(135deg, #16a34a, #15803d);
    }
  }

  &.leave-btn {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;

    &:hover {
      background: linear-gradient(135deg, #dc2626, #b91c1c);
    }
  }

  &.dismiss-btn {
    background: linear-gradient(135deg, #f97316, #ea580c);
    color: white;

    &:hover {
      background: linear-gradient(135deg, #ea580c, #c2410c);
    }
  }

  &.view-btn {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
    color: white;

    &:hover {
      background: linear-gradient(135deg, #2563eb, #1d4ed8);
    }
  }
}

.player-info-center {
  flex: 1;
  min-width: 0;
}

.player-name {
  font-size: 1rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 0.25rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.empty-player-name {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.6);
  margin-bottom: 0.5rem;
}

.join-battle-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  color: white;
  border: none;
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  backdrop-filter: blur(10px);

  &:hover {
    background: linear-gradient(135deg, #2563eb, #1d4ed8);
    transform: translateY(-1px);
  }

  &:active {
    transform: translateY(0);
  }
}

.empty-slot-message {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.75rem;
}

.rounds-section {
  margin-top: 1rem;
}

.rounds-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: var(--radius-md);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.rounds-title {
  font-size: 0.875rem;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.8);
  flex: 1;
}

.rounds-value {
  font-size: 0.875rem;
  font-weight: 600;
  color: #22c55e;
}

.rounds-grid {
  display: grid;
  gap: 0.5rem;
  grid-template-columns: repeat(auto-fit, minmax(3rem, 1fr));

  &[data-players="2"] {
    grid-template-columns: repeat(3, 1fr);
  }

  &[data-players="3"] {
    grid-template-columns: repeat(3, 1fr);
  }

  &[data-players="4"] {
    grid-template-columns: repeat(4, 1fr);
  }
}

.round-slot {
  aspect-ratio: 1;
  border-radius: var(--radius-md);
  border: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(255, 255, 255, 0.02);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  overflow: hidden;
  position: relative;

  &:hover {
    border-color: rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.05);
  }

  &.empty-round-slot {
    background: rgba(255, 255, 255, 0.02);
    border-style: dashed;
  }
}

.empty-item-slot {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  color: rgba(255, 255, 255, 0.3);
}

.animation-section {
  margin-top: 1rem;
  min-height: 8rem;
  border-radius: var(--radius-md);
  border: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(255, 255, 255, 0.02);
  overflow: hidden;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .player-card {
    padding: 1rem;
  }

  .player-avatar {
    width: 3rem;
    height: 3rem;
  }

  .player-name {
    font-size: 0.875rem;
  }

  .rounds-grid {
    grid-template-columns: repeat(auto-fit, minmax(2.5rem, 1fr));
  }
}

// 🎨 等待状态下的发光动画
@keyframes waiting-glow {
  0%, 100% {
    opacity: 0;
    transform: translateX(-100%);
  }
  50% {
    opacity: 1;
    transform: translateX(100%);
  }
} 