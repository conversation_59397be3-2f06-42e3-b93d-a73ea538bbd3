// 获胜物品区域
.win-items-section {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

// 获胜物品头部
.win-items-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
  font-size: 0.875rem;
  color: var(--color-gray-300, #d1d5db);
}

.win-items-title {
  font-weight: 600;
  flex: 1;
}

.win-items-value {
  font-weight: 700;
  color: var(--color-yellow-400, #fbbf24);
}

// 获胜物品网格
.win-items-grid {
  display: grid;
  gap: 0.5rem;
  margin-top: 1rem;

  // 响应式网格
  &[data-players="2"] {
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  }

  &[data-players="3"] {
    grid-template-columns: repeat(auto-fit, minmax(90px, 1fr));
  }

  &[data-players="4"] {
    grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
  }

  // 桌面端固定列数
  @media (min-width: 768px) {
    &[data-players="2"] {
      grid-template-columns: repeat(3, 1fr);
    }

    &[data-players="3"] {
      grid-template-columns: repeat(3, 1fr);
    }

    &[data-players="4"] {
      grid-template-columns: repeat(3, 1fr);
    }
  }
}

// 获胜物品包装器
.win-item-wrapper {
  position: relative;
  transition: all 0.3s ease;

  &:hover {
    transform: scale(1.05);
    z-index: 10;
  }
}

// 获胜皮肤卡片
.win-skin-card {
  width: 100%;
  height: 5rem;
  border-radius: var(--radius-md, 0.5rem);
  overflow: hidden;
  border: 2px solid rgba(255, 215, 0, 0.3);
  background: rgba(255, 215, 0, 0.05);
  transition: all 0.3s ease;

  &:hover {
    border-color: rgba(255, 215, 0, 0.6);
    box-shadow: 0 0 15px rgba(255, 215, 0, 0.3);
  }
} 