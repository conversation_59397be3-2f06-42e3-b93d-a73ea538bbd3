// 字体文件 - 遵循 Vue3 + Nuxt3 + TypeScript 开发规范
// 国际化字体配置：默认简体中文，支持英文切换
// 桌面端优先设计，移动端适配

// =================== 
// 自定义字体定义
// ===================

// 英文字体族 - 科技感电竞风格
@font-face {
  font-family: 'Arame';
  src: url('/fonts/Arame-Regular.otf') format('opentype');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Paracaps';
  src: url('/fonts/paracaps-bold.otf') format('opentype');
  font-weight: bold;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Trivial';
  src: url('/fonts/Trivial-Bold.otf') format('opentype');
  font-weight: bold;
  font-style: normal;
  font-display: swap;
}

// 简体中文字体族 - 阿里巴巴普惠体
@font-face {
  font-family: 'AlibabaPuHuiTi';
  src: url('/fonts/AlibabaPuHuiTi-3-35-Thin.woff2') format('woff2'),
       url('/fonts/AlibabaPuHuiTi-3-35-Thin.woff') format('woff'),
       url('/fonts/AlibabaPuHuiTi-3-35-Thin.otf') format('opentype'),
       url('/fonts/AlibabaPuHuiTi-3-35-Thin.ttf') format('truetype'),
       url('/fonts/AlibabaPuHuiTi-3-35-Thin.eot') format('embedded-opentype');
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'AlibabaPuHuiTi';
  src: url('/fonts/AlibabaPuHuiTi-3-85-Bold.woff2') format('woff2'),
       url('/fonts/AlibabaPuHuiTi-3-85-Bold.woff') format('woff'),
       url('/fonts/AlibabaPuHuiTi-3-85-Bold.otf') format('opentype'),
       url('/fonts/AlibabaPuHuiTi-3-85-Bold.ttf') format('truetype'),
       url('/fonts/AlibabaPuHuiTi-3-85-Bold.eot') format('embedded-opentype');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

// ===================
// 国际化字体变量系统 - 默认简体中文
// ===================

:root {
  // Logo 字体 - 所有语言统一使用 Trivial
  --font-logo: 'Trivial', 'AlibabaPuHuiTi', sans-serif;
}

// 简体中文状态下的字体配置（默认）
:root,
:root[lang="zh-hans"] {
  --font-primary: 'AlibabaPuHuiTi', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  --font-title: 'AlibabaPuHuiTi', -apple-system, BlinkMacSystemFont, sans-serif;
  --font-accent: 'AlibabaPuHuiTi', sans-serif;
  
  body {
    font-family: var(--font-primary);
    font-weight: 300;
  }
  
  h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-title);
    font-weight: 700;
  }
}

// 英文状态下的字体配置
:root[lang="en"] {
  --font-primary: 'Arame', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  --font-title: 'Trivial', -apple-system, BlinkMacSystemFont, sans-serif;
  --font-accent: 'Paracaps', sans-serif;
  
  body {
    font-family: var(--font-primary);
    font-weight: normal;
  }
  
  h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-title);
    font-weight: bold;
  }
}

// ===================
// 通用国际化字体工具类
// ===================

.font-i18n {
  font-family: var(--font-primary);
}

.title-i18n {
  font-family: var(--font-title);
  font-weight: 700;  
}

.accent-i18n {
  font-family: var(--font-accent);
}

.font-logo {
  font-family: var(--font-logo);
  font-weight: bold;
}

// ===================
// 特定字体工具类 - 用于特殊场景
// ===================

// 英文字体工具类
.font-arame {
  font-family: 'Arame', sans-serif;
}

.font-paracaps {
  font-family: 'Paracaps', sans-serif;
  font-weight: bold;
}

.font-trivial {
  font-family: 'Trivial', sans-serif;
  font-weight: bold;
}

// 中文字体工具类
.font-alibaba {
  font-family: 'AlibabaPuHuiTi', sans-serif;
}

.font-alibaba-thin {
  font-family: 'AlibabaPuHuiTi', sans-serif;
  font-weight: 300;
}

.font-alibaba-bold {
  font-family: 'AlibabaPuHuiTi', sans-serif;
  font-weight: 700;
}

// ===================
// 标题样式工具类 - 带字间距和大小写
// ===================

.title-arame {
  font-family: 'Arame', sans-serif;
  letter-spacing: 0.05em;
  text-transform: uppercase;
}

.title-paracaps {
  font-family: 'Paracaps', sans-serif;
  font-weight: bold;
  letter-spacing: 0.03em;
  text-transform: uppercase;
}

.title-trivial {
  font-family: 'Trivial', sans-serif;
  font-weight: bold;
  letter-spacing: 0.02em;
  text-transform: uppercase;
}

.title-alibaba {
  font-family: 'AlibabaPuHuiTi', sans-serif;
  font-weight: 700;
  letter-spacing: 0.01em;
}

// ===================
// 响应式字体大小 - 桌面端优先
// ===================

// 桌面端字体大小（默认）
.text-display {
  font-size: 3.75rem; // 60px
  line-height: 1.1;
  font-weight: 700;
}

.text-hero {
  font-size: 3rem; // 48px
  line-height: 1.2;
  font-weight: 600;
}

.text-title {
  font-size: 2.25rem; // 36px
  line-height: 1.3;
  font-weight: 600;
}

.text-subtitle {
  font-size: 1.5rem; // 24px
  line-height: 1.4;
  font-weight: 500;
}

.text-body {
  font-size: 1rem; // 16px
  line-height: 1.6;
  font-weight: 400;
}

.text-caption {
  font-size: 0.875rem; // 14px
  line-height: 1.5;
  font-weight: 400;
}

// 移动端字体大小适配
@media (max-width: 768px) {
  .text-display {
    font-size: 2.5rem; // 40px
  }
  
  .text-hero {
    font-size: 2rem; // 32px
  }
  
  .text-title {
    font-size: 1.75rem; // 28px
  }
  
  .text-subtitle {
    font-size: 1.25rem; // 20px
  }
  
  .text-body {
    font-size: 0.95rem; // 15px
  }
  
  .text-caption {
    font-size: 0.8rem; // 13px
  }
}

// ===================
// 字体性能优化
// ===================

// 预加载关键字体
.font-preload {
  font-display: swap;
}

// 避免布局偏移
.font-stable {
  font-display: optional;
}
