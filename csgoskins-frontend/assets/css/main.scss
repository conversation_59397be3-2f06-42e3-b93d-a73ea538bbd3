// 主样式文件 - 遵循 Vue3 + Nuxt3 + TypeScript 开发规范
// 使用现代 Sass @use 语法，避免弃用警告

// ===================
// 1. 主题变量 - 首先导入，供其他样式使用
// ===================
@use './theme-variables.scss';

// ===================
// 2. 字体定义 - 自定义字体和多语言支持
// ===================
@use './fonts.scss';

// ===================
// 3. Tailwind CSS 基础样式
// ===================
@use 'tailwindcss/base';

// ===================
// 4. 组件样式 - 在 Tailwind components 之前
// ===================
@use './component-styles.scss';

// ===================
// 5. 动画样式
// ===================
@use './animations.scss';

// ===================
// 6. Tailwind CSS 组件
// ===================
@use 'tailwindcss/components';

// ===================
// 7. Tailwind CSS 工具类 - 最后导入，确保最高优先级
// ===================
@use 'tailwindcss/utilities';

// ===================
// 全局基础样式
// ===================

html {
  font-family: var(--font-primary, 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif);
  color-scheme: dark;
  font-size: 16px;
}

body {
  margin: 0;
  padding: 0;
  background: var(--color-background, linear-gradient(135deg, #1a202c 0%, #2d3748 100%));
  min-height: 100vh;
  color: var(--color-text);
  font-family: var(--font-primary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// ===================
// 认证页面特殊样式
// ===================
.auth-page {
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.9) 0%, rgba(15, 23, 42, 0.9) 100%);
  backdrop-filter: blur(10px);
  min-height: 100vh;
  
  .auth-form {
    background: rgba(31, 41, 55, 0.4);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(107, 114, 128, 0.3);
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  }
}

// ===================
// 滚动条样式 - 全局优化
// ===================
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--color-background-secondary, rgba(31, 41, 55, 0.4));
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: var(--color-primary-30, rgba(0, 168, 255, 0.3));
  border-radius: 4px;
  
  &:hover {
    background: var(--color-primary-50, rgba(0, 168, 255, 0.5));
  }
}

// ===================
// 通用工具类
// ===================
.glass-effect {
  background: rgba(31, 41, 55, 0.4);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(107, 114, 128, 0.3);
}

.glow-effect {
  box-shadow: 0 0 20px var(--color-primary-30, rgba(0, 168, 255, 0.3));
}

.text-gradient {
  background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

// ===================
// 表单样式 - 全局优化
// ===================
input, textarea, select {
  background: rgba(55, 65, 81, 0.5);
  border: 1px solid rgba(107, 114, 128, 0.5);
  color: white;
  
  &:focus {
    outline: none;
    border-color: rgba(59, 130, 246, 0.5);
    box-shadow: 0 0 0 1px rgba(59, 130, 246, 0.5);
  }
  
  &::placeholder {
    color: rgba(156, 163, 175, 1);
  }
}

label {
  color: rgba(255, 255, 255, 0.8);
}

// ===================
// 全局样式结束
// ===================