// 主题变量文件 - 遵循 Vue3 + Nuxt3 + TypeScript 开发规范
// CSGO 开箱网站主题色彩系统 (支持 Tailwind CSS + UnoCSS)

:root {
  // ===================
  // 主题色 - 科技感蓝色主调
  // ===================
  --color-primary: #00A8FF;
  --color-primary-dark: #0076B3;
  --color-primary-light: #33BBFF;
  --color-primary-5: rgba(0, 168, 255, 0.05);
  --color-primary-10: rgba(0, 168, 255, 0.1);
  --color-primary-20: rgba(0, 168, 255, 0.2);
  --color-primary-30: rgba(0, 168, 255, 0.3);
  --color-primary-50: rgba(0, 168, 255, 0.5);
  --color-primary-80: rgba(0, 168, 255, 0.8);
  --color-primary-90: rgba(0, 168, 255, 0.9);
  
  // Tailwind/UnoCSS 色阶映射
  --color-primary-50: #E6F7FF;
  --color-primary-100: #BAE7FF;
  --color-primary-200: #91D5FF;
  --color-primary-300: #69C0FF;
  --color-primary-400: #40A9FF;
  --color-primary-500: #00A8FF;
  --color-primary-600: #0086CC;
  --color-primary-700: #0066A3;
  --color-primary-800: #004C7A;
  --color-primary-900: #003252;
  --color-primary-950: #001A29;
  
  // ===================
  // 辅助色 - 橙色强调
  // ===================
  --color-secondary: #FF4D00;
  --color-secondary-dark: #CC3D00;
  --color-secondary-light: #FF7133;
  
  --color-secondary-50: #FFF3E0;
  --color-secondary-100: #FFE0B2;
  --color-secondary-200: #FFCC80;
  --color-secondary-300: #FFB74D;
  --color-secondary-400: #FF7133;
  --color-secondary-500: #FF4D00;
  --color-secondary-600: #CC3D00;
  --color-secondary-700: #993000;
  --color-secondary-800: #662000;
  --color-secondary-900: #331000;
  --color-secondary-950: #1A0800;
  
  // ===================
  // 背景色系统 - 深色调
  // ===================
  --color-background: #0D0F12;
  --color-background-lighter: #171C21;
  --color-background-darker: #080A0C;
  --color-background-dark: #12171C;
  
  // 灰度色阶
  --color-gray-50: #F8F9FA;
  --color-gray-100: #F1F3F5;
  --color-gray-200: #E9ECEF;
  --color-gray-300: #DEE2E6;
  --color-gray-400: #CED4DA;
  --color-gray-500: #ADB5BD;
  --color-gray-600: #6C757D;
  --color-gray-700: #495057;
  --color-gray-800: #343A40;
  --color-gray-850: #1E2329;
  --color-gray-900: #171C21;
  --color-gray-950: #0D0F12;
  
  // ===================
  // 文字颜色
  // ===================
  --color-text: #ffffff;
  --color-text-secondary: rgba(255, 255, 255, 0.8);
  --color-text-muted: rgba(255, 255, 255, 0.6);
  --color-text-hint: rgba(255, 255, 255, 0.4);
  
  // ===================
  // 边框颜色
  // ===================
  --color-border: rgba(255, 255, 255, 0.1);
  --color-border-hover: rgba(255, 255, 255, 0.2);
  --color-border-focus: rgba(0, 168, 255, 0.5);
  
  // ===================
  // 状态颜色
  // ===================
  --color-success: #4CAF50;
  --color-success-foreground: #ffffff;
  --color-warning: #FFC107;
  --color-warning-foreground: #000000;
  --color-danger: #F44336;
  --color-danger-foreground: #ffffff;
  
  // ===================
  // CSGO 稀有度颜色系统
  // ===================
  --rarity-common: #B0C3D9;
  --rarity-uncommon: #5E98D9;
  --rarity-rare: #4B69FF;
  --rarity-mythical: #8847FF;
  --rarity-legendary: #D32CE6;
  --rarity-ancient: #EB4B4B;
  --rarity-immortal: #E4AE33;
  --rarity-arcana: #ADE55C;
  
  // 稀有度发光效果
  --glow-common: 0 0 0.3125rem rgba(176, 195, 217, 0.7);
  --glow-uncommon: 0 0 0.3125rem rgba(94, 152, 217, 0.7);
  --glow-rare: 0 0 0.3125rem rgba(75, 105, 255, 0.7);
  --glow-mythical: 0 0 0.3125rem rgba(136, 71, 255, 0.7);
  --glow-legendary: 0 0 0.3125rem rgba(211, 44, 230, 0.7);
  --glow-ancient: 0 0 0.3125rem rgba(235, 75, 75, 0.7);
  --glow-immortal: 0 0 0.3125rem rgba(228, 174, 51, 0.7);
  --glow-arcana: 0 0 0.3125rem rgba(173, 229, 92, 0.7);
  
  // ===================
  // 阴影和特效
  // ===================
  --shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.2), 0 0 0.125rem rgba(0, 168, 255, 0.1);
  --shadow-md: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.25), 0 0 0.25rem rgba(0, 168, 255, 0.15);
  --shadow-lg: 0 0.5rem 1rem rgba(0, 0, 0, 0.3), 0 0 0.5rem rgba(0, 168, 255, 0.2);
  --shadow-xl: 0 1rem 2rem rgba(0, 0, 0, 0.35), 0 0 1rem rgba(0, 168, 255, 0.25);
  
  // 霓虹效果
  --neon-primary: 0 0 0.3125rem rgba(0, 168, 255, 0.5), 0 0 0.625rem rgba(0, 168, 255, 0.3);
  --neon-secondary: 0 0 0.3125rem rgba(255, 77, 0, 0.5), 0 0 0.625rem rgba(255, 77, 0, 0.3);
  --neon-rare: 0 0 0.3125rem rgba(255, 215, 0, 0.5), 0 0 0.625rem rgba(255, 215, 0, 0.3);
  
  // ===================
  // 渐变背景
  // ===================
  --gradient-primary: linear-gradient(135deg, #00A8FF, #0056C8);
  --gradient-secondary: linear-gradient(135deg, #FF4D00, #CC0000);
  --gradient-case: linear-gradient(135deg, #171C21, #0D0F12);
  --gradient-rare: linear-gradient(135deg, #FFD700, #FFA500);
  --gradient-legendary: linear-gradient(135deg, #9C27B0, #673AB7);
  --gradient-dark: linear-gradient(135deg, #12171C, #0A0C0F);
  --gradient-highlight: linear-gradient(135deg, #00A8FF, #0066A3);
  
  // ===================
  // 设计系统 - 圆角
  // ===================
  --radius-xs: 0.125rem;
  --radius-sm: 0.25rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-xxl: 1.5rem;
  --radius-full: 9999px;
  
  // ===================
  // 设计系统 - 间距
  // ===================
  --spacing-xxs: 0.25rem;
  --spacing-xs: 0.5rem;
  --spacing-sm: 0.75rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-xxl: 3rem;
  --spacing-3xl: 4rem;
  --spacing-4xl: 6rem;
  
  // ===================
  // 动画和过渡
  // ===================
  --transition-fast: 0.15s;
  --transition-normal: 0.3s;
  --transition-slow: 0.5s;
  
  --animation-fast: 0.3s;
  --animation-normal: 0.6s;
  --animation-slow: 1s;
  --animation-very-slow: 2s;
  
  // 动画曲线
  --ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
  --ease-out: cubic-bezier(0.33, 1, 0.68, 1);
  --ease-in: cubic-bezier(0.32, 0, 0.67, 0);
  --ease-in-out: cubic-bezier(0.65, 0, 0.35, 1);
  
  // ===================
  // 响应式断点
  // ===================
  --breakpoint-xs: 30rem;
  --breakpoint-sm: 40rem;
  --breakpoint-md: 48rem;
  --breakpoint-lg: 64rem;
  --breakpoint-xl: 80rem;
  --breakpoint-xxl: 96rem;
  
  // ===================
  // ECharts 图表色彩
  // ===================
  --chart-1: 200 100% 50%;
  --chart-2: 16 100% 50%;
  --chart-3: 122 39% 49%;
  --chart-4: 45 100% 51%;
  --chart-5: 276 91% 56%;
}

// 组件样式变量
// ===================
