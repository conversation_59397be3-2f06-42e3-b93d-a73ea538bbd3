<template>
  <div class="auth-form p-6 rounded-xl shadow-lg w-full max-w-md mx-auto bg-gray-800/40 backdrop-blur-md border border-gray-700/30">
    <h1 class="text-2xl font-bold mb-6 text-center text-transparent bg-clip-text bg-gradient-to-r from-orange-400 to-red-400">
      {{ $t('auth.forgot_password') }}
    </h1>

    <!-- 步骤指示器 -->
    <div class="flex items-center justify-center mb-6">
      <div 
        v-for="(stepName, index) in steps" 
        :key="index"
        class="flex items-center"
      >
        <div 
          class="w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium transition-all duration-300"
          :class="[
            index < currentStep 
              ? 'bg-green-500 text-white' 
              : index === currentStep 
                ? 'bg-orange-500 text-white' 
                : 'bg-gray-700 text-gray-400'
          ]"
        >
          <div v-if="index < currentStep" class="i-ph-check w-4 h-4"></div>
          <span v-else>{{ index + 1 }}</span>
        </div>
        <div 
          v-if="index < steps.length - 1"
          class="w-12 h-0.5 mx-2 transition-colors duration-300"
          :class="index < currentStep ? 'bg-green-500' : 'bg-gray-700'"
        ></div>
      </div>
    </div>

    <!-- 步骤1: 输入邮箱 -->
    <form v-if="currentStep === 0" @submit.prevent="sendResetCode" class="space-y-4">
      <div class="text-center text-sm text-white/70 mb-4">
        {{ $t('auth.verify_email') }}
      </div>
      
      <div class="form-group">
        <label for="email" class="block mb-1.5 text-sm font-medium text-white/80">
          {{ $t('auth.email') }}
        </label>
        <div class="flex items-stretch">
          <div class="relative flex-1">
            <div class="i-ph-envelope-simple absolute left-3 top-1/2 transform -translate-y-1/2 text-white/50 w-5 h-5"></div>
            <input 
              type="email" 
              id="email" 
              v-model="formData.email" 
              :class="[
                'w-full pl-10 pr-4 py-2.5 rounded-l-lg bg-gray-700/50 border transition-all duration-300',
                'focus:outline-none focus:ring-1 focus:ring-orange-500/50 focus:border-orange-500/50',
                'placeholder:text-gray-400',
                errors.email ? 'border-red-500' : 'border-gray-700/50'
              ]"
              :placeholder="$t('auth.email_placeholder')"
              @blur="validateField('email')"
              autocomplete="email"
            />
          </div>
          <button 
            type="submit"
            class="px-4 py-2.5 rounded-r-lg bg-gradient-to-r from-orange-500 to-red-500 text-white font-medium transition-all duration-300 hover:opacity-90 disabled:opacity-50 disabled:cursor-not-allowed whitespace-nowrap flex items-center justify-center min-w-[6rem]"
            :disabled="isCodeLoading || !isEmailValid || codeCountdown > 0"
          >
            <div v-if="isCodeLoading" class="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full"></div>
            <span v-else-if="codeCountdown > 0">{{ $t('auth.code_countdown', { seconds: codeCountdown }) }}</span>
            <span v-else>{{ $t('auth.send_code') }}</span>
          </button>
        </div>
        <p v-if="errors.email" class="mt-1 text-sm text-red-400">{{ $t(errors.email) }}</p>
      </div>

      <div v-if="formError" class="p-3 bg-red-500/10 border border-red-500/20 rounded-lg">
        <p class="text-sm text-red-400 text-center">{{ formError }}</p>
      </div>

      <div class="text-center">
        <NuxtLink 
          to="/auth/login" 
          class="text-sm text-orange-400 hover:text-orange-300 transition-colors"
        >
          {{ $t('auth.back_to_login') }}
        </NuxtLink>
      </div>
    </form>

    <!-- 步骤2: 输入验证码 -->
    <form v-if="currentStep === 1" @submit.prevent="verifyCode" class="space-y-4">
      <div class="text-center text-sm text-white/70 mb-4">
        已向 <span class="text-orange-400">{{ formData.email }}</span> 发送验证码
      </div>
      
      <div class="form-group">
        <label for="verifyCode" class="block mb-1.5 text-sm font-medium text-white/80">
          {{ $t('auth.verification_code') }}
        </label>
        <div class="relative">
          <div class="i-ph-shield-check absolute left-3 top-1/2 transform -translate-y-1/2 text-white/50 w-5 h-5"></div>
          <input 
            type="text" 
            id="verifyCode" 
            v-model="formData.verifyCode" 
            :class="[
              'w-full pl-10 pr-4 py-2.5 rounded-lg bg-gray-700/50 border transition-all duration-300',
              'focus:outline-none focus:ring-1 focus:ring-orange-500/50 focus:border-orange-500/50',
              'placeholder:text-gray-400',
              errors.verifyCode ? 'border-red-500' : 'border-gray-700/50'
            ]"
            :placeholder="$t('auth.email_verification_code_placeholder')"
            @blur="validateField('verifyCode')"
            autocomplete="off"
            maxlength="6"
          />
        </div>
        <p v-if="errors.verifyCode" class="mt-1 text-sm text-red-400">{{ $t(errors.verifyCode) }}</p>
      </div>

      <button 
        type="submit" 
        class="w-full bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white font-medium py-2.5 px-4 rounded-lg transition-all duration-300 flex items-center justify-center transform hover:-translate-y-0.5 shadow-md disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
        :disabled="isLoading"
      >
        <div v-if="isLoading" class="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full mr-2"></div>
        {{ $t('auth.verify_proceed') }}
      </button>

      <div v-if="formError" class="p-3 bg-red-500/10 border border-red-500/20 rounded-lg">
        <p class="text-sm text-red-400 text-center">{{ formError }}</p>
      </div>

      <div class="flex justify-between items-center text-sm">
        <button 
          type="button" 
          @click="currentStep = 0"
          class="text-orange-400 hover:text-orange-300 transition-colors"
        >
          {{ $t('auth.back_to_register') }}
        </button>
        <button 
          type="button" 
          @click="resendCode"
          :disabled="codeCountdown > 0"
          class="text-orange-400 hover:text-orange-300 transition-colors disabled:text-gray-500"
        >
          {{ codeCountdown > 0 ? `${codeCountdown}秒后重发` : $t('auth.code_resend') }}
        </button>
      </div>
    </form>

    <!-- 步骤3: 设置新密码 -->
    <form v-if="currentStep === 2" @submit.prevent="resetPassword" class="space-y-4">
      <div class="text-center text-sm text-white/70 mb-4">
        {{ $t('auth.set_new_password') }}
      </div>
      
      <!-- 新密码输入 -->
      <div class="form-group">
        <label for="newPassword" class="block mb-1.5 text-sm font-medium text-white/80">
          {{ $t('auth.new_password') }}
        </label>
        <div class="relative">
          <div class="i-ph-lock-simple absolute left-3 top-1/2 transform -translate-y-1/2 text-white/50 w-5 h-5"></div>
          <input 
            :type="showPassword ? 'text' : 'password'" 
            id="newPassword" 
            v-model="formData.newPassword" 
            :class="[
              'w-full pl-10 pr-12 py-2.5 rounded-lg bg-gray-700/50 border transition-all duration-300',
              'focus:outline-none focus:ring-1 focus:ring-orange-500/50 focus:border-orange-500/50',
              'placeholder:text-gray-400',
              errors.newPassword ? 'border-red-500' : 'border-gray-700/50'
            ]"
            :placeholder="$t('auth.new_password_placeholder')"
            @blur="validateField('newPassword')"
            autocomplete="new-password"
          />
          <button 
            type="button" 
            @click="showPassword = !showPassword"
            class="absolute right-3 top-1/2 transform -translate-y-1/2 text-white/50 hover:text-white/70"
          >
            <div :class="showPassword ? 'i-ph-eye-slash' : 'i-ph-eye'" class="w-5 h-5"></div>
          </button>
        </div>
        <!-- 密码强度指示器 -->
        <div v-if="formData.newPassword" class="mt-2">
          <div class="flex items-center gap-2 text-xs">
            <span class="text-white/60">强度:</span>
            <div class="flex-1 bg-gray-700 rounded-full h-1.5">
              <div 
                class="h-1.5 rounded-full transition-all duration-300"
                :class="{
                  'w-1/3 bg-red-500': passwordStrength.strength === 'weak',
                  'w-2/3 bg-yellow-500': passwordStrength.strength === 'medium',
                  'w-full bg-green-500': passwordStrength.strength === 'strong'
                }"
              ></div>
            </div>
            <span 
              class="text-xs"
              :class="{
                'text-red-400': passwordStrength.strength === 'weak',
                'text-yellow-400': passwordStrength.strength === 'medium',
                'text-green-400': passwordStrength.strength === 'strong'
              }"
            >
              {{ $t(`auth.password_${passwordStrength.strength}`) }}
            </span>
          </div>
        </div>
        <p v-if="errors.newPassword" class="mt-1 text-sm text-red-400">{{ $t(errors.newPassword) }}</p>
      </div>

      <!-- 确认新密码输入 -->
      <div class="form-group">
        <label for="confirmPassword" class="block mb-1.5 text-sm font-medium text-white/80">
          {{ $t('auth.confirm_password') }}
        </label>
        <div class="relative">
          <div class="i-ph-lock-simple absolute left-3 top-1/2 transform -translate-y-1/2 text-white/50 w-5 h-5"></div>
          <input 
            :type="showConfirmPassword ? 'text' : 'password'" 
            id="confirmPassword" 
            v-model="formData.confirmPassword" 
            :class="[
              'w-full pl-10 pr-12 py-2.5 rounded-lg bg-gray-700/50 border transition-all duration-300',
              'focus:outline-none focus:ring-1 focus:ring-orange-500/50 focus:border-orange-500/50',
              'placeholder:text-gray-400',
              errors.confirmPassword ? 'border-red-500' : 'border-gray-700/50'
            ]"
            :placeholder="$t('auth.confirm_password_placeholder')"
            @blur="validateField('confirmPassword')"
            autocomplete="new-password"
          />
          <button 
            type="button" 
            @click="showConfirmPassword = !showConfirmPassword"
            class="absolute right-3 top-1/2 transform -translate-y-1/2 text-white/50 hover:text-white/70"
          >
            <div :class="showConfirmPassword ? 'i-ph-eye-slash' : 'i-ph-eye'" class="w-5 h-5"></div>
          </button>
        </div>
        <p v-if="errors.confirmPassword" class="mt-1 text-sm text-red-400">{{ $t(errors.confirmPassword) }}</p>
      </div>

      <button 
        type="submit" 
        class="w-full bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white font-medium py-2.5 px-4 rounded-lg transition-all duration-300 flex items-center justify-center transform hover:-translate-y-0.5 shadow-md disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
        :disabled="isLoading"
      >
        <div v-if="isLoading" class="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full mr-2"></div>
        {{ isLoading ? $t('auth.resetting') : $t('auth.reset_password') }}
      </button>

      <div v-if="formError" class="p-3 bg-red-500/10 border border-red-500/20 rounded-lg">
        <p class="text-sm text-red-400 text-center">{{ formError }}</p>
      </div>
    </form>

    <!-- 步骤4: 重置成功 -->
    <div v-if="currentStep === 3" class="text-center space-y-4">
      <div class="w-16 h-16 mx-auto bg-green-500/20 rounded-full flex items-center justify-center">
        <div class="i-ph-check-circle text-green-400 w-8 h-8"></div>
      </div>
      <h2 class="text-lg font-semibold text-white">{{ $t('auth.reset_success') }}</h2>
      <p class="text-sm text-white/70">密码重置成功，您可以使用新密码登录了</p>
      
      <button 
        @click="goToLogin"
        class="w-full bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 text-white font-medium py-2.5 px-4 rounded-lg transition-all duration-300 flex items-center justify-center transform hover:-translate-y-0.5 shadow-md"
      >
        {{ $t('auth.back_to_login') }}
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { useAuth } from '~/composables/useAuth'
import { 
  validateEmail, 
  validatePassword, 
  validateConfirmPassword, 
  validateVerificationCode
} from '~/utils/validation'

const { t } = useI18n()
const router = useRouter()

// 使用认证组合函数
const {
  codeCountdown,
  isCodeLoading,
  isLoading,
  sendEmailCode,
  resetPassword: resetPasswordApi
} = useAuth()

// 步骤定义
const steps = ['验证邮箱', '输入验证码', '设置新密码', '完成重置']
const currentStep = ref(0)

// 表单数据
const formData = reactive({
  email: '',
  verifyCode: '',
  newPassword: '',
  confirmPassword: ''
})

// 错误状态
const errors = reactive({
  email: '',
  verifyCode: '',
  newPassword: '',
  confirmPassword: ''
})

// 组件状态
const showPassword = ref(false)
const showConfirmPassword = ref(false)
const formError = ref('')

// 计算属性
const isEmailValid = computed(() => {
  const result = validateEmail(formData.email)
  return result.valid
})

const passwordStrength = computed(() => {
  return validatePassword(formData.newPassword)
})

// 验证字段
const validateField = (field: keyof typeof formData) => {
  switch (field) {
    case 'email':
      const emailResult = validateEmail(formData.email)
      errors.email = emailResult.valid ? '' : emailResult.message || ''
      break
    case 'verifyCode':
      const codeResult = validateVerificationCode(formData.verifyCode)
      errors.verifyCode = codeResult.valid ? '' : codeResult.message || ''
      break
    case 'newPassword':
      const passwordResult = validatePassword(formData.newPassword)
      errors.newPassword = passwordResult.valid ? '' : passwordResult.message || ''
      // 如果确认密码已输入，同时验证确认密码
      if (formData.confirmPassword) {
        validateField('confirmPassword')
      }
      break
    case 'confirmPassword':
      const confirmResult = validateConfirmPassword(formData.newPassword, formData.confirmPassword)
      errors.confirmPassword = confirmResult.valid ? '' : confirmResult.message || ''
      break
  }
}

// 发送重置验证码
const sendResetCode = async () => {
  formError.value = ''
  validateField('email')
  
  if (!isEmailValid.value) {
    return
  }
  
  const success = await sendEmailCode(formData.email)
  if (success) {
    currentStep.value = 1
  } else {
    formError.value = t('auth.code_sent')
  }
}

// 重新发送验证码
const resendCode = async () => {
  await sendResetCode()
}

// 验证验证码
const verifyCode = async () => {
  formError.value = ''
  validateField('verifyCode')
  
  if (!errors.verifyCode) {
    // 验证码正确，进入下一步
    currentStep.value = 2
  } else {
    formError.value = t('auth.code_required')
  }
}

// 重置密码
const resetPassword = async () => {
  formError.value = ''
  
  validateField('newPassword')
  validateField('confirmPassword')
  
  if (errors.newPassword || errors.confirmPassword) {
    return
  }
  
  try {
    const result = await resetPasswordApi({
      email: formData.email,
      code: formData.verifyCode,
      newPassword: formData.newPassword
    })
    
    if (result.success) {
      currentStep.value = 3
    } else {
      formError.value = result.error || t('auth.reset_failed')
    }
  } catch (error: any) {
    formError.value = error.message || t('auth.reset_error')
  }
}

// 跳转到登录页
const goToLogin = () => {
  router.push('/auth/login')
}
</script>

<style scoped>
.auth-form {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}
</style> 