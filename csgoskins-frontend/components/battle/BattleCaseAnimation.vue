<template>
  <div
    class="battle-case-animation relative"
    ref="rootEl"
    :data-player="playerName"
    :data-player-id="playerId"
    :data-player-index="battlePlayerIndex"
    data-battle-animation="true"
  >
    <!-- 主动画区域 -->
    <div
      class="relative bg-gradient-to-br from-gray-800/90 to-gray-900/90 rounded-2xl border border-gray-700/30 backdrop-blur-md overflow-hidden"
    >
      <!-- 背景装饰 -->
      <div class="absolute inset-0 bg-grid-pattern opacity-5"></div>
      <div
        class="absolute -inset-px bg-gradient-to-r from-primary/20 via-transparent to-secondary/20 rounded-2xl"
      ></div>

      <!-- 动画轨道容器 -->
      <div class="relative py-6 px-4">
        <!-- 中奖指示线 -->
        <div class="absolute left-1/2 top-6 w-1 h-48 -translate-x-1/2 z-20">
          <div
            class="h-full bg-gradient-to-b from-transparent via-cyan-400 to-transparent opacity-80"
          ></div>
        </div>

        <!-- 动画轨道 -->
        <div class="relative h-48 overflow-hidden py-4 bg-gray-800/30 rounded-lg border border-gray-600/50">
          <!-- 中奖指示线 -->
          <div class="absolute top-0 left-1/2 transform -translate-x-1/2 w-1 h-full bg-gradient-to-b from-cyan-400 to-blue-500 z-10"></div>
          
          <!-- 数据等待状态 -->
          <div v-if="isWaitingForData" class="flex items-center justify-center h-full">
            <div class="text-center">
              <div class="w-8 h-8 border-2 border-cyan-400 border-t-transparent rounded-full animate-spin mx-auto mb-2"></div>
              <div class="text-cyan-400 text-sm font-medium">{{ t('battle.animation.waiting_for_data') }}</div>
              <div class="text-gray-400 text-xs mt-1">{{ t('battle.animation.preparing_animation') }}</div>
            </div>
          </div>
          
          <!-- 动画轨道内容 -->
          <div
            v-else
            ref="trackContainer"
            class="flex items-center h-full"
            :style="{ transform: 'translateX(0px)' }"
          >
            <!-- 动画物品卡片 -->
            <div
              v-for="(item, index) in displayItems"
              :key="`item-${index}`"
              class="flex-shrink-0 mx-2 relative group"
              :class="[
                'w-28 h-36',
                isWinnerItem(item, index) && !isAnimating ? 'winner-glow' : '',
              ]"
            >
              <!-- 物品卡片 -->
              <div
                class="relative h-full bg-gradient-to-b from-gray-700/80 to-gray-800/80 rounded-lg border-2 overflow-hidden transition-all duration-300 group-hover:scale-105"
                :style="{
                  borderColor:
                    isWinnerItem(item, index) && !isAnimating
                      ? '#00bcd4'
                      : (item.rarity_color || '#6b7280') + '80',
                }"
              >
                <!-- 稀有度指示条 -->
                <div
                  class="absolute bottom-0 left-0 right-0 h-1 opacity-80"
                  :style="{ backgroundColor: item.rarity_color || '#6b7280' }"
                ></div>

                <!-- StatTrak 角标 -->
                <div
                  v-if="isStatTrakItem(item)"
                  class="absolute top-1 left-1 z-10"
                >
                  <div
                    class="bg-secondary/90 rounded-md px-1.5 py-0.5 text-xs font-bold text-white shadow-lg backdrop-blur-sm border border-secondary/50"
                  >
                    <span class="mr-1">⚡</span>ST
                  </div>
                </div>

                <!-- 顶部右侧外观信息 -->
                <div class="absolute top-1 right-1 z-10">
                  <span class="text-gray-400 text-xs">
                    {{ getExteriorName(item) || getCondition(item) }}
                  </span>
                </div>

                <!-- 物品图片 -->
                <div
                  class="relative h-20 flex items-center justify-center pt-6 px-2"
                >
                  <img
                    :src="item.image || '/demo/item1.png'"
                    :alt="getLocalizedItemName(item)"
                    class="max-w-full max-h-full object-contain drop-shadow-lg"
                    @error="handleImageError"
                  />
                </div>

                <!-- 物品信息 -->
                <div class="space-y-1 px-1">
                  <!-- 饰品名称 -->
                  <div
                    class="text-white text-xs font-medium truncate text-center"
                    :title="getLocalizedItemName(item)"
                  >
                    {{ getSkinName(item) }}
                  </div>

                  <!-- 价格和稀有度 -->
                  <div class="flex items-center justify-between text-xs">
                    <div class="text-primary text-xs font-bold">
                      ${{ formatPrice(item.price) }}
                    </div>
                    <span
                      class="font-bold whitespace-nowrap overflow-hidden text-ellipsis pl-1"
                      :style="{ color: item.rarity_color || '#6b7280' }"
                    >
                      {{ getRarityName(item) }}
                    </span>
                  </div>
                </div>

                <!-- 获胜物品特效 -->
                <div
                  v-if="isWinnerItem(item, index) && !isAnimating"
                  class="absolute inset-0 bg-gradient-to-r from-cyan-400/20 to-blue-400/20 rounded-lg animate-pulse"
                ></div>
              </div>
            </div>
          </div>
        </div>

        <!-- 对战状态显示 -->
        <div class="mt-4 text-center">
          <div class="text-sm text-gray-300">
            {{ getBattleStatusText() }}
          </div>
        </div>
      </div>
    </div>

    <!-- 步骤提示覆盖层 -->
    <div
      v-if="showStepHint"
      class="fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center z-50 px-4"
    >
      <div
        class="bg-gray-900/95 rounded-2xl border border-primary/50 shadow-2xl p-6 text-center max-w-sm w-full relative"
      >
        <div class="relative">
          <UiLoadingSpinner type="csgo" size="medium" :show-text="false" />
          <h3 class="text-lg font-bold text-white mt-3 mb-2">
            {{ currentStep.title }}
          </h3>
          <p class="text-gray-300 text-sm">{{ currentStep.description }}</p>
          
          <!-- 进度条 -->
          <div class="w-full bg-gray-700 rounded-full h-1.5 mt-3">
            <div
              class="bg-gradient-to-r from-primary to-secondary h-1.5 rounded-full transition-all duration-500"
              :style="{
                width: `${((currentStepIndex + 1) / totalSteps) * 100}%`,
              }"
            ></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 🎯 导入
import { ref, computed, watch, nextTick, onMounted, onUnmounted } from 'vue'
import { gsap } from 'gsap'
import { useBattleAnimations } from '~/composables/useBattleAnimations'
import { caseApi as globalCaseApi } from '@/services/case-api'

// 🎯 调试日志前缀
const LOG_PREFIX = '[🎮BATTLE-ANIMATION]'

// 🎯 日志控制开关 - 专注于动画调试
const ENABLE_DETAILED_LOGS = false
const ENABLE_ANIMATION_UPDATE_LOGS = false
const ENABLE_ANIMATION_LOGS = true // 只保留动画相关日志
const ENABLE_DATA_LOGS = false // 关闭数据相关日志
const ENABLE_STEP_LOGS = false // 关闭步骤相关日志

// 🎯 调试开关：仅首轮执行动画（process.dev 环境）
const DEBUG_FIRST_ROUND_ONLY = process.dev

// 🎯 Props定义
interface Props {
  caseDetail: any
  caseItems: any[]
  battlePlayerIndex: number
  isBattleMode: boolean
  battleRound: number
  totalRounds: number
  isPlayerTurn: boolean
  playerId?: string // 新增：玩家ID
  playerName?: string
  totalPlayers?: number
}

const props = withDefaults(defineProps<Props>(), {
  caseDetail: () => ({}),
  caseItems: () => ([]),
  battlePlayerIndex: -1,
  isBattleMode: true,
  battleRound: 1,
  totalRounds: 1,
  isPlayerTurn: false,
  playerId: '',
  playerName: '',
  totalPlayers: 0
})

// 🎯 Emits
const emit = defineEmits<{
  "animation-complete": [result: any]
  "data-ready": [isReady: boolean]
  "update:caseItems": [items: any[]]
}>()

// 🎯 国际化
const { t } = useI18n()

// 🎯 响应式状态
const isAnimating = ref(false)
const animationOffset = ref(0)
const displayItems = ref<any[]>([])
const winnerItem = ref<any>(null)
const currentWinnerPosition = ref(60)
const showStepHint = ref(false)
const currentStepIndex = ref(0)
const isDataReady = ref(false)
const isWaitingForData = ref(false)
const trackContainer = ref<HTMLElement>()
const rootEl = ref<HTMLDivElement>()
const lastAnimationId = ref<string>('')

// 🎯 步骤提示配置
const totalSteps = 5
const steps = [
  { title: t('battle.animation.checking_user'), description: t('battle.animation.checking_user_desc') },
  { title: t('battle.animation.checking_balance'), description: t('battle.animation.checking_balance_desc') },
  { title: t('battle.animation.checking_case'), description: t('battle.animation.checking_case_desc') },
  { title: t('battle.animation.preparing'), description: t('battle.animation.preparing_desc') },
  { title: t('battle.animation.ready_go'), description: t('battle.animation.ready_go_desc') }
]

const currentStep = computed(() => steps[currentStepIndex.value] || steps[0])

// 🎯 数据完整性检查（支持多种字段名）
const hasValidData = computed(() => {
  return !!(
    props.caseItems &&
    props.caseItems.length > 0
  )
})

const caseApi = globalCaseApi

const fetchCaseItemsIfNeeded = async () => {
  if (props.caseItems && props.caseItems.length > 0) return true
  if (!props.caseDetail?.case_key) return false
  try {
    const res = await caseApi.getCaseSkins(props.caseDetail.case_key)
    if (res.code === 0) {
      const list = Array.isArray(res.body) ? res.body : res.body?.items || []
      if (list.length) {
        emit('update:caseItems', list) // 父组件需使用v-model:caseItems
        return true
      }
    }
  } catch (e) {
    console.error('[BattleCaseAnimation] 获取箱子物品失败', e)
  }
  return false
}

// 🎯 数据准备检查
const checkDataReady = async () => {
  if (ENABLE_DATA_LOGS) {
    console.log(`${LOG_PREFIX} 检查数据准备状态:`, {
      isBattleMode: props.isBattleMode,
      hasCaseDetail: !!props.caseDetail?.case_key,
      hasCaseItems: props.caseItems?.length > 0,
      hasValidData: hasValidData.value
    })
  }
  
  if (hasValidData.value || await fetchCaseItemsIfNeeded()) {
    isDataReady.value = true
    isWaitingForData.value = false
    emit('data-ready', true)
    if (ENABLE_DATA_LOGS) {
      console.log(`${LOG_PREFIX} 数据准备完成`)
    }
    return true
  } else {
    isDataReady.value = false
    isWaitingForData.value = true
    emit('data-ready', false)
    if (ENABLE_DATA_LOGS) {
      console.log(`${LOG_PREFIX} 数据未准备好，等待中...`)
    }
    return false
  }
}

// 🎯 等待数据准备
const waitForDataReady = async (timeout = 10000): Promise<boolean> => {
  const startTime = Date.now()
  
  while (!hasValidData.value) {
    if (Date.now() - startTime > timeout) {
      if (ENABLE_DATA_LOGS) {
        console.warn(`${LOG_PREFIX} 数据准备超时`)
      }
      return false
    }
    await new Promise(resolve => setTimeout(resolve, 100))
  }
  
  if (ENABLE_DATA_LOGS) {
    console.log(`${LOG_PREFIX} 数据准备完成`)
  }
  return true
}

// 🎯 方法
const getBattleStatusText = () => {
  if (props.isPlayerTurn) {
    return t('battle.animation.your_turn', { round: props.battleRound })
  }
  return t('battle.animation.waiting_turn')
}

const getLocalizedItemName = (item: any): string => {
  const { locale } = useI18n()
  return locale.value === 'zh-hans' 
    ? (item.name_zh_hans || item.name) 
    : (item.name_en || item.name)
}

const getSkinName = (item: any): string => {
  const fullName = getLocalizedItemName(item)
  const parts = fullName.split(' | ')
  return parts.length > 1 ? parts[1] : fullName
}

const getRarityName = (item: any): string => {
  const { locale } = useI18n()
  if (locale.value === 'zh-hans') {
    return item.rarity_name_zh_hans || item.rarity_name || ''
  }
  return item.rarity_name_en || item.rarity_name || ''
}

const getExteriorName = (item: any): string => {
  const { locale } = useI18n()
  if (locale.value === 'zh-hans') {
    return item.exterior_name_zh_hans || item.exterior_name || ''
  }
  return item.exterior_name_en || item.exterior_name || ''
}

const getCondition = (item: any): string => {
  const name = getLocalizedItemName(item)
  const conditionMatch = name.match(/\((.*?)\)/)
  if (conditionMatch) {
    return conditionMatch[1]
  }
  return ''
}

const isStatTrakItem = (item: any): boolean => {
  return item.name?.includes('StatTrak™') || false
}

const isWinnerItem = (item: any, index: number): boolean => {
  return winnerItem.value && item.id === winnerItem.value.id && index === currentWinnerPosition.value
}

const formatPrice = (price: number): string => {
  return price.toFixed(2)
}

const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.src = '/images/item-placeholder.svg'
}

// 🎯 动画相关方法
const generateAnimationItems = (winner?: any) => {
  console.log(`${LOG_PREFIX} 生成动画物品序列，获胜物品:`, winner)
  
  console.log('[🎰BATTLE] caseItems.length=', props.caseItems?.length)
  
  // 数据源：优先使用真实 caseItems，否则使用占位数据
  const pool = props.caseItems && props.caseItems.length > 0
    ? props.caseItems
    : Array.from({ length: 10 }).map((_, idx) => ({
        id: `placeholder-${idx}`,
        name: 'Loading…',
        image: '/images/item-placeholder.svg',
        item_price: { price: 0 },
        rarity_color: '#6b7280'
      }))

  const items: any[] = []
  const totalItems = 80 // 对战模式使用较少的物品

  for (let i = 0; i < totalItems; i++) {
    if (winner && i === currentWinnerPosition.value) {
      items.push(convertApiItemFormat(winner))
    } else {
      const randomItem = pool[Math.floor(Math.random() * pool.length)]
      items.push(convertApiItemFormat(randomItem))
    }
  }
  
  displayItems.value = items
  
  console.log('[🎰BATTLE] displayItems 生成完成 len=', items.length, ' sample=', items.slice(0,3))
  if (winner) {
    winnerItem.value = winner
  }
}

const convertApiItemFormat = (apiItem: any) => {
  // 调试API数据结构
  console.log(`${LOG_PREFIX} 🔍 转换API物品数据:`, apiItem)
  
  return {
    id: apiItem.uid || apiItem.id || apiItem.item_id,
    name: apiItem.name || apiItem.name_zh_hans || apiItem.name_en || '未知物品',
    name_zh_hans: apiItem.name_zh_hans || apiItem.name,
    name_en: apiItem.name_en || apiItem.name,
    image: apiItem.image,
    price: apiItem.item_price?.price || apiItem.price || 0,
    rarity_color: apiItem.rarity_color || apiItem.item_rarity?.rarity_color || '#6b7280',
    rarity_name: apiItem.rarity_name || apiItem.item_rarity?.rarity_name,
    rarity_name_zh_hans: apiItem.rarity_name_zh_hans || apiItem.item_rarity?.rarity_name_zh_hans,
    rarity_name_en: apiItem.rarity_name_en || apiItem.item_rarity?.rarity_name_en,
    exterior_name: apiItem.exterior_name,
    exterior_name_zh_hans: apiItem.exterior_name_zh_hans,
    exterior_name_en: apiItem.exterior_name_en
  }
}

// ============================= 方案 A：循环滚动 + 结果减速 =============================

// 单位尺寸
const ITEM_WIDTH = 120  // 物品卡片宽度 + 间距，px
const LOOP_DURATION = 8 // 整个轨道滚动一圈时长 s

let loopTween: gsap.core.Tween | null = null
let repeatCounter = 0

// 当后端未及时推送 round_result 时，自动在 LOOP_DURATION-1 秒后本地减速
let autoDecelerateTimer: ReturnType<typeof setTimeout> | null = null

const clearAutoDecelerate = () => {
  if (autoDecelerateTimer) {
    clearTimeout(autoDecelerateTimer)
    autoDecelerateTimer = null
  }
}

const pickRandomWinner = () => {
  if (!props.caseItems || props.caseItems.length === 0) return null
  const rnd = props.caseItems[Math.floor(Math.random() * props.caseItems.length)]
  return rnd
}

// 启动匀速循环滚动
const startInfiniteLoop = () => {
  if (!trackContainer.value) return

  // 如果已经有循环 tween，避免重复
  if (loopTween) return

  const totalWidth = ITEM_WIDTH * displayItems.value.length

  loopTween = gsap.fromTo(
    trackContainer.value,
    { x: 0 },
    {
      x: -totalWidth,
      duration: LOOP_DURATION,
      ease: 'none',
      repeat: -1,
      onStart() {
        console.log('[🎰BATTLE] loopTween started, track children=', (trackContainer.value as HTMLElement).children.length)
      },
      onRepeat() {
        repeatCounter += 1
        if (repeatCounter % 5 === 0 && trackContainer.value) {
          const currentX = gsap.getProperty(trackContainer.value as gsap.TweenTarget, 'x') as number
          console.log(`[🎰BATTLE] loop repeat ${repeatCounter}, currentX=${currentX}`)
        }
      }
    }
  )
}

// 停止循环并减速到目标 offset
const decelerateToWinner = (winner: any) => {
  if (!trackContainer.value || !loopTween) return

  // 🛡️ 空值保护：当后端未提供获胜饰品时，直接停止循环并保持当前位置
  if (!winner) {
    loopTween.kill()
    loopTween = null
    isAnimating.value = false
    emit('animation-complete', { playerIndex: props.battlePlayerIndex, winner: null })
    return
  }

  // 先 kill 循环 tween
  loopTween.kill()
  loopTween = null

  // 确保序列中包含 winner，没有则推到末尾
  let winnerIndex = displayItems.value.findIndex(i => i.id === winner.id)
  if (winnerIndex === -1) {
    displayItems.value.push(convertApiItemFormat(winner))
    winnerIndex = displayItems.value.length - 1
  }

  const currentX = gsap.getProperty(trackContainer.value, 'x') as number
  const targetX = -winnerIndex * ITEM_WIDTH - ITEM_WIDTH / 2
  const delta = targetX - currentX

  gsap.to(trackContainer.value, {
    x: targetX,
    duration: 0.8,
    ease: 'power2.out',
    onComplete() {
      isAnimating.value = false
      winnerItem.value = winner
      // 事件通知父层动画完成
      emit('animation-complete', { playerIndex: props.battlePlayerIndex, winner })
    }
  })

  // 有真实 winner 或本地 fallback 时清除自动计时器
  clearAutoDecelerate()
}

// 监听全局 decelerate 事件（由 useBattleAnimation 在 round_result 时派发）
const handleGlobalDecelerate = (e: Event) => {
  // 期望 detail = { playerId / playerName / playerIndex, item }
  const detail = (e as CustomEvent).detail || {}

  const matchByIndex = detail.playerIndex === props.battlePlayerIndex
  const matchById = detail.playerId && detail.playerId === props.playerId
  const matchByName = detail.playerName && detail.playerName === props.playerName

  if (matchByIndex || matchById || matchByName) {
    decelerateToWinner(detail.item)
  }
}

// ======================================================================================

const startOpening = async () => {
  if (isAnimating.value) {
    if (ENABLE_ANIMATION_LOGS) {
      console.log(`${LOG_PREFIX} 动画进行中，跳过`)
    }
    return
  }
  
  if (ENABLE_ANIMATION_LOGS) {
    console.log(`${LOG_PREFIX} 开始对战开箱动画，玩家索引: ${props.battlePlayerIndex}`)
  }
  
  try {
    // 🎯 1. 检查数据准备状态
    const dataReady = await checkDataReady()
    if (!dataReady) {
      if (ENABLE_DATA_LOGS) {
        console.log(`${LOG_PREFIX} 数据未准备好，进入阻塞等待...`)
      }

      isWaitingForData.value = true

      // 阻塞等待数据准备（最长10秒）
      const ready = await waitForDataReady()
      if (!ready) {
        if (ENABLE_DATA_LOGS) {
          console.warn(`${LOG_PREFIX} 数据等待超时，使用占位物品启动动画（调试）`)
        }
        // 使用占位数据启动动画，winner 为 null
        generateAnimationItems()
        await nextTick()
        startInfiniteLoop()
        // 设置自动减速 fallback
        clearAutoDecelerate()
        autoDecelerateTimer = setTimeout(() => {
          if (isAnimating.value) {
            const fallback = pickRandomWinner()
            decelerateToWinner(fallback)
          }
        }, Math.max(LOOP_DURATION - 1, 1) * 1000)
        return
      }
    }
    
    isAnimating.value = true
    isWaitingForData.value = false
    
    // 🎯 3. 对战模式：使用真实API数据
    if (props.isBattleMode) {
      // 检查是否有有效的箱子物品数据
      if (!hasValidData.value) {
        if (ENABLE_DATA_LOGS) {
          console.warn(`${LOG_PREFIX} 对战模式：没有有效的箱子物品数据`)
        }
        isAnimating.value = false
        return
      }
      
      // 使用真实的箱子物品数据生成动画
      const winner = props.caseItems[0] // 实际应该从Socket消息获取获胜物品
      generateAnimationItems(winner)
      
      if (ENABLE_ANIMATION_LOGS) {
        console.log(`${LOG_PREFIX} 对战模式使用真实API数据，获胜物品:`, winner)
      }
    } else {
      // 普通模式：从props获取
      if (!hasValidData.value) {
        if (ENABLE_DATA_LOGS) {
          console.warn(`${LOG_PREFIX} 没有有效的箱子物品数据`)
        }
        isAnimating.value = false
        return
      }
      
      const winner = props.caseItems[0]
      generateAnimationItems(winner)
    }
    
    // 🎯 4. 等待DOM渲染完成后开始循环滚动动画
    await nextTick()

    if (ENABLE_ANIMATION_LOGS) {
      console.log(`${LOG_PREFIX} 开始无限循环滚动`)
    }
    startInfiniteLoop()
    
    // 设置自动减速 fallback（LOOP_DURATION - 1 秒后）
    clearAutoDecelerate()
    autoDecelerateTimer = setTimeout(() => {
      if (isAnimating.value) {
        const fallback = pickRandomWinner()
        if (ENABLE_ANIMATION_LOGS) {
          console.log(`${LOG_PREFIX} ⏱️ 本地自动减速触发`, fallback)
        }
        decelerateToWinner(fallback)
      }
    }, Math.max(LOOP_DURATION - 1, 1) * 1000)
    
  } catch (error) {
    console.error(`${LOG_PREFIX} 开箱动画失败:`, error)
    isAnimating.value = false
    showStepHint.value = false
    isWaitingForData.value = false
  }
}

const resetAnimation = () => {
  console.log(`${LOG_PREFIX} 重置动画状态`)
  isAnimating.value = false
  winnerItem.value = null
  animationOffset.value = 0
  showStepHint.value = false
  currentStepIndex.value = 0
  
  // 停止所有GSAP动画
  gsap.killTweensOf(animationOffset)
  if (loopTween) {
    loopTween.kill()
    loopTween = null
  }
  clearAutoDecelerate()
}

// 🎯 监听器
watch(() => props.isPlayerTurn, (newValue) => {
  if (newValue && !props.isBattleMode) {
    if (ENABLE_ANIMATION_LOGS) {
      console.log(`${LOG_PREFIX} 普通模式：轮到玩家 ${props.battlePlayerIndex} 开箱`)
    }
    nextTick(() => {
      startOpening()
    })
  }
  
  // 🎯 关键修复：对战模式下的动画触发
  if (newValue && props.isBattleMode) {
    if (ENABLE_ANIMATION_LOGS) {
      console.log(`${LOG_PREFIX} 对战模式：轮到玩家 ${props.battlePlayerIndex} 开箱`)
    }
    nextTick(() => {
      if (!isAnimating.value && hasValidData.value) {
        startOpening()
      }
    })
  }
})

// 🎯 监听箱子物品数据变化
watch(() => props.caseItems, (newItems, oldItems) => {
  if (ENABLE_DATA_LOGS) {
    console.log(`${LOG_PREFIX} 🧪 箱子物品数据变化:`, {
      oldCount: oldItems?.length || 0,
      newCount: newItems?.length || 0,
      hasValidData: hasValidData.value,
      newItems: newItems?.slice(0, 2) // 只显示前两个物品
    })
  }
  
  // ⚠️ 已移除：在对战模式下，收到opening_start后才启动动画
  if (newItems && newItems.length > 0 && (!oldItems || oldItems.length === 0) && !props.isBattleMode) {
    if (ENABLE_ANIMATION_LOGS) {
      console.log(`${LOG_PREFIX} 🧪 普通模式：检测到箱子物品数据从无到有，自动开始动画`)
    }
    nextTick(() => {
      if (!isAnimating.value) {
        startOpening()
      }
    })
  }
  
  // 🧪 开发模式：任何数据变化都自动开始
  if (process.dev && newItems && newItems.length > 0 && !isAnimating.value) {
    // 所有玩家同时开始动画
    const delayTime = 1000 // 统一1秒后开始
    if (ENABLE_ANIMATION_LOGS) {
      console.log(`${LOG_PREFIX} 🧪 开发模式：检测到数据变化，${delayTime/1000}秒后自动开始动画（玩家${props.battlePlayerIndex}）`)
    }
    setTimeout(() => {
      if (!isAnimating.value) {
        startOpening()
      }
    }, delayTime)
  }
}, { deep: true, immediate: true })

// 🎯 监听箱子详情变化
watch(() => props.caseDetail, (newDetail, oldDetail) => {
  if (ENABLE_DETAILED_LOGS) {
    console.log(`${LOG_PREFIX} 🧪 箱子详情变化:`, {
      oldDetail: oldDetail ? '有数据' : '无数据',
      newDetail: newDetail ? '有数据' : '无数据',
      newDetailKeys: newDetail ? Object.keys(newDetail) : []
    })
  }
}, { deep: true })

// 🎯 当hasValidData从false变为true时自动尝试开始动画（解决先收到caseItems后更新caseDetail字段的情况）
watch(hasValidData, (newVal, oldVal) => {
  if (newVal && !oldVal && !props.isBattleMode) {
    if (ENABLE_ANIMATION_LOGS) {
      console.log(`${LOG_PREFIX} hasValidData 变为 true，尝试启动动画`)
    }
    if (!isAnimating.value) {
      startOpening()
    }
  }
})

// 🎯 监听回合变化，确保每一轮所有玩家同时滚动
watch(() => props.battleRound, (newRound, oldRound) => {
  if (ENABLE_ANIMATION_LOGS) {
    console.log(`${LOG_PREFIX} battleRound changed:`, { oldRound, newRound, player: props.battlePlayerIndex })
  }

  // 回合变化时仅重置动画，等待 caseItems 更新后由其他 watcher 启动动画
  resetAnimation()
})

// 🎯 动画元素注册到全局动画系统
const { registerCaseElement, unregisterCaseElement } = useBattleAnimations()

// 🎯 玩家相关唯一标识集合：username、uid、nickname 三个都尝试注册
const playerIdentifiers = computed(() => {
  const identifiers: string[] = []
  if (props.playerName) identifiers.push(String(props.playerName))
  if (props.playerId) identifiers.push(String(props.playerId))
  // fallback
  identifiers.push(`player-${props.battlePlayerIndex}`)
  return identifiers
})

const playerName = computed(() => playerIdentifiers.value[0])

// 🎯 自动动画事件监听
onMounted(() => {
  if (ENABLE_ANIMATION_LOGS) {
    console.log(`${LOG_PREFIX} 对战动画组件已挂载，玩家索引: ${props.battlePlayerIndex}`)
  }
  
  if (ENABLE_DETAILED_LOGS) {
    console.log(`${LOG_PREFIX} 🧪 组件挂载时的详细状态:`, {
      caseDetail: props.caseDetail,
      caseItemsCount: props.caseItems?.length,
      caseItems: props.caseItems,
      battlePlayerIndex: props.battlePlayerIndex,
      isBattleMode: props.isBattleMode,
      battleRound: props.battleRound,
      totalRounds: props.totalRounds,
      isPlayerTurn: props.isPlayerTurn,
      hasValidData: hasValidData.value
    })
  }
  
  if (rootEl.value) {
    playerIdentifiers.value.forEach(id => {
      registerCaseElement(id, rootEl.value as HTMLElement)
      console.log(`[🎰CASE-ANIMATION] 注册元素: ${id}`)
    })
  } else {
    console.warn('[🎰CASE-ANIMATION] rootEl.value 为空，无法注册元素')
  }

  window.addEventListener('battle:decelerate', handleGlobalDecelerate)
  window.addEventListener('battle:opening_start', handleGlobalOpeningStart)
  window.addEventListener('battle-animation-start', handleBattleAnimStart as any)
})

// 🎯 监听rootEl变化，确保元素被正确注册
watch(rootEl, (newEl) => {
  if (newEl) {
    console.log('[🎰CASE-ANIMATION] rootEl已准备好，注册元素')
    playerIdentifiers.value.forEach(id => {
      registerCaseElement(id, newEl)
      console.log(`[🎰CASE-ANIMATION] 延迟注册元素: ${id}`)
    })
  }
}, { immediate: true })

// 🎯 清理事件监听
onUnmounted(() => {
  playerIdentifiers.value.forEach(id => unregisterCaseElement(id))

  if (loopTween) {
    loopTween.kill()
    loopTween = null
  }
  window.removeEventListener('battle:decelerate', handleGlobalDecelerate)
  window.removeEventListener('battle:opening_start', handleGlobalOpeningStart)
  window.removeEventListener('battle-animation-start', handleBattleAnimStart as any)
  clearAutoDecelerate()
})

// 🎯 暴露方法
defineExpose({
  startOpening,
  resetAnimation
})

// 监听全局 opening_start 事件（由 useBattleAnimation 派发）
const handleGlobalOpeningStart = (e: Event) => {
  if (isAnimating.value) return // 已在动画中
  const detail = (e as CustomEvent).detail || {}
  const participants = detail?.data?.participants || detail?.participants || []

  const expectedPlayers =
    detail?.data?.max_participants ||
    detail?.data?.max_joiner ||
    detail?.data?.max_joiners ||
    detail?.max_participants ||
    detail?.max_joiner ||
    props.totalPlayers ||
    participants.length // 回退为当前长度

  // 若启用仅首轮调试，但当前round>1，则忽略
  const roundNo = detail?.data?.round || detail?.round || 1
  if (DEBUG_FIRST_ROUND_ONLY && roundNo > 1) {
    if (ENABLE_ANIMATION_LOGS) {
      console.log(`${LOG_PREFIX} 🔕 DEBUG_FIRST_ROUND_ONLY - 忽略 round ${roundNo}`)
    }
    return
  }

  // 若参与者数组为空，则忽略本次 opening_start
  if (participants.length === 0) {
    if (ENABLE_ANIMATION_LOGS) {
      console.log(`${LOG_PREFIX} ⏩ opening_start 忽略：参与者数组为空`)
    }
    return
  }

  // 判断是否匹配当前组件玩家
  const matched = participants.some((p: any, idx: number) => {
    const uid = p.user?.uid || p.user?.id || p.user?.username
    const name = p.user?.username || p.user?.nickname
    const matchByIdx = idx === props.battlePlayerIndex
    const matchById = uid && uid === props.playerId
    const matchByName = name && name === props.playerName
    return matchByIdx || matchById || matchByName
  })

  if (matched) {
    if (ENABLE_ANIMATION_LOGS) {
      console.log(`${LOG_PREFIX} 💡 opening_start 命中玩家${props.battlePlayerIndex}，触发动画`)
    }
    nextTick(() => startOpening())
  }

  const animationId = detail?.data?.animation_id || detail?.animation_id || ''
  if (animationId && animationId === lastAnimationId.value) {
    if (ENABLE_ANIMATION_LOGS) {
      console.log(`${LOG_PREFIX} 🔁 已处理过 animation_id ${animationId}，忽略`)
    }
    return
  }

  // 更新lastAnimationId
  if (animationId) lastAnimationId.value = animationId
}

// === 同步控制接入 ===
const handleBattleAnimStart = (e: Event) => {
  const { id, catchUp = 0, duration } = (e as CustomEvent).detail || {}
  // 仅在当前组件对应玩家时启动；简单判定：若 isBattleMode 则全部启动
  if (!props.isBattleMode) return
  startOpening()
  if (catchUp > 0 && loopTween) {
    const progress = Math.min(catchUp / duration, 1)
    loopTween.progress(progress)
  }
}
</script>

<style scoped>
.bg-grid-pattern {
  background-image: linear-gradient(
      rgba(255, 255, 255, 0.1) 1px,
      transparent 1px
    ),
    linear-gradient(
      90deg,
      rgba(255, 255, 255, 0.1) 1px,
      transparent 1px
    );
  background-size: 20px 20px;
}

.winner-glow {
  filter: drop-shadow(0 0 10px rgba(0, 188, 212, 0.5));
}

/* 对战模式特殊样式 */
.battle-case-animation {
  --animation-duration: 8s;
  --item-width: 112px; /* 28 * 4 */
  --item-spacing: 8px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .battle-case-animation {
    --item-width: 96px; /* 24 * 4 */
  }
}
</style>