<template>
  <div class="battle-case-display">
    <!-- 箱子展示头部 -->
    <div class="cases-header">
      <h3 class="cases-title">{{ t('battle.cases.title') }}</h3>
      <div class="rounds-indicator">
        <span class="round-info">
          {{ t('battle.round') }} {{ currentRound }} / {{ totalRounds }}
        </span>
      </div>
    </div>
    
    <!-- 箱子网格 -->
    <div :class="['cases-flex-container', gridLayoutClass]">
      <div 
        v-for="(caseItem, index) in cases" 
        :key="caseItem.id"
        :class="['case-card', {
          'current': !isBattleFinished && isCurrentRoundCase(caseItem, index),
          'opening': !isBattleFinished && isOpeningCase(caseItem, index),
          'completing': !isBattleFinished && isCompletingCase(caseItem, index),
          'interval': !isBattleFinished && isIntervalCase(caseItem, index),
          'completed': isCompletedCase(caseItem, index),
          'pending': isPendingCase(caseItem, index)
        }]"
      >
        <!-- 箱子图片 -->
        <div class="case-image-container">
          <img 
            :src="caseItem.cover" 
            :alt="getCaseName(caseItem)"
            class="case-image"
            @error="handleImageError"
          />
          
          <!-- 状态覆盖层 -->
          <div class="case-overlay">
            <!-- 当前轮次标识 -->
            <div v-if="!isBattleFinished && isCurrentRoundCase(caseItem, index)" class="current-badge">
              <Icon name="heroicons:play" class="w-4 h-4" />
              <span>{{ t('battle.cases.current') }}</span>
            </div>
            
            <!-- 开箱中标识 -->
            <div v-if="!isBattleFinished && isOpeningCase(caseItem, index)" class="opening-badge">
              <Icon name="heroicons:sparkles" class="w-4 h-4" />
              <span>{{ t('battle.cases.opening') }}</span>
            </div>
            
            <!-- 完成中标识 -->
            <div v-if="!isBattleFinished && isCompletingCase(caseItem, index)" class="completing-badge">
              <Icon name="heroicons:bolt" class="w-4 h-4" />
              <span>{{ t('battle.cases.completing') }}</span>
            </div>
            
            <!-- 间隔中标识 -->
            <div v-if="!isBattleFinished && isIntervalCase(caseItem, index)" class="interval-badge">
              <Icon name="heroicons:pause" class="w-4 h-4" />
              <span>{{ t('battle.cases.interval') }}</span>
            </div>
            
            <!-- 已完成标识 -->
            <div v-if="isCompletedCase(caseItem, index)" class="completed-badge">
              <Icon name="heroicons:check" class="w-4 h-4" />
              <span>{{ t('battle.cases.completed') }}</span>
            </div>
            
            <!-- 使用次数标识 -->
            <div v-if="caseItem.count && caseItem.count > 1" class="count-badge">
              <Icon name="heroicons:cube" class="w-3 h-3" />
              <span>x{{ caseItem.count }}</span>
            </div>
          </div>
          
          <!-- 开箱动画效果 -->
          <div v-if="!isBattleFinished && isOpeningCase(caseItem, index)" class="opening-animation">
            <div class="sparkle sparkle-1"></div>
            <div class="sparkle sparkle-2"></div>
            <div class="sparkle sparkle-3"></div>
            <div class="sparkle sparkle-4"></div>
            <div class="energy-ring"></div>
            <div class="pulse-wave"></div>
          </div>
          
          <!-- 完成中动画效果 -->
          <div v-if="!isBattleFinished && isCompletingCase(caseItem, index)" class="completing-animation">
            <div class="completion-glow"></div>
            <div class="success-particles"></div>
          </div>
          
          <!-- 间隔动画效果 -->
          <div v-if="!isBattleFinished && isIntervalCase(caseItem, index)" class="interval-animation">
            <div class="cooldown-ring"></div>
          </div>
        </div>
        
        <!-- 箱子信息 -->
        <div class="case-info flex justify-between">
          <div class="case-name">{{ getCaseName(caseItem) }}</div>
          <div class="case-price">${{ formatPrice(caseItem.price) }}</div>
        </div>
        
        <!-- 轮次指示器 -->
        <div class="round-indicator">
          <span class="round-number">{{ index + 1 }}</span>
        </div>
      </div>
    </div>
    
    <!-- 箱子统计信息 -->
    <div class="cases-stats">
      <div class="stat-item">
        <Icon name="heroicons:cube" class="w-4 h-4" />
        <span class="stat-label">{{ t('battle.cases.total_cases') }}</span>
        <span class="stat-value">{{ cases.length }}</span>
      </div>
      <div class="stat-item">
        <Icon name="heroicons:currency-dollar" class="w-4 h-4" />
        <span class="stat-label">{{ t('battle.cases.total_value') }}</span>
        <span class="stat-value">${{ formatPrice(totalValue) }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 🎯 导入枚举
// import { RoundExecutionState } from '~/composables/useBattleRoundAnimationManager'

// 🎯 国际化设置
const { t, locale } = useI18n()

// 🎯 Props定义（与页面使用方式完全匹配）
interface Case {
  id: string
  key?: string
  case_key?: string
  name: string
  name_en?: string
  name_zh_hans?: string
  cover: string
  price: number
  count?: number
}

interface Props {
  cases: Case[]                // battleCore.battleState.displayCases.value
  currentRound: number         // battleCore.battleState.currentRound.value
  totalRounds: number          // battleCore.battleState.totalRounds.value
  openingCaseId?: string       // 正在执行动画的箱子ID
  isBattleStarted?: boolean    // 对战是否已开始
  isBattleFinished?: boolean   // 对战是否已结束（全部动画完成或状态结束）
  isRoundChanging?: boolean    // 轮次是否正在切换
  completedCases?: string[]    // 已完成开箱的箱子ID列表
}

const props = withDefaults(defineProps<Props>(), {
  openingCaseId: '',
  isBattleStarted: false,
  isBattleFinished: false,
  isRoundChanging: false,
  completedCases: () => []
})

// 🎯 计算属性
const totalValue = computed(() => {
  return props.cases.reduce((sum, caseItem) => {
    const count = caseItem.count || 1
    return sum + (caseItem.price * count)
  }, 0)
})

// 🎯 网格布局计算属性
const gridLayoutClass = computed(() => {
  const caseCount = props.cases.length
  if (caseCount <= 2) {
    return 'grid-2-cases'
  } else if (caseCount === 3) {
    return 'grid-3-cases'
  } else {
    return 'grid-4-cases'
  }
})

// 🎯 状态缓存：避免重复计算
const statusCache = ref<Map<string, { status: string; timestamp: number }>>(new Map())

// 🎯 状态判断函数 - 🚨 完全重构：优先使用openingCaseId prop
const getCaseStatus = (caseItem: Case, index: number) => {
  // 简单缓存机制：同一渲染周期内复用结果
  const cacheKey = `${index}-${props.currentRound}-${props.openingCaseId}-${props.isBattleFinished}`
  const cached = statusCache.value.get(cacheKey)
  const now = Date.now()
  
  if (cached && now - cached.timestamp < 100) { // 100ms缓存
    return cached.status
  }
  
  // 🎯 调试信息：输出所有箱子的状态判断
  console.log(`[🎯CASE-DISPLAY] 箱子${index} 状态判断:`, {
    箱子ID: caseItem.id,
    箱子key: caseItem.key,
    箱子case_key: caseItem.case_key,
    对应轮次: index + 1,
    页面当前轮次: props.currentRound,
    传入openingCaseId: props.openingCaseId,
    传入openingCaseId类型: typeof props.openingCaseId,
    传入openingCaseId长度: props.openingCaseId?.length,
    是否对战完成: props.isBattleFinished,
    是否对战开始: props.isBattleStarted,
    箱子名称: caseItem.name,
    是否匹配id: props.openingCaseId === caseItem.id,
    是否匹配key: props.openingCaseId === caseItem.key,
    是否匹配case_key: props.openingCaseId === caseItem.case_key
  })
  
  // 🚨 关键修复：使用严格的优先级顺序，确保只有一个权威状态
  
  // 🎯 状态判断逻辑
  let finalStatus = 'pending'

  // 检查箱子是否已完成（通过completedCases列表）
  const isCaseCompleted = props.completedCases.some(completedId =>
    completedId === caseItem.id ||
    completedId === caseItem.key ||
    completedId === caseItem.case_key
  )

  // 1. 对战已完成：所有箱子显示为"已完成"
  if (props.isBattleFinished) {
    finalStatus = 'completed'
  }
  // 2. 对战未开始：所有箱子显示为"等待中"
  else if (!props.isBattleStarted) {
    finalStatus = 'waiting'
  }
  // 3. 箱子已在完成列表中：显示为"已完成"
  else if (isCaseCompleted) {
    finalStatus = 'completed'
  }
  // 4. 对战进行中：根据openingCaseId判断当前开箱的箱子
  // 🎯 关键修复：检查多个字段匹配
  else if (props.openingCaseId && (
    props.openingCaseId === caseItem.id ||
    props.openingCaseId === caseItem.key ||
    props.openingCaseId === caseItem.case_key
  )) {
    finalStatus = 'opening'
  }
  // 5. 已完成的轮次：显示为"已完成"
  else if (index + 1 < props.currentRound) {
    finalStatus = 'completed'
  }
  // 6. 当前轮次但非开箱中：显示为"等待中"
  else if (index + 1 === props.currentRound) {
    finalStatus = props.isRoundChanging ? 'changing' : 'waiting'
  }
  // 7. 未来轮次：显示为"等待中"
  else {
    finalStatus = 'waiting'
  }
  
  // 缓存结果
  statusCache.value.set(cacheKey, { status: finalStatus, timestamp: now })
  
  console.log(`[🎯CASE-DISPLAY] 箱子${index} 最终状态: ${finalStatus}`)
  
  return finalStatus
}

// 🎯 状态检查函数 - 简化版本，只调用一次getCaseStatus
const isCurrentRoundCase = (caseItem: Case, index: number): boolean => {
  const status = getCaseStatus(caseItem, index)
  return status === 'current' || status === 'preparing'
}

const isOpeningCase = (caseItem: Case, index: number): boolean => {
  return getCaseStatus(caseItem, index) === 'opening'
}

const isCompletingCase = (caseItem: Case, index: number): boolean => {
  return getCaseStatus(caseItem, index) === 'completing'
}

const isIntervalCase = (caseItem: Case, index: number): boolean => {
  return getCaseStatus(caseItem, index) === 'interval'
}

const isCompletedCase = (caseItem: Case, index: number): boolean => {
  return getCaseStatus(caseItem, index) === 'completed'
}

const isPendingCase = (caseItem: Case, index: number): boolean => {
  return getCaseStatus(caseItem, index) === 'pending'
}

// 🎯 国际化名称处理
const getCaseName = (caseItem: Case): string => {
  if (locale.value === 'zh-hans' && caseItem.name_zh_hans) {
    return caseItem.name_zh_hans
  }
  if (locale.value === 'en' && caseItem.name_en) {
    return caseItem.name_en
  }
  return caseItem.name
}

// 🎯 价格格式化
const formatPrice = (price: number): string => {
  return price.toFixed(2)
}

// 🎯 图片错误处理
const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.src = '/images/case-placeholder.svg'
}
</script>

<style lang="scss" scoped>
// 🎯 对战箱子展示容器
.battle-case-display {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: var(--radius-lg, 0.75rem);
  padding: 1.5rem;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

// 🎯 箱子展示头部
.cases-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-bottom: 1rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.cases-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
}

.rounds-indicator {
  display: flex;
  align-items: center;
}

.round-info {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.7);
  background: rgba(255, 255, 255, 0.1);
  padding: 0.375rem 0.75rem;
  border-radius: var(--radius-md, 0.375rem);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

// 🎯 箱子网格容器
.cases-flex-container {
  display: flex;
  justify-content: center;
  align-items: start;
  gap: 1rem;
  max-width: 100%;
  margin: 0 auto;
  flex-wrap: wrap;
}

// 🎯 2个箱子的布局 - 增大尺寸
.grid-2-cases {
  .case-card {
    flex: 0 0 calc(40% - 0.5rem);
    max-width: 280px;
  }
}

// 🎯 3个箱子的布局 - 增大尺寸
.grid-3-cases {
  .case-card {
    flex: 0 0 calc(30% - 0.67rem);
    max-width: 240px;
  }
}

// 🎯 4个箱子的布局 - 保持原有尺寸
.grid-4-cases {
  .case-card {
    flex: 0 0 calc(25% - 0.75rem);
    max-width: 200px;
  }
}

// 🎯 箱子卡片
.case-card {
  position: relative;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-lg, 0.75rem);
  padding: 0.75rem;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  min-height: 160px;
  width: 100%;
  max-width: 200px;

  &:hover {
    background: rgba(255, 255, 255, 0.08);
    border-color: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
  }

  &.current {
    background: rgba(59, 130, 246, 0.15);
    border-color: rgba(59, 130, 246, 0.4);
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
  }

  &.opening {
    background: rgba(168, 85, 247, 0.15);
    border-color: rgba(168, 85, 247, 0.4);
    box-shadow: 0 0 0 2px rgba(168, 85, 247, 0.2);
    animation: openingPulse 1.5s ease-in-out infinite;
  }

  &.completing {
    background: rgba(34, 197, 94, 0.15);
    border-color: rgba(34, 197, 94, 0.4);
    box-shadow: 0 0 0 2px rgba(34, 197, 94, 0.2);
  }

  &.interval {
    background: rgba(245, 158, 11, 0.15);
    border-color: rgba(245, 158, 11, 0.4);
    box-shadow: 0 0 0 2px rgba(245, 158, 11, 0.2);
  }

  &.completed {
    background: rgba(34, 197, 94, 0.1);
    border-color: rgba(34, 197, 94, 0.3);
    opacity: 0.8;
  }

  &.changing {
    background: rgba(139, 92, 246, 0.15);
    border-color: rgba(139, 92, 246, 0.4);
    box-shadow: 0 0 0 2px rgba(139, 92, 246, 0.2);
    animation: roundChangingCase 1.5s ease-in-out infinite;
  }

  &.pending {
    background: rgba(255, 255, 255, 0.03);
    border-color: rgba(255, 255, 255, 0.1);
    opacity: 0.6;
  }
}

// 🎯 箱子图片容器
.case-image-container {
  position: relative;
  width: 100%;
  aspect-ratio: 4/3;
  border-radius: var(--radius-md, 0.375rem);
  overflow: hidden;
  background: rgba(0, 0, 0, 0.2);
}

.case-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.case-card:hover .case-image {
  transform: scale(1.05);
}

// 🎯 状态覆盖层
.case-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.6);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.case-card:hover .case-overlay,
.case-card.current .case-overlay,
.case-card.opening .case-overlay,
.case-card.completing .case-overlay,
.case-card.interval .case-overlay,
.case-card.completed .case-overlay {
  opacity: 1;
}

// 🎯 状态徽章
.current-badge,
.opening-badge,
.completing-badge,
.interval-badge,
.completed-badge {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.375rem 0.5rem;
  border-radius: var(--radius-md, 0.375rem);
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  backdrop-filter: blur(10px);
}

.current-badge {
  background: rgba(59, 130, 246, 0.9);
  color: #ffffff;
}

.opening-badge {
  background: rgba(168, 85, 247, 0.9);
  color: #ffffff;
}

.completing-badge {
  background: rgba(34, 197, 94, 0.9);
  color: #ffffff;
}

.interval-badge {
  background: rgba(245, 158, 11, 0.9);
  color: #ffffff;
}

.completed-badge {
  background: rgba(34, 197, 94, 0.9);
  color: #ffffff;
}

.count-badge {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.375rem;
  background: rgba(0, 0, 0, 0.8);
  color: rgba(255, 255, 255, 0.9);
  border-radius: var(--radius-sm, 0.25rem);
  font-size: 0.75rem;
  font-weight: 600;
}

// 🎯 箱子信息
.case-info {
  
}

.case-name {
  font-size: 0.875rem;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.case-price {
  font-size: 0.875rem;
  font-weight: 600;
  color: #22c55e;
}

// 🎯 轮次指示器
.round-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.25rem;
}

.round-number {
  font-size: 0.75rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.7);
  background: rgba(255, 255, 255, 0.1);
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius-sm, 0.25rem);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

// 🎯 箱子统计信息
.cases-stats {
  display: flex;
  align-items: center;
  justify-content: space-around;
  padding-top: 1rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  gap: 1rem;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-md, 0.375rem);
  flex: 1;
  justify-content: center;
}

.stat-label {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.7);
  font-weight: 500;
}

.stat-value {
  font-size: 0.875rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
}

// 🎯 动画效果
@keyframes openingPulse {
  0%, 100% {
    box-shadow: 0 0 0 2px rgba(168, 85, 247, 0.2);
  }
  50% {
    box-shadow: 0 0 0 4px rgba(168, 85, 247, 0.4);
  }
}

@keyframes roundChangingCase {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 2px rgba(139, 92, 246, 0.2);
    border-color: rgba(139, 92, 246, 0.4);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 0 6px rgba(139, 92, 246, 0.3);
    border-color: rgba(139, 92, 246, 0.6);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 2px rgba(139, 92, 246, 0.2);
    border-color: rgba(139, 92, 246, 0.4);
  }
}

// 🎯 开箱动画效果
.opening-animation {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.sparkle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: #ffffff;
  border-radius: 50%;
  animation: sparkleFloat 2s ease-in-out infinite;

  &.sparkle-1 {
    top: 20%;
    left: 20%;
    animation-delay: 0s;
  }

  &.sparkle-2 {
    top: 30%;
    right: 20%;
    animation-delay: 0.5s;
  }

  &.sparkle-3 {
    bottom: 30%;
    left: 30%;
    animation-delay: 1s;
  }

  &.sparkle-4 {
    bottom: 20%;
    right: 30%;
    animation-delay: 1.5s;
  }
}

@keyframes sparkleFloat {
  0%, 100% {
    opacity: 0;
    transform: translateY(0) scale(0);
  }
  50% {
    opacity: 1;
    transform: translateY(-10px) scale(1);
  }
}

.energy-ring {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 60px;
  height: 60px;
  border: 2px solid rgba(168, 85, 247, 0.6);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: energyRotate 2s linear infinite;
}

@keyframes energyRotate {
  0% {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  100% {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

.pulse-wave {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 40px;
  height: 40px;
  border: 2px solid rgba(168, 85, 247, 0.4);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: pulseExpand 1.5s ease-out infinite;
}

@keyframes pulseExpand {
  0% {
    width: 40px;
    height: 40px;
    opacity: 1;
  }
  100% {
    width: 80px;
    height: 80px;
    opacity: 0;
  }
}

// 🎯 完成中动画效果
.completing-animation {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.completion-glow {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle, rgba(34, 197, 94, 0.3) 0%, transparent 70%);
  animation: completionGlow 1s ease-out;
}

@keyframes completionGlow {
  0% {
    opacity: 0;
    transform: scale(0.5);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
  100% {
    opacity: 0;
    transform: scale(1.5);
  }
}

.success-particles {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 4px;
  height: 4px;
  background: #22c55e;
  border-radius: 50%;
  animation: successParticle 0.8s ease-out;
}

@keyframes successParticle {
  0% {
    transform: translate(-50%, -50%) scale(0);
    opacity: 1;
  }
  100% {
    transform: translate(-50%, -50%) scale(3);
    opacity: 0;
  }
}

// 🎯 间隔动画效果
.interval-animation {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.cooldown-ring {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 50px;
  height: 50px;
  border: 2px solid rgba(245, 158, 11, 0.4);
  border-top-color: transparent;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: cooldownRotate 2s linear infinite;
}

@keyframes cooldownRotate {
  0% {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  100% {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

// 🎯 响应式设计
@media (max-width: 768px) {
  .battle-case-display {
    padding: 1rem;
    gap: 1rem;
  }

  .cases-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }

  .cases-flex-container {
    gap: 0.5rem;
  }

  // 🎯 移动端箱子布局 - 增大尺寸
  .grid-2-cases {
    .case-card {
      flex: 0 0 calc(45% - 0.25rem);
      max-width: 200px;
    }
  }
  
  .grid-3-cases {
    .case-card {
      flex: 0 0 calc(45% - 0.25rem);
      max-width: 180px;
    }
  }
  
  .grid-4-cases {
    .case-card {
      flex: 0 0 calc(50% - 0.25rem);
      max-width: 160px;
    }
  }

  .case-card {
    min-height: 120px;
    padding: 0.5rem;
    max-width: none;
  }

  .case-name {
    font-size: 0.75rem;
  }

  .case-price {
    font-size: 0.75rem;
  }

  .cases-stats {
    flex-direction: column;
    gap: 0.75rem;
  }

  .stat-item {
    justify-content: space-between;
  }
}

@media (max-width: 480px) {
  .cases-flex-container {
    gap: 0.375rem;
  }

  // 🎯 小屏幕箱子布局 - 增大尺寸
  .grid-2-cases {
    .case-card {
      flex: 0 0 calc(45% - 0.1875rem);
      max-width: 160px;
    }
  }
  
  .grid-3-cases {
    .case-card {
      flex: 0 0 calc(45% - 0.1875rem);
      max-width: 140px;
    }
  }
  
  .grid-4-cases {
    .case-card {
      flex: 0 0 calc(50% - 0.1875rem);
      max-width: 140px;
    }
  }

  .case-card {
    min-height: 100px;
    padding: 0.375rem;
  }

  .case-name {
    font-size: 0.625rem;
    -webkit-line-clamp: 1;
  }

  .case-price {
    font-size: 0.625rem;
  }

  .round-number {
    font-size: 0.625rem;
    padding: 0.125rem 0.25rem;
  }
}

@media (min-width: 1024px) {
  .cases-flex-container {
    gap: 1.5rem;
  }

  // 🎯 桌面端箱子布局 - 增大尺寸
  .grid-2-cases {
    .case-card {
      flex: 0 0 calc(40% - 1.125rem);
      max-width: 320px;
    }
  }
  
  .grid-3-cases {
    .case-card {
      flex: 0 0 calc(30% - 1rem);
      max-width: 280px;
    }
  }
  
  .grid-4-cases {
    .case-card {
      flex: 0 0 calc(25% - 1.125rem);
      max-width: 220px;
    }
  }

  .case-card {
    max-width: 220px;
  }
}

@media (min-width: 1280px) {
  .cases-flex-container {
    gap: 2rem;
  }

  // 🎯 大屏幕箱子布局 - 增大尺寸
  .grid-2-cases {
    .case-card {
      flex: 0 0 calc(40% - 1.5rem);
      max-width: 360px;
    }
  }
  
  .grid-3-cases {
    .case-card {
      flex: 0 0 calc(30% - 1.33rem);
      max-width: 320px;
    }
  }
  
  .grid-4-cases {
    .case-card {
      flex: 0 0 calc(25% - 1.5rem);
      max-width: 250px;
    }
  }

  .case-card {
    max-width: 250px;
  }
}
</style> 