<template>
  <div class="player-card empty-slot-card rounded-lg" :class="{ 'waiting-state': !isBattleStarted }">
    <!-- 等待状态下的紧凑布局 -->
    <div v-if="!isBattleStarted" class="waiting-layout">
      <!-- 头像和文本紧凑排列 -->
      <div class="compact-header">
        <div class="avatar-section">
          <div class="empty-avatar">
            <Icon name="heroicons:user-plus" class="w-8 h-8 text-gray-400" />
          </div>
          <!-- 简化的脉冲光环 -->
          <div class="pulse-rings">
            <div class="pulse-ring ring-1"></div>
            <div class="pulse-ring ring-2"></div>
          </div>
        </div>
        
        <div class="text-section">
          <div class="empty-player-name">
            {{ t("battle.detail.waiting_for_player") }}
          </div>
          
          <!-- 加入按钮 -->
          <button
            v-if="shouldShowJoinButton"
            @click="handleJoinClick"
            class="join-battle-btn-modern"
          >
            <div class="btn-content">
              <Icon name="heroicons:user-plus" class="w-4 h-4" />
              <span>{{ t("battle.detail.join_battle") }}</span>
            </div>
            <div class="btn-glow"></div>
          </button>
        </div>
      </div>
    </div>

    <!-- 对战开始后的完整布局 -->
    <div v-else class="battle-layout">
      <!-- 空位头部 -->
      <div class="player-header player-header-center">
        <div class="player-avatar-container">
          <div class="empty-avatar">
            <Icon name="heroicons:user-plus" class="w-8 h-8 text-gray-400" />
          </div>
        </div>

        <div class="player-info-center">
          <div class="empty-player-name">
            {{ t("battle.detail.waiting_for_player") }}
          </div>
          
          <!-- 对战开始后的空位提示 -->
          <div class="empty-slot-message-modern">
            <Icon name="heroicons:clock" class="w-4 h-4 text-gray-400" />
            <span class="text-gray-400 text-sm">{{ t("battle.detail.battle_in_progress") }}</span>
          </div>
        </div>
      </div>

      <!-- 空位轮次网格 -->
      <div class="rounds-section">
        <div class="rounds-header">
          <Icon name="heroicons:plus-circle" class="w-4 h-4 text-gray-400" />
          <span class="rounds-title text-gray-400">{{ t("battle.detail.opening_results") }}</span>
        </div>
        <div class="rounds-grid" :data-players="maxPlayers">
          <div
            v-for="roundIndex in totalRounds"
            :key="roundIndex"
            class="round-slot empty-round-slot-modern"
          >
            <div class="empty-item-slot-modern">
              <Icon name="heroicons:plus-circle" class="w-6 h-6 text-gray-400" />
              <div class="slot-glow"></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 加入对战确认模态框 -->
    <NotificationModal
      :visible="showJoinModal"
      type="info"
      :title="t('battle.confirm.join.title')"
      :subtitle="t('battle.confirm.join.subtitle')"
      :confirm-text="t('battle.confirm.join.confirm')"
      confirm-icon="heroicons:user-plus"
      :loading="modalLoading"
      :loading-text="t('battle.confirm.join.loading')"
      @confirm="handleConfirmJoin"
      @cancel="handleCancelJoin"
      @close="handleCancelJoin"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import NotificationModal from '~/components/ui/NotificationModal.vue'

// 🎯 国际化设置
const { t } = useI18n();

// 🎯 开发模式检测
const isDev = import.meta.dev

// 🎯 Props定义
interface Props {
  index: number;
  totalRounds: number;
  maxPlayers: number;
  isBattleStarted?: boolean;
  isUserJoined?: boolean;
  isUserCreator?: boolean;
  currentUserId?: string;
  // 控制是否允许加入对战
  canJoin?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  isBattleStarted: false,
  isUserJoined: false,
  isUserCreator: false,
  currentUserId: "",
  canJoin: true,
});

// 🎯 Events
const emit = defineEmits<{
  "join-battle": [];
}>();

// 🎯 模态框状态管理
const showJoinModal = ref(false)
const modalLoading = ref(false)

// 🎯 计算属性：是否应该显示加入按钮
const shouldShowJoinButton = computed(() => {
  return props.canJoin && !props.isBattleStarted && !props.isUserJoined && !props.isUserCreator
})

// 🎯 处理加入对战点击
const handleJoinClick = () => {
  showJoinModal.value = true
}

// 🎯 确认加入对战
const handleConfirmJoin = async () => {
  modalLoading.value = true
  
  try {
    emit('join-battle')
    // 等待父组件处理完成
    await new Promise(resolve => setTimeout(resolve, 100))
    showJoinModal.value = false
  } catch (error) {
    console.error('[BattleEmptySlot] 加入对战失败:', error)
  } finally {
    modalLoading.value = false
  }
}

// 🎯 取消加入对战
const handleCancelJoin = () => {
  showJoinModal.value = false
  modalLoading.value = false
}
</script>

<style lang="scss" scoped>
@use "~/assets/css/components/battle-empty-slot.scss";
</style> 