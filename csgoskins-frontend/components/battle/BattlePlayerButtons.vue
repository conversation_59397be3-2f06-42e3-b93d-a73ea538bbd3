<template>
  <div class="player-buttons">
    

    <!-- 查看结果按钮 - 已完成且用户参与时显示 -->
    <button
      v-if="isBattleFinished && isUserJoined"
      @click="$emit('view-result')"
      class="view-result-btn"
    >
      <Icon name="heroicons:eye" class="w-4 h-4" />
      {{ t("battle.detail.view_result") }}
    </button>
  </div>
</template>

<script setup lang="ts">
// 🎯 国际化设置
const { t } = useI18n();

// 🎯 Props定义
interface Props {
  isBattleStarted?: boolean;
  isBattleFinished?: boolean;
  isUserCreator?: boolean;
  isUserJoined?: boolean;
  isHost?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  isBattleStarted: false,
  isBattleFinished: false,
  isUserCreator: false,
  isUserJoined: false,
  isHost: false,
});

// 🎯 Events
const emit = defineEmits<{
  "view-result": [];
}>();
</script>

<style lang="scss" scoped>
@use "~/assets/css/components/battle-player-buttons.scss";
</style> 