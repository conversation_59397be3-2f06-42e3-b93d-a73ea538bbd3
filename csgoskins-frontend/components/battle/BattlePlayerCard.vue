<template>
  <!-- 根元素增加ref和data属性，便于动画系统定位 -->
  <div
    class="player-card"
    :class="{ 
      'is-host': isHost, 
      'is-winner': isWinner, 
      'is-opening': isOpening,
      'waiting-state': !isBattleStarted && !isBattleFinished
    }"
    ref="rootEl"
    :data-player="playerName"
  >
    <!-- 等待状态下的紧凑布局 -->
    <div v-if="!isBattleStarted && !isBattleFinished" class="waiting-layout">
      <div class="compact-header">
        <div class="avatar-section">
          <img 
            :src="player.user?.profile?.avatar || '/demo/avatar1.png'" 
            :alt="player.user?.profile?.nickname"
            @error="handleImageError"
            class="player-avatar"
          />
          
          <!-- 状态徽章 -->
          <div class="player-badges">
            <div v-if="isHost" class="host-badge">
              <Icon name="heroicons:home" class="w-3 h-3" />
            </div>
            <div v-if="isWinner" class="winner-badge">
              <Icon name="heroicons:star" class="w-3 h-3" />
            </div>
            <div v-if="isOpening" class="opening-badge">
              <Icon name="heroicons:play" class="w-3 h-3" />
            </div>
          </div>

          <!-- 玩家操作按钮 -->
          <div class="player-actions">
            

            <!-- 房主解散房间按钮 - 房主且未开始时 -->
            <button
              v-if="!isBattleStarted && isCurrentUser && isHost"
              @click="handleDismissClick"
              class="action-btn dismiss-btn"
              title="解散房间"
            >
              <Icon name="heroicons:trash" class="w-4 h-4" />
            </button>

            <!-- 普通用户退出对战按钮 - 非房主且未开始时 -->
            <button
              v-if="!isBattleStarted && isCurrentUser && !isHost"
              @click="handleLeaveClick"
              class="action-btn leave-btn"
              title="退出对战"
            >
              <Icon name="heroicons:x-mark" class="w-4 h-4" />
            </button>
          </div>
        </div>

        <div class="text-section">
          <div class="player-name">{{ player.user?.profile?.nickname || `玩家${index + 1}` }}</div>
          
          <!-- 玩家状态标签 -->
          <div class="player-status-tags">
            <span v-if="isHost" class="status-tag host-tag">
              {{ t("battle.detail.host") }}
            </span>
            <span v-else class="status-tag joined-tag">
              {{ t("battle.detail.joined") }}
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- 对战进行中的完整布局 -->
    <div v-else class="battle-layout">
      <!-- 玩家头部信息 -->
      <div class="player-header">
        <div class="player-avatar-container">
          <img 
            :src="player.user?.profile?.avatar || '/demo/avatar1.png'" 
            :alt="player.user?.profile?.nickname"
            @error="handleImageError"
            class="player-avatar"
          />
          
          <!-- 状态徽章 -->
          <div class="player-badges">
            <div v-if="isHost" class="host-badge">
              <Icon name="heroicons:home" class="w-3 h-3" />
            </div>
            <div v-if="isWinner" class="winner-badge">
              <Icon name="heroicons:star" class="w-3 h-3" />
            </div>
            <div v-if="isOpening" class="opening-badge">
              <Icon name="heroicons:play" class="w-3 h-3" />
            </div>
            <div v-if="isRoundChanging" class="round-changing-badge">
              <Icon name="heroicons:arrow-path" class="w-3 h-3 animate-spin" />
            </div>
          </div>

          <!-- 玩家操作按钮 -->
          <div class="player-actions">
            

            <!-- 房主解散房间按钮 - 房主且未开始时 -->
            <button
              v-if="!isBattleStarted && isCurrentUser && isHost"
              @click="handleDismissClick"
              class="action-btn dismiss-btn"
              title="解散房间"
            >
              <Icon name="heroicons:trash" class="w-4 h-4" />
            </button>

            <!-- 普通用户退出对战按钮 - 非房主且未开始时 -->
            <button
              v-if="!isBattleStarted && isCurrentUser && !isHost"
              @click="handleLeaveClick"
              class="action-btn leave-btn"
              title="退出对战"
            >
              <Icon name="heroicons:x-mark" class="w-4 h-4" />
            </button>
          </div>
        </div>

        <div class="player-info-center">
          <div class="player-name">{{ player.user?.profile?.nickname || `玩家${index + 1}` }}</div>
        </div>
      </div>

      <!-- 开箱记录/动画区域 -->
      <div v-if="showRoundsSection" class="rounds-section">
        <div class="rounds-header">
          <Icon name="heroicons:cube" class="w-4 h-4 text-blue-400" />
          <span class="rounds-title">{{ t("battle.detail.opening_results") }}</span>
          <span class="rounds-value">￥{{ getOpenTotalValue(player).toFixed(2) }}</span>
        </div>

        <!-- 动画区域 - 始终渲染以确保动画系统能找到元素 -->
        <div class="animation-section">
          <BattleCaseAnimation
            :case-detail="currentCaseDetail"
            :case-items="getPlayerCaseItems(index)"
            :battle-player-index="index"
            :is-battle-mode="true"
            :battle-round="currentRound"
            :total-rounds="totalRounds"
            :is-player-turn="isPlayerOpening(player, index)"
            :player-id="player.user?.uid"
            :player-name="playerName"
            @animation-complete="handleAnimationComplete"
            @data-ready="handleDataReady"
          />
        </div>

        <!-- 开箱记录网格 - 随轮次实时展示开箱记录 -->
        <BattleRoundsGrid
          :key="`grid-${localOpenItems.length}-${isRoundChanging}`"
          v-if="showRoundsSection"
          :player="proxyPlayer"
          :total-rounds="totalRounds"
          :max-players="maxPlayers"
          :convert-to-skin-item="convertToSkinItem"
        />
        <!-- 获取记录区域 -->
        <BattleWinItems 
            v-if="(isBattleStarted || isBattleFinished) && getWinItems(player).length > 0"
            :player="player"
            :max-players="maxPlayers"
            :convert-to-skin-item="convertToSkinItem"
          />
      </div>
    </div>

    <!-- 确认操作模态框 -->
    <NotificationModal
      :visible="showConfirmModal"
      :type="confirmModalConfig.type"
      :title="confirmModalConfig.title"
      :subtitle="confirmModalConfig.subtitle"
      :confirm-text="confirmModalConfig.confirmText"
      :confirm-icon="confirmModalConfig.confirmIcon"
      :loading="modalLoading"
      :loading-text="confirmModalConfig.loadingText"
      @confirm="handleConfirmAction"
      @cancel="handleCancelAction"
      @close="handleCancelAction"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, provide } from 'vue'
import type { BattleBet, BattleItem } from "~/types/battle";
import { useBattleAnimation } from "~/composables/useBattleAnimation";
import BattleCaseAnimation from "~/components/battle/BattleCaseAnimation.vue";
import NotificationModal from "~/components/ui/NotificationModal.vue";
import { useBattleAnimations } from "~/composables/useBattleAnimations";

// 🎯 国际化设置
const { t } = useI18n();

// 🎯 Props定义
interface Props {
  player: BattleBet;
  index: number;
  currentRound: number;
  totalRounds: number;
  openingPlayerName?: string;
  openingPlayerIndex?: number;
  hostUid?: string;
  maxPlayers?: number;
  isBattleStarted?: boolean;
  isBattleFinished?: boolean;
  currentCaseDetail?: any;
  currentCaseItems?: any[];
  isUserCreator?: boolean;
  isUserJoined?: boolean;
  currentUserId?: string;
  // 状态同步相关
  isRoundChanging?: boolean;
  playerRecords?: ReadonlyMap<string, readonly any[]>;
  completedRounds?: number[];
}

const props = withDefaults(defineProps<Props>(), {
  openingPlayerName: "",
  openingPlayerIndex: -1,
  hostUid: "",
  maxPlayers: 4,
  isBattleStarted: false,
  isBattleFinished: false,
  currentCaseDetail: () => ({}),
  currentCaseItems: () => [],
  isUserCreator: false,
  isUserJoined: false,
  currentUserId: "",
  isRoundChanging: false,
  playerRecords: () => new Map(),
  completedRounds: () => []
});

// 🎯 Events
const emit = defineEmits<{
  "start-battle": [];
  "view-result": [];
  "leave-battle": [];
  "dismiss-battle": [];
  "animation-complete": [playerIndex: number, result: any];
}>();

// 🎯 计算属性
const isHost = computed(() => props.player.user?.uid === props.hostUid);
const isWinner = computed(() => props.player.victory);
const isOpening = computed(() => isPlayerOpening(props.player, props.index));
const isCurrentUser = computed(() => props.player.user?.uid === props.currentUserId);

// 🎯 对战动画管理器
const { isOpening: animationIsOpening } = useBattleAnimation();

// 🎯 路由导航
const router = useRouter();

// 🎯 确认模态框状态管理
const showConfirmModal = ref(false);
const modalLoading = ref(false);
const confirmActionType = ref<'leave' | 'dismiss'>('leave');

// 🎯 模态框配置
const confirmModalConfig = computed(() => {
  const isLeaving = confirmActionType.value === 'leave';
  return {
    type: 'warning' as const,
    title: isLeaving ? t('battle.confirm.leave.title') : t('battle.confirm.dismiss.title'),
    subtitle: isLeaving ? t('battle.confirm.leave.subtitle') : t('battle.confirm.dismiss.subtitle'),
    confirmText: isLeaving ? t('battle.confirm.leave.confirm') : t('battle.confirm.dismiss.confirm'),
    confirmIcon: isLeaving ? 'heroicons:x-mark' : 'heroicons:trash',
    loadingText: isLeaving ? t('battle.confirm.leave.loading') : t('battle.confirm.dismiss.loading')
  };
});

// 🎯 开箱状态检查
const isPlayerOpening = (player: BattleBet, index: number): boolean => {
  if (!props.isBattleStarted) return false

  // 🎯 关键修复：检查是否有开箱状态
  const hasOpeningState = props.openingPlayerName || 
                         (props.openingPlayerIndex !== undefined && props.openingPlayerIndex !== -1) ||
                         animationIsOpening.value;

  if (!hasOpeningState) {
    return false;
  }

  // 若openPlayer未指定(-1/空)，视为所有玩家同时开箱
  const unspecified = (!props.openingPlayerName && (props.openingPlayerIndex === -1 || props.openingPlayerIndex === undefined))

  if (unspecified) {
    console.log(`[🎰BATTLE-PLAYER] 玩家${index}开箱状态检查: 未指定玩家，所有玩家同时开箱`);
    return animationIsOpening.value;
  }

  // 否则对比指定玩家
  const candidateNames: string[] = []
  if ((player.user as any)?.username) candidateNames.push(String((player.user as any).username))
  if (player.user?.uid) candidateNames.push(String(player.user.uid))
  if (player.user?.profile?.nickname) candidateNames.push(String(player.user.profile.nickname))

  const matchedByName = props.openingPlayerName && candidateNames.includes(props.openingPlayerName)
  const matchedByIndex = props.openingPlayerIndex !== undefined && props.openingPlayerIndex === index

  const isOpening = (matchedByName || matchedByIndex) && animationIsOpening.value;
  
  console.log(`[🎰BATTLE-PLAYER] 玩家${index}开箱状态检查:`, {
    playerName: playerName.value,
    openingPlayerName: props.openingPlayerName,
    openingPlayerIndex: props.openingPlayerIndex,
    matchedByName,
    matchedByIndex,
    animationIsOpening: animationIsOpening.value,
    isOpening
  });

  return isOpening;
};

// 🎯 根据玩家索引获取对应的物品数据
const getPlayerCaseItems = (index: number): any[] => {
  const baseItems = props.currentCaseItems || [];
  
  if (!baseItems || baseItems.length === 0) {
    return [];
  }
  
  const playerItems = [...baseItems];
  
  if (index > 0) {
    const shiftAmount = index * 5;
    for (let i = 0; i < shiftAmount; i++) {
      const firstItem = playerItems.shift();
      if (firstItem) {
        playerItems.push(firstItem);
      }
    }
  }
  
  return playerItems;
};

// 🎯 工具函数
const getOpenTotalValue = (player: BattleBet): number => {
  return (player.open_items || []).reduce(
    (sum, item) => {
      // 🛡️ 防御性编程：跳过null/undefined值
      if (!item || typeof item !== 'object') {
        return sum;
      }
      return sum + (item.item_price?.price || 0);
    },
    0
  );
};

const getWinItems = (player: BattleBet): BattleItem[] => {
  return player.win_items || [];
};

// 🎯 类型适配函数
const convertToSkinItem = (battleItem: BattleItem): any => {
  return {
    id: battleItem.item_id || `battle-${Date.now()}`,
    name: battleItem.name,
    name_zh_hans: battleItem.name_zh_hans,
    name_en: battleItem.name_en,
    image: battleItem.image,
    price: battleItem.item_price?.price || 0,
    item_price: battleItem.item_price,
    item_rarity: battleItem.item_rarity,
    isStatTrak: battleItem.name?.includes("StatTrak™") || false,
    weapon: battleItem.name?.split(" | ")[0] || "",
    skin: battleItem.name?.split(" | ")[1]?.split(" (")[0] || "",
    exterior: battleItem.name?.match(/\(([^)]+)\)$/)?.[1] || "",
  };
};

// 🎯 动画完成处理
interface AnimCompletePayload {
  playerIndex?: number
  winner?: any
  result?: any
}

const handleAnimationComplete = (payload: AnimCompletePayload) => {
  const idx = payload?.playerIndex ?? props.index;

  // 提取动画结果：兼容 winner / result / 整个 payload
  const result =
    (payload?.winner as any) ?? (payload?.result as any) ?? payload;

  // 本地立即记录，UI 秒显
  const currentRoundIdx = props.currentRound > 0 ? props.currentRound : 1
  if (result) {
    setLocalOpenItem(currentRoundIdx, result as any)
  }

  // 向父组件冒泡事件，保持旧版 (playerIndex, result) 签名
  emit("animation-complete", idx, result);
};

// 🎯 数据准备状态处理
const handleDataReady = (isReady: boolean) => {
  // 关闭日志输出，专注于动画调试
  // console.log(`[🎮BATTLE-PLAYER] 玩家${props.index}数据准备状态:`, isReady);
};

// 🎯 事件处理
const handleImageError = (event: Event) => {
  const target = event.target as HTMLImageElement;
  target.src = "/demo/avatar1.png";
};

// 🎯 退出对战按钮点击
const handleLeaveClick = () => {
  console.log('[🎰PLAYER-CARD] 用户点击退出对战按钮');
  confirmActionType.value = 'leave';
  showConfirmModal.value = true;
};

// 🎯 解散房间按钮点击
const handleDismissClick = () => {
  console.log('[🎰PLAYER-CARD] 房主点击解散房间按钮');
  confirmActionType.value = 'dismiss';
  showConfirmModal.value = true;
};

// 🎯 模态框确认操作
const handleConfirmAction = () => {
  modalLoading.value = true;

  if (confirmActionType.value === 'leave') {
    // 退出对战
    console.log('[🎰PLAYER-CARD] 确认退出对战，发送leave-battle事件');
    emit('leave-battle');
  } else {
    // 解散房间
    console.log('[🎰PLAYER-CARD] 确认解散房间，发送dismiss-battle事件');
    emit('dismiss-battle');
  }

  // 发送事件后立即关闭模态框，让父组件处理后续逻辑
  modalLoading.value = false;
  showConfirmModal.value = false;
};

// 🎯 模态框取消操作
const handleCancelAction = () => {
  showConfirmModal.value = false;
  modalLoading.value = false;
};

// 🎯 根元素引用
const rootEl = ref<HTMLElement | null>(null)

// 🎯 玩家相关唯一标识集合：username、uid、nickname 三个都尝试注册
const playerIdentifiers = computed(() => {
  const identifiers: string[] = []
  const user: any = props.player.user || {}
  if (user.username) identifiers.push(String(user.username))
  if (user.uid) identifiers.push(String(user.uid))
  if (user.profile?.nickname) identifiers.push(String(user.profile.nickname))
  // 兜底：基于索引的临时id
  identifiers.push(`player-${props.index}`)
  return identifiers
})

// 便于模板直接使用昵称或username显示
const playerName = computed(() => playerIdentifiers.value[0])

// 🎯 注册到动画系统
const { registerPlayerElement, unregisterPlayerElement } = useBattleAnimations()



onMounted(() => {
  if (rootEl.value) {
    // 同时注册所有可能的标识符，避免后端消息字段不一致导致无法匹配
    playerIdentifiers.value.forEach(id => registerPlayerElement(id, rootEl.value as HTMLElement))
  }
})

onBeforeUnmount(() => {
  playerIdentifiers.value.forEach(id => unregisterPlayerElement(id))
})

// 🎯 开箱记录显示条件：对战已开始 或 玩家已有开箱掉落记录
const showRoundsSection = computed(() => {
  const hasOpenedItems = Array.isArray(props.player.open_items) && props.player.open_items.length > 0;
  return props.isBattleStarted || hasOpenedItems;
});

// ================= 即时掉落本地状态 =================
// 说明：直接依赖父级 battleData 更新，会因服务端推送延迟导致网格迟钝。
// 在玩家卡片内部维护一份 localOpenItems：
// 1) 动画完成立即写入 -> UI 秒显；
// 2) 监听 props.player.open_items，同步服务端最终结果。

const localOpenItems = ref<(BattleItem | null)[]>([...props.player.open_items ?? []])

// 🎯 向子组件提供 localOpenItems 数据
provide('localOpenItems', localOpenItems)

// 同步服务端推送（props 变化）
watch(() => props.player.open_items, (newVal) => {
  if (Array.isArray(newVal)) {
    localOpenItems.value = [...newVal]
  }
}, { deep: true })

// 助手：写入指定轮次掉落，本地秒显
const setLocalOpenItem = (roundIndex: number, item: BattleItem) => {
  const updated = [...localOpenItems.value]
  // 🛡️ 通过下标直接写入，长度不够时用 null 占位，避免推入重复数据
  while (updated.length < roundIndex) {
    updated.push(null)
  }
  updated[roundIndex - 1] = item
  localOpenItems.value = updated
}

// 代理 player，替换 open_items 引用 -> 交给 BattleRoundsGrid 渲染
const proxyPlayer = computed<BattleBet>(() => {
  // 🛡️ 过滤掉 null 值，但在 BattleRoundsGrid 中处理索引问题
  const filteredOpenItems = localOpenItems.value.filter((item): item is BattleItem => item !== null)
  return { ...props.player, open_items: filteredOpenItems }
})
</script>

<style lang="scss" scoped>
@use "~/assets/css/components/battle-player-card.scss";
</style> 