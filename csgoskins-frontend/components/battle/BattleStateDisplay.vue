<template>
  <div class="battle-state-display">
    

    <!-- 主状态区域 -->
    <div class="state-main">
      <div class="state-info">
        <!-- 状态图标 -->
        <div class="state-icon" :class="currentStateConfig.bgColor">
          <Icon :name="currentStateConfig.icon" class="w-6 h-6" :class="currentStateConfig.color" />
        </div>
        
        <!-- 状态文字 -->
        <div class="state-text">
          <h3 class="state-title">{{ t(currentStateConfig.title) }}</h3>
          <p class="state-description">{{ t(currentStateConfig.description) }}</p>
        </div>
        
        <!-- 状态徽章 -->
        <div class="state-badge" :class="currentStateConfig.badgeClass">
          {{ t(currentStateConfig.badgeText) }}
        </div>
      </div>
    </div>

    <!-- 进度区域 -->
    <div v-if="isBattleStarted" class="progress-section">
      <!-- 轮次指示器 -->
      <div class="round-indicator">
        <div class="round-header">
          <span class="round-text">{{ t('battle.detail.round_progress') }}</span>
          <span class="round-count">{{ currentRound }} / {{ totalRounds }}</span>
        </div>
        
        <!-- 进度条 -->
        <div class="progress-bar">
          <div 
            class="progress-fill"
            :style="{ width: progressPercentage + '%' }"
            :class="{ 'animate': isProgressAnimating }"
          ></div>
        </div>
        
        <!-- 轮次状态指示器 -->
        <div class="round-dots">
          <div 
            v-for="round in totalRounds" 
            :key="round"
            class="round-dot"
            :class="{
              'completed': round < currentRound,
              'current': round === currentRound,
              'pending': round > currentRound
            }"
          >
            <Icon 
              v-if="round < currentRound" 
              name="heroicons:check" 
              class="w-3 h-3" 
            />
            <span v-else class="dot-number">{{ round }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 额外信息区域 -->
    <div v-if="showExtraInfo" class="extra-info">
      <!-- 当前开箱信息 -->
      <div v-if="openingCaseId" class="opening-info">
        <Icon name="heroicons:cube" class="w-4 h-4" />
        <span class="opening-text">{{ t('battle.detail.opening_case') }}</span>
        <span class="case-id">{{ openingCaseId }}</span>
      </div>
      
      <!-- 下一步提示 -->
      <div v-if="false" class="next-step">
        <Icon name="heroicons:arrow-right" class="w-4 h-4" />
        <span class="next-step-text">测试文本</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 🎯 国际化设置
const { t } = useI18n()

// 🧪 Socket测试变量
const testMessage = ref('等待Socket事件...')
const testTimestamp = ref(new Date().toLocaleTimeString())
const socketConnected = ref(false)

// 🎯 获取Socket Store
const socketStore = useSocketStore()

// 🎯 Props定义（与页面使用方式完全匹配）
interface Props {
  battleState: string           // battleCore.battleState.battleState.value
  currentRound: number          // battleCore.battleState.currentRound.value
  totalRounds: number          // battleCore.battleState.totalRounds.value
  openingCaseId?: string       // getOpeningCaseId 计算属性
  isBattleStarted?: boolean    // 对战是否已开始
  isBattleFinished?: boolean   // 对战是否已结束
}

const props = withDefaults(defineProps<Props>(), {
  openingCaseId: '',
  isBattleStarted: false,
  isBattleFinished: false
})

// 🎯 完整的状态配置
const stateConfig = {
  loading: {
    icon: 'heroicons:arrow-path',
    color: 'text-gray-400',
    bgColor: 'bg-gray-500/20',
    badgeClass: 'bg-gray-500 text-gray-900',
    title: 'battle.state.loading.title',
    description: 'battle.state.loading.description',
    badgeText: 'battle.state.loading.badge'
  },
  waiting: {
    icon: 'heroicons:users',
    color: 'text-yellow-400',
    bgColor: 'bg-yellow-500/20',
    badgeClass: 'bg-yellow-500 text-yellow-900',
    title: 'battle.state.waiting.title',
    description: 'battle.state.waiting.description',
    badgeText: 'battle.state.waiting.badge'
  },
  preparing: {
    icon: 'heroicons:cog',
    color: 'text-blue-400',
    bgColor: 'bg-blue-500/20',
    badgeClass: 'bg-blue-500 text-blue-900',
    title: 'battle.state.preparing.title',
    description: 'battle.state.preparing.description',
    badgeText: 'battle.state.preparing.badge'
  },
  countdown: {
    icon: 'heroicons:stopwatch',
    color: 'text-orange-400',
    bgColor: 'bg-orange-500/20',
    badgeClass: 'bg-orange-500 text-orange-900',
    title: 'battle.state.countdown.title',
    description: 'battle.state.countdown.description',
    badgeText: 'battle.state.countdown.badge'
  },
  battle: {
    icon: 'heroicons:play',
    color: 'text-green-400',
    bgColor: 'bg-green-500/20',
    badgeClass: 'bg-green-500 text-green-900',
    title: 'battle.state.battle.title',
    description: 'battle.state.battle.description',
    badgeText: 'battle.state.battle.badge'
  },
  calculating: {
    icon: 'heroicons:calculator',
    color: 'text-purple-400',
    bgColor: 'bg-purple-500/20',
    badgeClass: 'bg-purple-500 text-purple-900',
    title: 'battle.state.calculating.title',
    description: 'battle.state.calculating.description',
    badgeText: 'battle.state.calculating.badge'
  },
  finished: {
    icon: 'heroicons:trophy',
    color: 'text-yellow-400',
    bgColor: 'bg-yellow-500/20',
    badgeClass: 'bg-yellow-500 text-yellow-900',
    title: 'battle.state.finished.title',
    description: 'battle.state.finished.description',
    badgeText: 'battle.state.finished.badge'
  },
  completed: {
    icon: 'heroicons:check-circle',
    color: 'text-green-400',
    bgColor: 'bg-green-500/20',
    badgeClass: 'bg-green-500 text-green-900',
    title: 'battle.state.completed.title',
    description: 'battle.state.completed.description',
    badgeText: 'battle.state.completed.badge'
  },
  cancelled: {
    icon: 'heroicons:x-circle',
    color: 'text-red-400',
    bgColor: 'bg-red-500/20',
    badgeClass: 'bg-red-500 text-red-900',
    title: 'battle.state.cancelled.title',
    description: 'battle.state.cancelled.description',
    badgeText: 'battle.state.cancelled.badge'
  }
}

// 🎯 计算属性
const currentStateConfig = computed(() => {
  return stateConfig[props.battleState as keyof typeof stateConfig] || stateConfig.waiting
})

const progressPercentage = computed(() => {
  if (!props.isBattleStarted || props.totalRounds === 0) return 0
  return Math.min((props.currentRound / props.totalRounds) * 100, 100)
})

const isProgressAnimating = computed(() => {
  return ['battle', 'countdown', 'calculating'].includes(props.battleState)
})

const showExtraInfo = computed(() => {
  return props.openingCaseId || ['waiting', 'loading', 'preparing'].includes(props.battleState)
})

// 🧪 Socket测试事件监听
onMounted(() => {
  const handleTestUpdate = (event: Event) => {
    const customEvent = event as CustomEvent
    console.log('[🧪BATTLE-STATE] 收到Socket测试事件:', customEvent.detail)
    testMessage.value = `Socket事件: ${customEvent.detail.eventName}`
    testTimestamp.value = customEvent.detail.timestamp
  }
  
  // 直接从Socket Store获取连接状态
  const updateSocketStatus = () => {
    socketConnected.value = socketStore.isConnected
    console.log('[🧪BATTLE-STATE] Socket连接状态更新:', socketConnected.value)
  }
  
  // 初始状态
  updateSocketStatus()
  
  // 监听Socket Store状态变化
  watch(() => socketStore.isConnected, (newStatus) => {
    console.log('[🧪BATTLE-STATE] Socket Store状态变化:', newStatus)
    socketConnected.value = newStatus
  })
  
  window.addEventListener('socket:test-update', handleTestUpdate)
  
  // 清理事件监听器
  onUnmounted(() => {
    window.removeEventListener('socket:test-update', handleTestUpdate)
  })
})
</script>

<style lang="scss" scoped>
// 🎯 对战状态显示容器
.battle-state-display {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: var(--radius-lg, 0.75rem);
  padding: 1.5rem;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

// 🎯 主状态区域
.state-main {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.state-info {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex: 1;
}

.state-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 3rem;
  height: 3rem;
  border-radius: var(--radius-lg, 0.75rem);
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(5px);
}

.state-text {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  flex: 1;
}

.state-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
}

.state-description {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
}

.state-badge {
  padding: 0.375rem 0.75rem;
  border-radius: var(--radius-full, 9999px);
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  white-space: nowrap;
}

// 🎯 进度区域
.progress-section {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 1.5rem;
}

.round-indicator {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.round-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.round-text {
  font-size: 0.875rem;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.8);
}

.round-count {
  font-size: 0.875rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  background: rgba(255, 255, 255, 0.1);
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius-sm, 0.25rem);
}

.progress-bar {
  width: 100%;
  height: 0.5rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-full, 9999px);
  overflow: hidden;
  position: relative;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #3b82f6, #60a5fa);
  border-radius: var(--radius-full, 9999px);
  transition: width 0.5s ease;
  position: relative;

  &.animate {
    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
      animation: shimmer 1.5s infinite;
    }
  }
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.round-dots {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.round-dot {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  font-size: 0.75rem;
  font-weight: 600;
  transition: all 0.3s ease;
  position: relative;

  &.completed {
    background: rgba(34, 197, 94, 0.2);
    border: 2px solid rgba(34, 197, 94, 0.4);
    color: #4ade80;
  }

  &.current {
    background: rgba(59, 130, 246, 0.2);
    border: 2px solid rgba(59, 130, 246, 0.6);
    color: #60a5fa;
    transform: scale(1.1);
    box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
  }

  &.pending {
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.2);
    color: rgba(255, 255, 255, 0.6);
  }

  .dot-number {
    font-size: 0.75rem;
    font-weight: 600;
  }
}

// 🎯 额外信息区域
.extra-info {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.opening-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-md, 0.375rem);
}

.opening-text {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.8);
}

.case-id {
  font-size: 0.875rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  background: rgba(255, 255, 255, 0.1);
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius-sm, 0.25rem);
}

.next-step {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: var(--radius-md, 0.375rem);
}

.next-step-text {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.8);
}

// 🎯 响应式设计
@media (max-width: 768px) {
  .battle-state-display {
    padding: 1rem;
    gap: 1rem;
  }

  .state-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }

  .state-main {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .round-dots {
    gap: 0.375rem;
  }

  .round-dot {
    width: 1.75rem;
    height: 1.75rem;
  }
}

// 🎯 状态颜色配置
.bg-gray-500\/20 {
  background: rgba(107, 114, 128, 0.2);
}

.bg-yellow-500\/20 {
  background: rgba(245, 158, 11, 0.2);
}

.bg-blue-500\/20 {
  background: rgba(59, 130, 246, 0.2);
}

.bg-orange-500\/20 {
  background: rgba(249, 115, 22, 0.2);
}

.bg-green-500\/20 {
  background: rgba(34, 197, 94, 0.2);
}

.bg-purple-500\/20 {
  background: rgba(168, 85, 247, 0.2);
}

.bg-red-500\/20 {
  background: rgba(239, 68, 68, 0.2);
}

.text-gray-400 {
  color: #9ca3af;
}

.text-yellow-400 {
  color: #fbbf24;
}

.text-blue-400 {
  color: #60a5fa;
}

.text-orange-400 {
  color: #fb923c;
}

.text-green-400 {
  color: #4ade80;
}

.text-purple-400 {
  color: #a78bfa;
}

.text-red-400 {
  color: #f87171;
}

.bg-gray-500 {
  background: #6b7280;
}

.bg-yellow-500 {
  background: #eab308;
}

.bg-blue-500 {
  background: #3b82f6;
}

.bg-orange-500 {
  background: #f97316;
}

.bg-green-500 {
  background: #22c55e;
}

.bg-purple-500 {
  background: #a855f7;
}

.bg-red-500 {
  background: #ef4444;
}

.text-gray-900 {
  color: #111827;
}

.text-yellow-900 {
  color: #713f12;
}

.text-blue-900 {
  color: #1e3a8a;
}

.text-orange-900 {
  color: #7c2d12;
}

.text-green-900 {
  color: #14532d;
}

.text-purple-900 {
  color: #581c87;
}

.text-red-900 {
  color: #7f1d1d;
}
</style> 