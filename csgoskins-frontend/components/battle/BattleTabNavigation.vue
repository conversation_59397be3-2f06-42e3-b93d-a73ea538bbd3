<template>
  <div class="tab-navigation mb-8">
    <div class="flex items-center justify-between mb-6">
      <div class="flex space-x-1 bg-gray-800/50 backdrop-blur-xl border border-gray-600/30 rounded-xl p-1">
        <button
          @click="$emit('update:activeTab', 'active')"
          :class="[
            'px-6 py-3 rounded-lg font-medium transition-all duration-300 relative',
            activeTab === 'active' 
              ? 'bg-gradient-to-r from-blue-500 to-blue-600 text-white shadow-lg' 
              : 'text-white/60 hover:text-white/80 hover:bg-gray-700/50'
          ]"
        >
          <i class="fas fa-play text-sm mr-2"></i>
          {{ t('battle.active_battles') }}
          <span v-if="activeCount > 0" class="ml-2 px-2 py-1 bg-blue-400/20 rounded-full text-xs">
            {{ activeCount }}
          </span>
        </button>
        
        <button
          @click="$emit('update:activeTab', 'my')"
          :class="[
            'px-6 py-3 rounded-lg font-medium transition-all duration-300 relative',
            activeTab === 'my' 
              ? 'bg-gradient-to-r from-green-500 to-green-600 text-white shadow-lg' 
              : 'text-white/60 hover:text-white/80 hover:bg-gray-700/50'
          ]"
        >
          <i class="fas fa-user text-sm mr-2"></i>
          {{ t('battle.my_battles') }}
        </button>
        
        <button
          @click="$emit('update:activeTab', 'history')"
          :class="[
            'px-6 py-3 rounded-lg font-medium transition-all duration-300 relative',
            activeTab === 'history' 
              ? 'bg-gradient-to-r from-purple-500 to-purple-600 text-white shadow-lg' 
              : 'text-white/60 hover:text-white/80 hover:bg-gray-700/50'
          ]"
        >
          <i class="fas fa-history text-sm mr-2"></i>
          {{ t('battle.history_battles') }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const { t } = useI18n()

defineProps<{
  activeTab: string
  activeCount: number
}>()

defineEmits<{
  'update:activeTab': [tab: string]
}>()
</script> 