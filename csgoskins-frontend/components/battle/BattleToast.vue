<template>
  <Transition name="toast" appear>
    <div 
      v-if="isVisible"
      :class="['battle-toast', `toast-${type}`]"
      @mouseenter="pauseTimer"
      @mouseleave="resumeTimer"
    >
      <!-- 图标区域 -->
      <div class="toast-icon">
        <Icon :name="icon" class="w-5 h-5" />
      </div>
      
      <!-- 内容区域 -->
      <div class="toast-content">
        <div class="toast-message">{{ message }}</div>
        
        <!-- 进度条 -->
        <div class="toast-progress">
          <div 
            class="progress-fill"
            :style="{ width: progressPercentage + '%' }"
          ></div>
        </div>
      </div>
      
      <!-- 关闭按钮 -->
      <button 
        class="toast-close"
        @click="closeToast"
        :aria-label="t('common.close')"
      >
        <Icon name="heroicons:x-mark" class="w-4 h-4" />
      </button>
      
      <!-- 背景动画 -->
      <div class="toast-background">
        <div class="bg-pattern"></div>
      </div>
    </div>
  </Transition>
</template>

<script setup lang="ts">
// 🎯 国际化设置
const { t } = useI18n()

// 🎯 Props定义（与页面使用方式完全匹配）
interface Props {
  message: string              // battleCore.battleActions.toastMessage.value
  type: string                // battleCore.battleActions.toastType.value
  icon: string                // getToastIcon 计算属性
}

const props = defineProps<Props>()

// 🎯 Emits定义
interface BattleToastEmits {
  close: []           // handleCloseToast
}

const emit = defineEmits<BattleToastEmits>()

// 🎯 响应式状态
const isVisible = ref(true)
const timeLeft = ref(5000) // 5秒
const isPaused = ref(false)

// 🎯 计算属性
const progressPercentage = computed(() => {
  return (timeLeft.value / 5000) * 100
})

// 🎯 定时器管理
let timer: NodeJS.Timeout | null = null

// 🎯 开始计时器
const startTimer = () => {
  if (timer) clearInterval(timer)
  
  timer = setInterval(() => {
    if (!isPaused.value) {
      timeLeft.value -= 100
      if (timeLeft.value <= 0) {
        closeToast()
      }
    }
  }, 100)
}

// 🎯 暂停计时器
const pauseTimer = () => {
  isPaused.value = true
}

// 🎯 恢复计时器
const resumeTimer = () => {
  isPaused.value = false
}

// 🎯 关闭Toast
const closeToast = () => {
  isVisible.value = false
  if (timer) {
    clearInterval(timer)
    timer = null
  }
  
  // 等待动画完成后触发close事件
  setTimeout(() => {
    emit('close')
  }, 300)
}

// 🎯 生命周期
onMounted(() => {
  startTimer()
})

onBeforeUnmount(() => {
  if (timer) {
    clearInterval(timer)
    timer = null
  }
})
</script>

<style lang="scss" scoped>
.battle-toast {
  position: fixed;
  top: 1rem;
  right: 1rem;
  max-width: 400px;
  min-width: 300px;
  z-index: 1000;
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  padding: 1rem;
  border-radius: var(--radius-lg);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
  }
  
  // 成功类型
  &.toast-success {
    background: rgba(34, 197, 94, 0.95);
    border-left: 4px solid #10b981;
    
    .toast-icon {
      color: #10b981;
    }
    
    .progress-fill {
      background: #10b981;
    }
    
    .bg-pattern {
      background: linear-gradient(45deg, transparent, rgba(16, 185, 129, 0.1), transparent);
    }
  }
  
  // 错误类型
  &.toast-error {
    background: rgba(239, 68, 68, 0.95);
    border-left: 4px solid #ef4444;
    
    .toast-icon {
      color: #ef4444;
    }
    
    .progress-fill {
      background: #ef4444;
    }
    
    .bg-pattern {
      background: linear-gradient(45deg, transparent, rgba(239, 68, 68, 0.1), transparent);
    }
  }
  
  // 警告类型
  &.toast-warning {
    background: rgba(251, 191, 36, 0.95);
    border-left: 4px solid #f59e0b;
    
    .toast-icon {
      color: #f59e0b;
    }
    
    .progress-fill {
      background: #f59e0b;
    }
    
    .bg-pattern {
      background: linear-gradient(45deg, transparent, rgba(245, 158, 11, 0.1), transparent);
    }
  }
  
  // 信息类型
  &.toast-info {
    background: rgba(59, 130, 246, 0.95);
    border-left: 4px solid #3b82f6;
    
    .toast-icon {
      color: #3b82f6;
    }
    
    .progress-fill {
      background: #3b82f6;
    }
    
    .bg-pattern {
      background: linear-gradient(45deg, transparent, rgba(59, 130, 246, 0.1), transparent);
    }
  }
}

.toast-icon {
  flex-shrink: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  backdrop-filter: blur(5px);
}

.toast-content {
  flex: 1;
  min-width: 0;
}

.toast-message {
  color: var(--color-white);
  font-size: 0.875rem;
  font-weight: 500;
  line-height: 1.4;
  margin-bottom: 0.5rem;
  word-wrap: break-word;
}

.toast-progress {
  width: 100%;
  height: 2px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 1px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  border-radius: 1px;
  transition: width 0.1s linear;
  position: relative;
  
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: progressShimmer 2s infinite;
  }
}

.toast-close {
  flex-shrink: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 50%;
  color: var(--color-white);
  cursor: pointer;
  transition: all 0.2s ease;
  backdrop-filter: blur(5px);
  
  &:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
  }
  
  &:active {
    transform: scale(0.95);
  }
}

.toast-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  overflow: hidden;
}

.bg-pattern {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  animation: patternMove 3s ease-in-out infinite;
}

// 动画定义
@keyframes progressShimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes patternMove {
  0%, 100% {
    opacity: 0.3;
    transform: translateX(-100%) translateY(-100%);
  }
  50% {
    opacity: 0.6;
    transform: translateX(100%) translateY(100%);
  }
}

// 进入/离开动画
.toast-enter-active,
.toast-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.toast-enter-from {
  opacity: 0;
  transform: translateX(100%) translateY(-20px);
}

.toast-leave-to {
  opacity: 0;
  transform: translateX(100%) translateY(-20px);
}

// 响应式设计
@media (max-width: 768px) {
  .battle-toast {
    top: 1rem;
    left: 1rem;
    right: 1rem;
    max-width: none;
    min-width: auto;
  }
  
  .toast-enter-from {
    transform: translateY(-100%) translateX(0);
  }
  
  .toast-leave-to {
    transform: translateY(-100%) translateX(0);
  }
}

@media (max-width: 480px) {
  .battle-toast {
    padding: 0.75rem;
    gap: 0.5rem;
  }
  
  .toast-message {
    font-size: 0.8rem;
  }
  
  .toast-icon,
  .toast-close {
    width: 20px;
    height: 20px;
  }
}
</style> 