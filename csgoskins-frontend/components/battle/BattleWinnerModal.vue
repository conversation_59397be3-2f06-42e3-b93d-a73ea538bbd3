<!-- components/battle/BattleWinnerModal.vue -->
<template>
  <Teleport to="body">
    <Transition
      enter-active-class="transition-all duration-500 ease-out"
      enter-from-class="opacity-0 scale-95"
      enter-to-class="opacity-100 scale-100"
      leave-active-class="transition-all duration-300 ease-in"
      leave-from-class="opacity-100 scale-100"
      leave-to-class="opacity-0 scale-95"
    >
      <div
        v-if="visible"
        class="fixed inset-0 z-[99999] flex items-center justify-center p-4"
        @click.self="handleClose"
      >
        <!-- 背景遮罩 -->
        <div class="absolute inset-0 bg-black/80 backdrop-blur-md"></div>
        
        <!-- 动态背景装饰 -->
        <div class="absolute inset-0 overflow-hidden pointer-events-none">
          <!-- 网格背景 -->
          <div class="absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.02)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.02)_1px,transparent_1px)] bg-[size:50px_50px]"></div>
          
          <!-- 彩纸下落粒子效果 -->
          <div class="confetti-container">
            <div 
              v-for="i in 50" 
              :key="`confetti-${i}`"
              class="confetti"
              :style="getConfettiStyle(i)"
            ></div>
          </div>
          
          <!-- 动态光球效果 -->
          <div
            v-for="i in 8"
            :key="`orb-${i}`"
            class="absolute w-4 h-4 bg-gradient-to-r from-yellow-400/30 to-orange-400/30 rounded-full blur-sm animate-pulse"
            :style="{
              left: Math.random() * 100 + '%',
              top: Math.random() * 100 + '%',
              animationDelay: Math.random() * 3 + 's',
              animationDuration: (2 + Math.random() * 3) + 's',
            }"
          ></div>
          
          <!-- 庆祝粒子效果 -->
          <div
            v-for="i in 30"
            :key="`particle-${i}`"
            class="absolute w-2 h-2 bg-gradient-to-r from-yellow-400 to-orange-400 rounded-full celebration-particle"
            :style="getCelebrationParticleStyle(i)"
          ></div>
          
          <!-- 星星闪烁效果 -->
          <div
            v-for="i in 15"
            :key="`star-${i}`"
            class="absolute text-yellow-400 sparkle-star"
            :style="getSparkleStyle(i)"
          >✨</div>
          
          <!-- 光束效果 -->
          <div class="absolute top-1/2 left-0 w-full h-px bg-gradient-to-r from-transparent via-yellow-400/50 to-transparent animate-pulse" style="animation-delay: 0.5s"></div>
          <div class="absolute top-1/3 left-0 w-full h-px bg-gradient-to-r from-transparent via-orange-400/30 to-transparent animate-pulse" style="animation-delay: 1s"></div>
          <div class="absolute top-2/3 left-0 w-full h-px bg-gradient-to-r from-transparent via-yellow-400/30 to-transparent animate-pulse" style="animation-delay: 1.5s"></div>
        </div>

        <!-- 主弹窗内容 -->
        <div class="relative w-full max-w-5xl transform transition-all duration-500">
          <div class="relative bg-gradient-to-br from-gray-900/95 via-gray-800/95 to-gray-900/95 backdrop-blur-xl border border-yellow-400/30 rounded-2xl shadow-2xl overflow-hidden">
            <!-- 装饰性背景 -->
            <div class="absolute inset-0 overflow-hidden pointer-events-none">
              <div class="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-yellow-500/5 to-orange-500/5"></div>
              <div class="absolute -top-20 -right-20 w-40 h-40 bg-gradient-to-br from-yellow-500/10 to-transparent rounded-full blur-xl"></div>
              <div class="absolute -bottom-20 -left-20 w-40 h-40 bg-gradient-to-br from-orange-500/10 to-transparent rounded-full blur-xl"></div>
            </div>

            <!-- 关闭按钮 -->
            <button
              @click="handleClose"
              class="absolute top-4 right-4 w-10 h-10 bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 rounded-full flex items-center justify-center transition-all duration-300 hover:scale-110 shadow-lg z-20 group"
            >
              <Icon name="material-symbols:close" class="w-6 h-6 text-white group-hover:rotate-90 transition-transform duration-300" />
            </button>

            <!-- 内容区域 -->
            <div class="relative z-10 p-6">
              <!-- 胜利者信息 -->
              <div class="text-center mb-8">
                <!-- 胜利标题 -->
                <div class="mb-6">
                  <h2 class="text-4xl font-bold mb-3 bg-gradient-to-r from-yellow-400 via-orange-400 to-yellow-400 bg-clip-text text-transparent">
                    🏆 恭喜获胜！
                  </h2>
                  <div class="w-32 h-1 bg-gradient-to-r from-yellow-400 to-orange-400 mx-auto rounded-full"></div>
                </div>
                
                <!-- 获胜者信息卡片 -->
                <div class="bg-gray-800/80 backdrop-blur-sm border border-gray-600/50 rounded-2xl p-6 shadow-2xl max-w-md mx-auto">
                  <!-- 头像区域 -->
                  <div class="mb-4 flex justify-center">
                    <div class="relative w-20 h-20 flex items-center justify-center">
                      <!-- 渐变描边+发光特效 -->
                      <span class="absolute inset-0 rounded-full pointer-events-none"
                        style="box-shadow: 0 0 8px 2px rgba(251,191,36,0.5), 0 0 0 2px #fbbf24; background: conic-gradient(from 0deg, #fbbf24, #f59e42, #fbbf24); opacity: 0.7; animation: avatar-glow 2s infinite alternate;"
                      ></span>
                      <div class="relative w-20 h-20 rounded-full overflow-hidden bg-gray-700">
                        <img 
                          :src="winner?.avatar" 
                          :alt="winner?.nickname"
                          class="w-full h-full object-cover"
                          @error="handleImageError"
                        />
                      </div>
                    </div>
                  </div>
                  
                  <!-- 用户信息 -->
                  <div class="text-center">
                    <h3 class="text-2xl font-bold text-white mb-4">{{ winner?.nickname }}</h3>
                    
                    <!-- 总价值展示 -->
                    <div class="bg-gray-700/60 border border-gray-600/50 rounded-xl p-4">
                      <div class="text-3xl font-bold bg-gradient-to-r from-yellow-400 to-orange-400 bg-clip-text text-transparent">
                        ${{ formatNumber(getRewardTotalValue(winner)) }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              
              <!-- 奖励饰品展示 -->
              <div v-if="winner?.rewardItems?.length">
                <div class="text-center mb-6">
                  <h4 class="text-2xl font-bold text-white mb-2 flex items-center justify-center gap-2">
                    <Icon name="material-symbols:card-giftcard" class="w-6 h-6 text-yellow-400" />
                    获得的奖励饰品
                  </h4>
                </div>
                
                <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
                  <SkinCard
                    v-for="item in winner.rewardItems"
                    :key="item.id"
                    :skin-item="item"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup lang="ts">
import type { PropType } from 'vue'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  winner: {
    type: Object as PropType<{
      id?: number;
      nickname?: string;
      avatar?: string;
      totalValue?: number;
      rewardItems?: Array<{
        id: string;
        name: string;
        name_zh_hans?: string;
        image: string;
        price: number;
        isStatTrak?: boolean;
        exterior?: string;
        rarity_name_zh_hans?: string;
        rarity_color?: string;
      }>;
      openingHistory?: Array<any>;
    }>,
    default: null
  }
});

// Emits
const emit = defineEmits(['close']);

// 方法
const handleClose = () => {
  console.log("[🎰MODAL] 关闭胜利者弹窗");
  emit('close');
};

const formatNumber = (num: number) => {
  return new Intl.NumberFormat('en-US', {
    minimumFractionDigits: 0,
    maximumFractionDigits: 2
  }).format(num || 0);
};

const getRewardTotalValue = (winner: any) => {
  if (!winner?.rewardItems) return 0;
  return winner.rewardItems.reduce((total: number, item: any) => total + (Number(item.price) || 0), 0);
};

const handleImageError = (event: Event) => {
  const target = event.target as HTMLImageElement;
  target.src = '/images/item-placeholder.svg';
};

// 彩纸下落样式生成
const getConfettiStyle = (index: number) => {
  const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57', '#ff9ff3', '#fbbf24', '#f59e0b'];
  const color = colors[index % colors.length];
  const left = Math.random() * 100;
  const animationDelay = Math.random() * 3;
  const animationDuration = 2 + Math.random() * 3;
  const size = 8 + Math.random() * 8;
  
  return {
    left: `${left}%`,
    backgroundColor: color,
    animationDelay: `${animationDelay}s`,
    animationDuration: `${animationDuration}s`,
    width: `${size}px`,
    height: `${size}px`,
  };
};

// 庆祝粒子样式生成
const getCelebrationParticleStyle = (index: number) => {
  const colors = ['#fbbf24', '#f59e0b', '#ff6b6b', '#4ade80', '#3b82f6', '#8b5cf6'];
  const color = colors[index % colors.length];
  const left = Math.random() * 100;
  const top = Math.random() * 100;
  const animationDelay = Math.random() * 2;
  const animationDuration = 1 + Math.random() * 2;
  
  return {
    left: `${left}%`,
    top: `${top}%`,
    backgroundColor: color,
    animationDelay: `${animationDelay}s`,
    animationDuration: `${animationDuration}s`,
  };
};

// 星星闪烁样式生成
const getSparkleStyle = (index: number) => {
  const left = Math.random() * 100;
  const top = Math.random() * 100;
  const animationDelay = Math.random() * 3;
  const animationDuration = 1.5 + Math.random() * 2;
  const fontSize = 0.8 + Math.random() * 0.6;
  
  return {
    left: `${left}%`,
    top: `${top}%`,
    animationDelay: `${animationDelay}s`,
    animationDuration: `${animationDuration}s`,
    fontSize: `${fontSize}rem`,
  };
};
</script>

<style scoped>
/* 彩纸下落动画 */
.confetti-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  pointer-events: none;
}

.confetti {
  position: absolute;
  top: -10px;
  border-radius: 2px;
  animation: confetti-fall linear infinite;
}

@keyframes confetti-fall {
  0% {
    transform: translateY(-100vh) rotate(0deg);
    opacity: 1;
  }
  100% {
    transform: translateY(100vh) rotate(720deg);
    opacity: 0;
  }
}

/* 庆祝粒子动画 */
.celebration-particle {
  animation: celebration-bounce ease-in-out infinite;
}

@keyframes celebration-bounce {
  0%, 100% {
    transform: translateY(0) scale(1);
    opacity: 0.8;
  }
  25% {
    transform: translateY(-20px) scale(1.2);
    opacity: 1;
  }
  50% {
    transform: translateY(-10px) scale(0.9);
    opacity: 0.9;
  }
  75% {
    transform: translateY(-30px) scale(1.1);
    opacity: 1;
  }
}

/* 星星闪烁动画 */
.sparkle-star {
  animation: sparkle-twinkle ease-in-out infinite;
}

@keyframes sparkle-twinkle {
  0%, 100% {
    opacity: 0.3;
    transform: scale(0.8) rotate(0deg);
  }
  25% {
    opacity: 0.8;
    transform: scale(1.2) rotate(90deg);
  }
  50% {
    opacity: 1;
    transform: scale(1.5) rotate(180deg);
  }
  75% {
    opacity: 0.6;
    transform: scale(1.1) rotate(270deg);
  }
}

/* 自定义动画 */
@keyframes victory-glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(251, 191, 36, 0.3);
  }
  50% {
    box-shadow: 0 0 40px rgba(251, 191, 36, 0.6);
  }
}

.victory-glow {
  animation: victory-glow 2s ease-in-out infinite;
}

@keyframes avatar-glow {
  0% { 
    opacity: 0.7; 
    filter: blur(0.5px); 
    transform: scale(1);
  }
  50% {
    opacity: 1; 
    filter: blur(1px);
    transform: scale(1.05);
  }
  100% { 
    opacity: 0.8; 
    filter: blur(2px);
    transform: scale(1);
  }
}

/* 自定义滚动条 */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #f59e0b, #f97316);
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #d97706, #ea580c);
}

/* 响应式优化 */
@media (max-width: 640px) {
  .grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  /* 移动端减少粒子数量以提升性能 */
  .confetti:nth-child(n+21) {
    display: none;
  }
  
  .celebration-particle:nth-child(n+16) {
    display: none;
  }
  
  .sparkle-star:nth-child(n+8) {
    display: none;
  }
}

@media (max-width: 480px) {
  .grid {
    grid-template-columns: repeat(1, 1fr);
  }
  
  /* 极小屏幕进一步减少特效 */
  .confetti:nth-child(n+11) {
    display: none;
  }
  
  .celebration-particle:nth-child(n+6) {
    display: none;
  }
  
  .sparkle-star:nth-child(n+4) {
    display: none;
  }
}
</style>
