<template>
  <div class="smooth-case-animation relative">
    <!-- 主动画区域 -->
    <div
      class="relative bg-gradient-to-br from-gray-800/90 to-gray-900/90 rounded-2xl border border-gray-700/30 backdrop-blur-md overflow-hidden"
    >
      <!-- 背景装饰 -->
      <div class="absolute inset-0 bg-grid-pattern opacity-5"></div>
      <div
        class="absolute -inset-px bg-gradient-to-r from-primary/20 via-transparent to-secondary/20 rounded-2xl"
      ></div>

      <!-- 动画轨道容器 -->
      <div class="relative py-8 px-4">
        <!-- 中奖指示线 - 增加高度 -->
        <div class="absolute left-1/2 top-8 w-1 h-60 -translate-x-1/2 z-20">
          <div
            class="h-full bg-gradient-to-b from-transparent via-cyan-400 to-transparent opacity-80"
          ></div>
        </div>

        <!-- 动画轨道 - 增加上下内边距 -->
        <div class="relative h-56 overflow-hidden py-4">
          <div
            ref="trackContainer"
            class="flex items-center h-full transition-transform duration-1000 ease-out"
            :style="{ transform: `translateX(${animationOffset}px)` }"
          >
            <!-- 动画物品卡片 -->
            <div
              v-for="(item, index) in displayItems"
              :key="`item-${index}`"
              class="flex-shrink-0 mx-2 relative group"
              :class="[
                'w-32 h-40',
                isWinnerItem(item, index) && !isAnimating ? 'winner-glow' : '',
              ]"
            >
              <!-- 物品卡片 -->
              <div
                class="relative h-full bg-gradient-to-b from-gray-700/80 to-gray-800/80 rounded-lg border-2 overflow-hidden transition-all duration-300 group-hover:scale-105"
                :style="{
                  borderColor:
                    isWinnerItem(item, index) && !isAnimating
                      ? '#00bcd4'
                      : (item.rarity_color || '#6b7280') + '80',
                }"
              >
                <!-- 稀有度指示条 - 移到底部 -->
                <div
                  class="absolute bottom-0 left-0 right-0 h-1 opacity-80"
                  :style="{ backgroundColor: item.rarity_color || '#6b7280' }"
                ></div>

                <!-- StatTrak 角标 - 参考OpenedSkinCardSimple -->
                <div
                  v-if="isStatTrakItem(item)"
                  class="absolute top-1 left-1 z-10"
                >
                  <div
                    class="bg-secondary/90 rounded-md px-2 py-1 text-xs font-bold text-white shadow-lg backdrop-blur-sm border border-secondary/50"
                  >
                    <span class="mr-1">⚡</span>ST
                  </div>
                </div>
                <!-- 顶部右侧外观信息-->
                <div class="absolute top-1 right-1 z-10">
                  <span class="text-gray-400 text-xs">
                    {{ getExteriorName(item) || getCondition(item) }}
                  </span>
                </div>

                <!-- 物品图片 - 增加顶部内边距 -->
                <div
                  class="relative h-24 flex items-center justify-center pt-8 px-2"
                >
                  <img
                    :src="item.image || '/demo/item1.png'"
                    :alt="getLocalizedItemName(item)"
                    class="max-w-full max-h-full object-contain drop-shadow-lg"
                    @error="handleImageError"
                  />
                </div>

                <!-- 物品信息 -->
                <div class="space-y-1 px-1">
                  <!-- 饰品名称 - 参考LiveOpenings显示方式 -->
                  <div
                    class="text-white text-sm font-medium truncate text-center"
                    :title="getLocalizedItemName(item)"
                  >
                    {{ getSkinName(item) }}
                  </div>

                  <!-- 品质和外价格 -->
                  <div class="flex items-center justify-between text-xs">
                    <!-- 价格信息 -->
                    <div class="text-primary text-sm font-bold">
                      ${{ formatPrice(item.price) }}
                    </div>
                    <span
                      class="font-bold whitespace-nowrap overflow-hidden text-ellipsis whitespace-nowrap pl-1"
                      :style="{ color: item.rarity_color || '#6b7280' }"
                    >
                      {{ getRarityName(item) }}
                    </span>
                  </div>
                </div>

                <!-- 获胜物品特效 -->
                <div
                  v-if="isWinnerItem(item, index) && !isAnimating"
                  class="absolute inset-0 bg-gradient-to-r from-cyan-400/20 to-blue-400/20 rounded-lg animate-pulse"
                ></div>
              </div>
            </div>
          </div>
        </div>

        <!-- 控制区域 -->
        <div class="mt-8 text-center">
          <button
            @click="startOpening"
            :disabled="isAnimating || !hasValidData"
            class="relative px-8 py-4 bg-gradient-to-r from-primary to-secondary text-white font-bold text-lg rounded-xl transition-all duration-300 hover:scale-105 hover:shadow-2xl disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100"
          >
            <span class="relative z-10">
              {{
                isAnimating
                  ? $t("cases.animation.opening")
                  : $t("cases.animation.open_case")
              }}
            </span>
            <div
              class="absolute inset-0 bg-gradient-to-r from-primary/80 to-secondary/80 rounded-xl blur-sm -z-10"
            ></div>
          </button>
        </div>
      </div>
    </div>

    <!-- 步骤提示覆盖层 - 与结果弹出框保持一致 -->
    <div
      v-if="showStepHint"
      class="fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center z-50 px-4"
    >
      <div
        class="bg-gray-900/95 rounded-2xl border border-primary/50 shadow-2xl p-8 text-center max-w-md w-full relative"
      >
        <!-- 装饰性背景 -->
        <div
          class="absolute -inset-1 bg-gradient-to-r from-primary/20 to-secondary/20 blur-xl opacity-50 rounded-2xl"
        ></div>

        <div class="relative">
          <!-- 霓虹Loading图标（来自old/v2） -->
          <div class="w-20 h-20 mx-auto mb-4 flex items-center justify-center">
            <!-- 使用通用的LoadingSpinner组件 -->
            <UiLoadingSpinner type="csgo" size="large" :show-text="false" />
          </div>

          <h2 class="text-2xl font-bold text-white mb-3">
            {{ currentStep.title }}
          </h2>
          <p class="text-gray-300 mb-4">{{ currentStep.description }}</p>

          <!-- 进度条 -->
          <div class="w-full bg-gray-700 rounded-full h-2 mb-2">
            <div
              class="bg-gradient-to-r from-primary to-secondary h-2 rounded-full transition-all duration-500"
              :style="{
                width: `${((currentStepIndex + 1) / totalSteps) * 100}%`,
              }"
            ></div>
          </div>
          <div class="text-sm text-gray-400">
            {{ currentStepIndex + 1 }} / {{ totalSteps }}
          </div>
        </div>
      </div>
    </div>

    <!-- 组件内部不再显示结果弹窗，由页面统一处理 -->

    <!-- 重置Loading提示 - 专业化设计 -->
    <div
      v-if="showResetHint"
      class="absolute inset-0 bg-black/60 backdrop-blur-md flex items-center justify-center z-20 rounded-2xl"
    >
      <div class="bg-gray-900/95 rounded-2xl border border-primary/50 shadow-2xl p-8 text-center max-w-sm w-full mx-4 relative">
        <!-- 装饰性背景 -->
        <div class="absolute -inset-1 bg-gradient-to-r from-primary/20 to-secondary/20 blur-xl opacity-50 rounded-2xl"></div>
        
        <div class="relative">
          <!-- CSGO霓虹风格Loading -->
          <div class="w-16 h-16 mx-auto mb-4 relative flex items-center justify-center">
            <UiLoadingSpinner type="csgo" size="large" :show-text="false" />
          </div>

          <!-- 标题 -->
          <h3 class="text-xl font-bold text-white mb-2">
            {{ $t("cases.animation.resetting") }}
          </h3>
          
          <!-- 描述 -->
          <p class="text-gray-300 text-sm mb-4">
            {{ $t("cases.animation.preparing_new_round") }}
          </p>

          <!-- 进度条 -->
          <div class="w-full bg-gray-700 rounded-full h-1.5 mb-2">
            <div class="bg-gradient-to-r from-primary to-secondary h-1.5 rounded-full animate-pulse" style="width: 100%;"></div>
          </div>
          
          <!-- 提示文字 -->
          <div class="text-xs text-gray-400">
            {{ $t("cases.animation.please_wait") }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, nextTick } from "vue";
import { useI18n } from "vue-i18n";
import { gsap } from "gsap";
import { caseApi } from "~/services/case-api";

// 日志前缀
const LOG_PREFIX = "[🎰SMOOTH]";

// 国际化
const { t, locale } = useI18n();

// Props
interface Props {
  selectedCase: any;
  caseItems: any[];
  demoMode?: boolean; // 新增：演示模式
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  "opening-start": [];
  "case-opened": [result: any];
}>();

// 响应式数据
const isAnimating = ref(false);
const showStepHint = ref(false);
const showResetHint = ref(false);
const displayItems = ref<any[]>([]);
const winnerItem = ref<any>(null);
const animationOffset = ref(0);
const trackContainer = ref<HTMLElement>();
const currentWinnerPosition = ref(60); // 动态中奖位置

// 步骤提示相关
const currentStepIndex = ref(0);
const totalSteps = 5;
const steps = computed(() => [
  {
    title: t("cases.animation.checking_user_status"),
    description: t("cases.animation.please_wait"),
  },
  {
    title: t("cases.animation.checking_balance"),
    description: t("cases.animation.please_wait"),
  },
  {
    title: t("cases.animation.checking_case_status"),
    description: t("cases.animation.please_wait"),
  },
  {
    title: t("cases.animation.preparing_animation"),
    description: t("cases.animation.generating_items"),
  },
  {
    title: t("cases.animation.ready_go"),
    description: t("cases.animation.please_wait"),
  },
]);

const currentStep = computed(
  () => steps.value[currentStepIndex.value] || steps.value[0]
);

// 计算属性
const hasValidData = computed(() => {
  return props.caseItems && props.caseItems.length > 0 && props.selectedCase;
});

// 工具函数 - 参考LiveOpenings组件的getLocalizedName实现
const getLocalizedName = (item: any, fieldName: string): string => {
  if (!item) return "";

  const currentLocale = locale.value === "zh-hans" ? "zh_hans" : locale.value;

  // 尝试获取本地化字段
  if (currentLocale === "zh_hans") {
    return (
      item[`${fieldName}_zh_hans`] ||
      item[`${fieldName}_zh`] ||
      item[fieldName] ||
      ""
    );
  } else if (currentLocale === "en") {
    return item[`${fieldName}_en`] || item[fieldName] || "";
  }

  return item[fieldName] || "";
};

const getLocalizedItemName = (item: any): string => {
  if (!item) return t("skins.unknown");

  // 使用通用的getLocalizedName函数
  const localizedName = getLocalizedName(item, "name");
  return localizedName || t("skins.unknown");
};

const getSkinName = (item: any): string => {
  if (!item) {
    return t("live_openings.mystery_item") || "Unknown Item";
  }

  // 获取本地化的完整名称 - 参考LiveOpenings的实现
  const localizedFullName = getLocalizedName(item, "name");
  const nameToSplit = localizedFullName || item.name || "";

  if (!nameToSplit) {
    return t("live_openings.mystery_item") || "Unknown Item";
  }

  // 使用LiveOpenings的简单分割逻辑
  const parts = nameToSplit.split(" | ");
  if (parts.length < 2) return nameToSplit;

  const skinPart = parts[1].trim();
  return skinPart.split(" (")[0].trim();
};

const getCondition = (item: any): string => {
  const fullName = getLocalizedItemName(item);
  if (!fullName) return "";

  // 提取括号内的品相信息
  const conditionMatch = fullName.match(/\(([^)]+)\)/);
  if (conditionMatch) {
    const condition = conditionMatch[1];

    // 中英文品相映射
    const conditionMap: Record<string, string> = {
      "Factory New": locale.value === "zh-hans" ? "崭新出厂" : "FN",
      "Minimal Wear": locale.value === "zh-hans" ? "略有磨损" : "MW",
      "Field-Tested": locale.value === "zh-hans" ? "久经沙场" : "FT",
      "Well-Worn": locale.value === "zh-hans" ? "破损不堪" : "WW",
      "Battle-Scarred": locale.value === "zh-hans" ? "战痕累累" : "BS",
      崭新出厂: locale.value === "zh-hans" ? "崭新出厂" : "FN",
      略有磨损: locale.value === "zh-hans" ? "略有磨损" : "MW",
      久经沙场: locale.value === "zh-hans" ? "久经沙场" : "FT",
      破损不堪: locale.value === "zh-hans" ? "破损不堪" : "WW",
      战痕累累: locale.value === "zh-hans" ? "战痕累累" : "BS",
    };

    return conditionMap[condition] || condition;
  }

  return "";
};

const getRarityName = (item: any): string => {
  if (!item) return "";

  // 使用通用的getLocalizedName函数获取稀有度名称
  const localizedRarityName = getLocalizedName(item, "rarity_name");
  if (localizedRarityName) {
    return localizedRarityName;
  }

  // 如果物品有rarity对象，尝试从中获取名称
  if (item.rarity) {
    const rarityLocalizedName = getLocalizedName(item.rarity, "name");
    if (rarityLocalizedName) {
      return rarityLocalizedName;
    }
  }

  // 回退到翻译映射
  const rarityKey = item.rarity?.toLowerCase() || "consumer";
  return t(`cases.animation.rarity.${rarityKey}`, rarityKey);
};

const getQualityName = (item: any): string => {
  if (!item) return "";

  // 使用通用的getLocalizedName函数获取品质名称
  const localizedQualityName = getLocalizedName(item, "quality_name");
  if (localizedQualityName) {
    return localizedQualityName;
  }

  // 如果物品有quality对象，尝试从中获取名称
  if (item.quality) {
    const qualityLocalizedName = getLocalizedName(item.quality, "name");
    if (qualityLocalizedName) {
      return qualityLocalizedName;
    }
  }

  return "";
};

const getExteriorName = (item: any): string => {
  if (!item) return "";

  // 使用通用的getLocalizedName函数获取外观名称
  const localizedExteriorName = getLocalizedName(item, "exterior_name");
  if (localizedExteriorName) {
    return localizedExteriorName;
  }

  // 如果物品有exterior对象，尝试从中获取名称
  if (item.exterior) {
    const exteriorLocalizedName = getLocalizedName(item.exterior, "name");
    if (exteriorLocalizedName) {
      return exteriorLocalizedName;
    }
  }

  return "";
};

const formatPrice = (price: any): string => {
  if (!price) return "0.00";
  const numPrice = typeof price === "string" ? parseFloat(price) : price;
  return numPrice.toFixed(2);
};

const isWinnerItem = (item: any, index: number): boolean => {
  if (!winnerItem.value) return false;
  return item === winnerItem.value;
};

// 检测是否是StatTrak物品 - 参考OpenedSkinCardSimple组件
const isStatTrakItem = (item: any): boolean => {
  if (!item) return false;

  // 检查多个可能的字段
  const fullName = getLocalizedItemName(item);
  return (
    fullName.includes("StatTrak™") ||
    item.is_stattrak === true ||
    item.name?.includes("StatTrak™") ||
    item.name_zh_hans?.includes("StatTrak™") ||
    item.name_en?.includes("StatTrak™")
  );
};

const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement;
  img.src = "/demo/item1.png";
};

// 动画相关函数
const generateAnimationItems = (winner: any): any[] => {
  // console.log(`${LOG_PREFIX} 生成动画物品，获胜物品:`, winner);

  const totalItems = 80;
  // 随机生成中奖位置，避免固定位置
  const winnerPosition = Math.floor(Math.random() * 20) + 55; // 在55-74之间随机

  // console.log(`${LOG_PREFIX} 随机中奖位置: ${winnerPosition}`);

  // 转换获胜物品格式
  const convertedWinner = convertApiItemFormat(winner);

  // 收集所有可用物品
  const allAvailableItems: any[] = [];
  if (props.caseItems && Array.isArray(props.caseItems)) {
    props.caseItems.forEach((rarityGroup: any) => {
      if (rarityGroup.items && Array.isArray(rarityGroup.items)) {
        rarityGroup.items.forEach((itemWrapper: any) => {
          const item = itemWrapper.item_info || itemWrapper;
          if (item) {
            // 转换为统一格式
            const convertedItem = {
              id: item.id,
              name: item.name,
              name_zh_hans: item.name_zh_hans || item.name_zh || item.name_cn,
              name_en: item.name_en || item.name,
              image: item.image,
              price: item.item_price?.price || item.price || 0,
              rarity_color:
                item.item_rarity?.rarity_color ||
                rarityGroup.rarity_color ||
                "#6b7280",
              rarity_name:
                item.item_rarity?.rarity_name || rarityGroup.rarity_name,
              rarity_name_zh_hans:
                item.item_rarity?.rarity_name_zh_hans ||
                rarityGroup.rarity_name_zh_hans,
              rarity_name_en:
                item.item_rarity?.rarity_name_en || rarityGroup.rarity_name_en,
              is_stattrak: item.name?.includes("StatTrak™") || false,
            };
            allAvailableItems.push(convertedItem);
          }
        });
      }
    });
  }

  // console.log(`${LOG_PREFIX} 收集到 ${allAvailableItems.length} 个可用物品`);

  // 先生成所有随机物品
  const randomItems: any[] = [];
  for (let i = 0; i < totalItems; i++) {
    if (allAvailableItems.length > 0) {
      const randomItem =
        allAvailableItems[Math.floor(Math.random() * allAvailableItems.length)];
      randomItems.push({ ...randomItem, id: Math.random() }); // 确保每个物品有唯一ID
    } else {
      // 如果没有可用物品，使用演示物品
      randomItems.push({
        id: Math.random(),
        name: t("skins.unknown"),
        image: "/demo/item1.png",
        price: 0,
        rarity_color: "#6b7280",
        is_stattrak: false,
      });
    }
  }

  // 将获胜物品放在随机位置
  randomItems[winnerPosition] = convertedWinner;

  // 打乱整个数组顺序（除了获胜物品位置）
  const shuffledItems = [...randomItems];
  for (let i = shuffledItems.length - 1; i > 0; i--) {
    if (i !== winnerPosition) {
      // 保持获胜物品在指定位置
      const j = Math.floor(Math.random() * i);
      if (j !== winnerPosition) {
        // 确保不与获胜物品位置交换
        [shuffledItems[i], shuffledItems[j]] = [
          shuffledItems[j],
          shuffledItems[i],
        ];
      }
    }
  }

  // 存储中奖位置供动画使用
  currentWinnerPosition.value = winnerPosition;

  console.log(
    `${LOG_PREFIX} 生成了 ${shuffledItems.length} 个动画物品，中奖位置: ${winnerPosition}`
  );
  return shuffledItems;
};

const convertApiItemFormat = (apiItem: any): any => {
  if (!apiItem) return null;

  console.log(`${LOG_PREFIX} 转换API物品数据:`, apiItem);
  console.log(`${LOG_PREFIX} API物品字段详情:`, {
    id: apiItem.id,
    uid: apiItem.uid,
    name: apiItem.name,
    nameZhHans: apiItem.nameZhHans,
    nameEn: apiItem.nameEn,
    image: apiItem.image,
    price: apiItem.price,
    rarity: apiItem.rarity,
    quality: apiItem.quality,
    exterior: apiItem.exterior,
  });

  // 根据实际API数据结构解析 (参考case-api.ts中的OpenCaseProcessedItem接口)
  const converted = {
    id: apiItem.id || apiItem.uid || Math.random(),
    name:
      apiItem.name ||
      apiItem.nameZhHans ||
      apiItem.nameEn ||
      t("skins.unknown"),
    name_zh_hans: apiItem.nameZhHans || apiItem.name_zh_hans || apiItem.name,
    name_en: apiItem.nameEn || apiItem.name_en || apiItem.name,
    image: apiItem.image || "/demo/item1.png",
    price: apiItem.price || 0,

    // 稀有度信息
    rarity_color: apiItem.rarity?.color || "#6b7280",
    rarity_name: apiItem.rarity?.name || apiItem.rarity?.nameZhHans,
    rarity_name_zh_hans:
      apiItem.rarity?.nameZhHans || apiItem.rarity?.name_zh_hans,
    rarity_name_en: apiItem.rarity?.nameEn || apiItem.rarity?.name_en,

    // 品质信息
    quality_name: apiItem.quality?.name || apiItem.quality?.nameZhHans,
    quality_name_zh_hans:
      apiItem.quality?.nameZhHans || apiItem.quality?.name_zh_hans,
    quality_name_en: apiItem.quality?.nameEn || apiItem.quality?.name_en,
    quality_color: apiItem.quality?.color,

    // 外观信息
    exterior_name: apiItem.exterior?.name || apiItem.exterior?.nameZhHans,
    exterior_name_zh_hans:
      apiItem.exterior?.nameZhHans || apiItem.exterior?.name_zh_hans,
    exterior_name_en: apiItem.exterior?.nameEn || apiItem.exterior?.name_en,
    exterior_color: apiItem.exterior?.color,

    // StatTrak检测 - 参考OpenedSkinCardSimple的逻辑
    is_stattrak:
      apiItem.name?.includes("StatTrak™") ||
      apiItem.nameZhHans?.includes("StatTrak™") ||
      apiItem.nameEn?.includes("StatTrak™") ||
      apiItem.quality?.name?.includes("StatTrak™") ||
      apiItem.quality?.nameZhHans?.includes("StatTrak™") ||
      false,
  };

  // console.log(`${LOG_PREFIX} 转换后的物品:`, converted);
  return converted;
};

const convertItemFormat = (item: any): any => {
  if (!item) return null;

  return {
    id: item.id || Math.random(),
    name: item.name || t("skins.unknown"),
    name_zh_hans: item.name_zh_hans || item.name_zh || item.name_cn,
    name_en: item.name_en || item.name,
    image: item.image || "/demo/item1.png",
    price: item.price || 0,
    rarity_color: item.rarity_color || "#6b7280",
    rarity_name: item.rarity_name,
    rarity_name_zh_hans: item.rarity_name_zh_hans,
    rarity_name_en: item.rarity_name_en,
    is_stattrak: item.is_stattrak || item.name?.includes("StatTrak™") || false,
  };
};

const performScrollAnimation = async (): Promise<void> => {
  return new Promise((resolve) => {
    // console.log(`${LOG_PREFIX} 开始滚动动画`);

    const containerWidth = trackContainer.value?.offsetWidth || 800;
    const itemWidth = 144; // 32 * 4 + 16 (w-32 + mx-2)
    const winnerPosition = currentWinnerPosition.value; // 使用动态中奖位置
    const targetOffset = -(
      winnerPosition * itemWidth -
      containerWidth / 2 +
      itemWidth / 2
    );
    const startOffset = 500;

    // console.log(`${LOG_PREFIX} 动画参数:`, {
    //   containerWidth,
    //   itemWidth,
    //   winnerPosition,
    //   targetOffset,
    //   startOffset,
    // });

    // 设置起始位置
    animationOffset.value = startOffset;

    // 使用GSAP进行平滑动画
    gsap.to(animationOffset, {
      value: targetOffset,
      duration: 8,
      ease: "power3.out",
      onComplete: () => {
        console.log(`${LOG_PREFIX} 滚动动画完成`);
        resolve();
      },
    });
  });
};

// 主要功能函数
const startOpening = async () => {
  if (isAnimating.value || !hasValidData.value) {
    console.log(
      `${LOG_PREFIX} 无法开始开箱: isAnimating=${isAnimating.value}, hasValidData=${hasValidData.value}`
    );
    return;
  }

  try {
    console.log(`${LOG_PREFIX} 开始开箱流程`);
    isAnimating.value = true;
    showStepHint.value = true;
    currentStepIndex.value = 0;

    emit("opening-start");

    // 步骤1: 检查用户状态
    await new Promise((resolve) => setTimeout(resolve, 800));
    currentStepIndex.value = 1;

    // 步骤2: 检查余额
    await new Promise((resolve) => setTimeout(resolve, 800));
    currentStepIndex.value = 2;

    // 步骤3: 检查箱子状态
    await new Promise((resolve) => setTimeout(resolve, 800));
    currentStepIndex.value = 3;

    // 步骤4: 准备动画
    await new Promise((resolve) => setTimeout(resolve, 500));

    let winnerData;

    // 检查是否为演示模式
    if (props.demoMode) {
      console.log(`${LOG_PREFIX} 演示模式：使用模拟数据`);
      
      // 从caseItems中随机选择一个物品作为获胜物品
      const allItems: any[] = [];
      props.caseItems.forEach((rarityGroup: any) => {
        if (rarityGroup.items && Array.isArray(rarityGroup.items)) {
          rarityGroup.items.forEach((itemWrapper: any) => {
            const item = itemWrapper.item_info || itemWrapper;
            if (item) {
              allItems.push(item);
            }
          });
        }
      });
      
      if (allItems.length > 0) {
        // 随机选择一个物品，但偏向稀有物品
        const randomIndex = Math.floor(Math.random() * allItems.length);
        winnerData = allItems[randomIndex];
        console.log(`${LOG_PREFIX} 演示模式获胜物品:`, winnerData);
      } else {
        // 生成一个默认的演示物品
        winnerData = {
          id: 'demo-winner',
          name: '演示获胜物品',
          name_zh_hans: '演示获胜物品',
          name_en: 'Demo Winner Item',
          image: '/demo/item1.png',
          item_price: { price: 50 },
          item_rarity: {
            rarity_name: '稀有',
            rarity_name_zh_hans: '稀有',
            rarity_name_en: 'Rare',
            rarity_color: '#3b82f6'
          },
          is_stattrak: false
        };
      }
    } else {
      // 真实模式：调用API
      const caseKey =
        props.selectedCase.case_key ||
        props.selectedCase.key ||
        props.selectedCase.id;
      console.log(`${LOG_PREFIX} 使用箱子标识符:`, caseKey);

      if (!caseKey) {
        throw new Error("箱子标识符不能为空");
      }

      // 调用开箱API
      const response = await caseApi.openCase(caseKey);
      console.log(`${LOG_PREFIX} API响应:`, response);
      console.log(
        `${LOG_PREFIX} API响应数据详情:`,
        JSON.stringify(response.data, null, 2)
      );

      if (!response.success || !response.data) {
        throw new Error(response.message || "开箱失败");
      }

      // API返回的是数组，取第一个物品作为获胜物品
      winnerData = Array.isArray(response.data)
        ? response.data[0]
        : response.data;
    }

    console.log(`${LOG_PREFIX} 获胜物品原始数据:`, winnerData);

    // 设置获胜物品
    winnerItem.value = convertApiItemFormat(winnerData);
    console.log(`${LOG_PREFIX} 转换后的获胜物品:`, winnerItem.value);

    // 生成动画物品
    displayItems.value = generateAnimationItems(winnerData);

    currentStepIndex.value = 4;
    await new Promise((resolve) => setTimeout(resolve, 1000));

    // 隐藏步骤提示
    showStepHint.value = false;

    // 执行滚动动画
    await performScrollAnimation();

    // 动画完成后等待1秒再发送结果
    await new Promise((resolve) => setTimeout(resolve, 1000));

    // 发送开箱完成事件，让页面处理结果弹窗
    // 传递原始API数据给页面
    emit("case-opened", winnerData);

    // 不再自动重置，等待用户关闭弹出框后由页面调用resetScrollPosition
  } catch (error) {
    console.error(`${LOG_PREFIX} 开箱失败:`, error);
    showStepHint.value = false;
    // 这里可以添加错误提示
  } finally {
    isAnimating.value = false;
  }
};

const resetAnimation = async () => {
  console.log(`${LOG_PREFIX} 开始重置动画 - 使用Loading方式`);
  
  // 显示重置loading
  showResetHint.value = true;

  // Loading时间稍长，让用户看到重置过程
  await new Promise((resolve) => setTimeout(resolve, 1200));

  // 直接重置所有状态，不使用动画
  isAnimating.value = false;
  showStepHint.value = false;
  winnerItem.value = null;
  currentStepIndex.value = 0;
  
  // 直接重置到初始位置
  animationOffset.value = 0;

  // 重新初始化预览物品
  if (props.caseItems && props.caseItems.length > 0) {
    initializePreviewItems();
  } else {
    displayItems.value = [];
  }

  // 隐藏重置提示
  showResetHint.value = false;

  console.log(`${LOG_PREFIX} 动画重置完成 - Loading方式`);
};

// 提供给页面调用的重置方法
const resetScrollPosition = async () => {
  console.log(`${LOG_PREFIX} 页面请求重置滚动位置`);
  await resetAnimation();
};

// 暴露方法给父组件
defineExpose({
  resetScrollPosition,
  startOpening,
  resetAnimation,
});

// 初始化预览物品
const initializePreviewItems = () => {
  if (!props.caseItems || props.caseItems.length === 0) {
    // console.log(`${LOG_PREFIX} 没有箱子物品数据，无法初始化预览`);
    return;
  }

  // console.log(`${LOG_PREFIX} 初始化预览物品，箱子物品数据:`, props.caseItems);

  // 生成预览物品（不包含获胜物品）
  const previewItems: any[] = [];
  const totalItems = 15; // 预览显示15个物品

  // 收集所有可用物品
  const allItems: any[] = [];
  props.caseItems.forEach((rarityGroup: any, groupIndex: number) => {
    // console.log(`${LOG_PREFIX} 处理稀有度组 ${groupIndex}:`, rarityGroup);

    if (rarityGroup.items && Array.isArray(rarityGroup.items)) {
      rarityGroup.items.forEach((itemWrapper: any, itemIndex: number) => {
        const item = itemWrapper.item_info || itemWrapper;
        // console.log(`${LOG_PREFIX} 处理物品 ${groupIndex}-${itemIndex}:`, item);

        if (item) {
          const convertedItem = {
            id: item.id || `item-${groupIndex}-${itemIndex}`,
            name: item.name || t("skins.unknown"),
            name_zh_hans: item.name_zh_hans || item.name_zh || item.name_cn,
            name_en: item.name_en || item.name,
            image: item.image || "/demo/item1.png",
            price: item.item_price?.price || item.price || 0,
            rarity_color:
              item.item_rarity?.rarity_color ||
              rarityGroup.rarity_color ||
              "#6b7280",
            rarity_name:
              item.item_rarity?.rarity_name || rarityGroup.rarity_name,
            rarity_name_zh_hans:
              item.item_rarity?.rarity_name_zh_hans ||
              rarityGroup.rarity_name_zh_hans,
            rarity_name_en:
              item.item_rarity?.rarity_name_en || rarityGroup.rarity_name_en,
            is_stattrak:
              item.is_stattrak || item.name?.includes("StatTrak™") || false,
          };

          // console.log(`${LOG_PREFIX} 转换后的物品:`, convertedItem);
          allItems.push(convertedItem);
        }
      });
    }
  });

  // console.log(`${LOG_PREFIX} 收集到 ${allItems.length} 个物品`);

  if (allItems.length === 0) {
    console.warn(`${LOG_PREFIX} 没有找到有效的物品数据`);
    return;
  }

  // 随机生成预览物品
  for (let i = 0; i < totalItems; i++) {
    const randomItem = allItems[Math.floor(Math.random() * allItems.length)];
    previewItems.push({ ...randomItem, id: `preview-${i}` });
  }

  displayItems.value = previewItems;
  // console.log(
  //   `${LOG_PREFIX} 预览物品初始化完成，共 ${previewItems.length} 个物品:`,
  //   previewItems
  // );
};

// 监听数据变化
watch(
  () => props.caseItems,
  (newItems) => {
    // console.log(`${LOG_PREFIX} 箱子物品数据更新:`, newItems?.length);
    if (newItems && newItems.length > 0 && displayItems.value.length === 0) {
      // 只在没有显示物品时初始化预览
      nextTick(() => {
        initializePreviewItems();
      });
    }
  },
  { immediate: true }
);

// 监听选中的箱子变化
watch(
  () => props.selectedCase,
  (newCase) => {
    // console.log(`${LOG_PREFIX} 选中箱子变化:`, newCase?.name || newCase?.key);
    // 重置动画状态
    if (!isAnimating.value) {
      displayItems.value = [];
      winnerItem.value = null;
      animationOffset.value = 0;

      // 重新初始化预览物品
      if (props.caseItems && props.caseItems.length > 0) {
        nextTick(() => {
          initializePreviewItems();
        });
      }
    }
  },
  { immediate: true }
);

// 组件挂载
onMounted(() => {
  // console.log(`${LOG_PREFIX} 组件已挂载`);

  // 延迟初始化预览物品，确保DOM已渲染
  nextTick(() => {
    if (props.caseItems && props.caseItems.length > 0) {
      initializePreviewItems();
    }
  });
});
</script>

<style scoped>
.bg-grid-pattern {
  background-image: linear-gradient(
      rgba(255, 255, 255, 0.1) 1px,
      transparent 1px
    ),
    linear-gradient(90deg, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
}

.winner-glow {
  filter: drop-shadow(0 0 20px rgba(6, 182, 212, 0.6));
}

.text-gradient {
  background: linear-gradient(
    135deg,
    var(--color-primary),
    var(--color-secondary)
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 硬件加速 */
.smooth-case-animation * {
  transform: translateZ(0);
  backface-visibility: hidden;
}

/* Loading效果现在使用通用的UiLoadingSpinner组件 */
</style>
