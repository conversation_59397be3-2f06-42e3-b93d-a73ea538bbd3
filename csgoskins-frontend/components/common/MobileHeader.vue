<!-- components/common/MobileHeader.vue -->
<template>
  <div class="mobile-header-container fixed top-0 left-0 right-0 z-50">
    <!-- 顶部通知栏 -->
    <div class="bg-gradient-to-b from-zinc-900/95 to-zinc-900/90 py-1 px-3 backdrop-blur-md border-b border-zinc-800/50 shadow-sm shadow-black/20">
      <div class="flex justify-between items-center">
        <div class="notification-ticker relative overflow-hidden h-5 flex-grow mr-2">
          <transition-group 
            name="notification-slide" 
            tag="div" 
            class="absolute inset-0 flex items-center"
          >
            <div 
              v-for="(notice, index) in activeNotices" 
              :key="notice.id"
              v-show="currentNoticeIndex === index"
              class="text-xs text-white/70 flex items-center w-full"
            >
              <span class="bg-primary px-1 mr-1.5 rounded text-[0.625rem] text-black font-medium">公告</span>
              {{ notice.text }}
            </div>
          </transition-group>
        </div>
        
        <!-- 语言切换器 -->
        <LanguageSwitcher class="scale-90 transform origin-right" />
      </div>
    </div>
    
    <!-- 主导航区域 -->
    <header class="bg-gradient-to-b from-zinc-800/95 to-zinc-900/95 py-2 border-b border-zinc-700/20 w-full shadow-lg shadow-black/20 backdrop-blur-md">
      <div class="flex justify-between items-center px-3">
        <NuxtLink to="/" class="inline-flex items-center whitespace-nowrap font-title text-primary font-logo h-8 hover:opacity-90 transition-opacity duration-200">
          <span>CSGO</span><img src="/images/logo.svg" alt="CSGOSKINS" class="h-6 m-0 drop-shadow-md"><span>SKINS</span>
        </NuxtLink>
        
        <div class="flex items-center space-x-2 pr-2">
          <!-- 用户余额 - 已登录时显示 -->
          <template v-if="userStore.isAuthenticated">
            <div class="user-balance px-2 py-1 bg-zinc-800/70 rounded-full flex items-center border border-zinc-700/30 shadow-sm shadow-black/20">
              <img src="/images/dollar.svg" alt="balance" class="w-3 h-3 mr-1 drop-shadow-sm">
              <span class="text-emerald-400 text-xs font-medium">{{ userStore.getBalance.toFixed(2) }}</span>
            </div>
            
            <button 
              @click="toggleMobileMenu"
              class="w-6 h-6 rounded-full overflow-hidden border border-primary/30 shadow-sm"
            >
              <img :src="userStore.getAvatar" :alt="userStore.getUsername" class="w-full h-full object-cover" />
            </button>
          </template>
          
          <!-- 登录按钮和菜单按钮 - 未登录时显示 -->
          <template v-else>
            <NuxtLink to="/auth/login" class="px-2 py-1 bg-zinc-800/70 rounded-full text-xs text-white hover:text-primary transition-colors">
              {{ $t('auth.login.title') }}
            </NuxtLink>
            
            <button 
              @click="toggleMobileMenu"
              class="mobile-menu-btn p-1.5 rounded-lg bg-gray-800/60 border border-gray-700/30 transition-all duration-300"
              :class="{ 'bg-primary/20': showMobileMenu }"
            >
              <div class="hamburger" :class="{ active: showMobileMenu }">
                <span></span>
                <span></span>
                <span></span>
              </div>
            </button>
          </template>
          
          <NuxtLink to="/recharge" class="px-2 py-1 bg-primary hover:bg-blue-500 text-xs rounded-full text-white transition-all duration-300 shadow-md shadow-primary/20">
            {{ $t('nav.recharge') }}
          </NuxtLink>
        </div>
      </div>
    </header>

    <!-- 移动端下拉菜单 -->
    <div 
      v-if="showMobileMenu" 
      class="mobile-menu bg-gray-900/95 backdrop-blur-md border-t border-gray-700/30 absolute top-full left-0 right-0 z-30"
    >
      <div class="container mx-auto px-4 py-4">
        <!-- 主导航 -->
        <nav class="mb-4">
          <div class="space-y-2">
            <NuxtLink 
              v-for="item in mainNavItems" 
              :key="item.path"
              :to="item.path" 
              @click="closeMobileMenu"
              class="flex items-center py-3 px-4 rounded-lg hover:bg-gray-800/60 transition-all duration-200"
              :class="{ 'bg-primary/10 text-primary': isActivePath(item.path) }"
            >
              <i :class="item.icon" class="mr-3 text-primary/70 w-5"></i>
              <span class="font-medium">{{ $t(item.label) }}</span>
              <span v-if="item.badge" class="ml-auto bg-secondary text-white text-xs px-2 py-1 rounded">{{ item.badge }}</span>
            </NuxtLink>
          </div>
        </nav>

        <!-- 用户区域 -->
        <div class="border-t border-gray-700/30 pt-4">
          <template v-if="!userStore.isAuthenticated">
            <NuxtLink 
              to="/auth/login" 
              @click="closeMobileMenu"
              class="flex items-center justify-center py-3 px-4 bg-gradient-primary rounded-lg font-medium transition-all duration-300"
            >
              <i class="fas fa-sign-in-alt mr-2"></i>
              {{ $t('auth.login_or_register') }}
            </NuxtLink>
          </template>
          <template v-else>
            <div class="space-y-2">
              <NuxtLink 
                to="/profile" 
                @click="closeMobileMenu"
                class="flex items-center py-3 px-4 rounded-lg hover:bg-gray-800/60 transition-all duration-200"
              >
                <div class="w-8 h-8 bg-gradient-primary rounded-full flex items-center justify-center mr-3">
                  <i class="fas fa-user text-white text-sm"></i>
                </div>
                <div>
                  <div class="font-medium">{{ userStore.user?.username || 'User' }}</div>
                  <div class="text-sm text-white/60">个人中心</div>
                </div>
              </NuxtLink>
              <button 
                @click="handleLogout"
                class="flex items-center w-full py-3 px-4 rounded-lg hover:bg-red-500/10 text-red-400 transition-all duration-200"
              >
                <i class="fas fa-sign-out-alt mr-3 w-5"></i>
                <span>退出登录</span>
              </button>
            </div>
          </template>
        </div>

        <!-- 语言切换器 -->
        <div class="border-t border-gray-700/30 pt-4 mt-4">
          <LanguageSwitcher />
        </div>
      </div>
    </div>
    
    <!-- 占位元素，保持内容在头部下方 -->
    <div class="header-placeholder" ref="headerPlaceholder"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import LanguageSwitcher from './LanguageSwitcher.vue'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()

// 通知相关
const notices = ref([
  { id: 1, text: '新用户首充享8折优惠!' },
  { id: 2, text: '每日登录领取免费开箱机会' },
  { id: 3, text: '新增龙王系列皮肤上线' }
])
const currentNoticeIndex = ref(0)
const activeNotices = computed(() => notices.value)

// 通知自动滚动
let noticeInterval: NodeJS.Timeout | null = null
const headerPlaceholder = ref<HTMLElement | null>(null)

// 移动端菜单状态
const showMobileMenu = ref(false)

// 主导航项
const mainNavItems = [
  { path: '/', label: 'nav.home', icon: 'fas fa-home' },
  { path: '/cases', label: 'nav.cases', icon: 'fas fa-box' },
  { path: '/battle', label: 'nav.battle', icon: 'fas fa-sword' },
  { path: '/activities', label: 'nav.activity', icon: 'fas fa-star', badge: 'HOT' },
  { path: '/profile', label: 'nav.profile', icon: 'fas fa-user' }
]

// 切换移动端菜单
const toggleMobileMenu = () => {
  showMobileMenu.value = !showMobileMenu.value
}

// 关闭移动端菜单
const closeMobileMenu = () => {
  showMobileMenu.value = false
}

// 检查路径是否激活
const isActivePath = (path: string) => {
  if (path === '/') {
    return route.path === '/'
  }
  return route.path.startsWith(path)
}

// 退出登录
const handleLogout = async () => {
  await userStore.logout()
  closeMobileMenu()
  router.push('/')
}

onMounted(async () => {
  // 启动通知滚动
  noticeInterval = setInterval(() => {
    currentNoticeIndex.value = (currentNoticeIndex.value + 1) % notices.value.length
  }, 4000)

  // 检查用户登录状态
  try {
    await userStore.checkAuth()
  } catch (error) {
    console.warn('Failed to check auth status:', error)
  }
  
  // 设置头部占位符高度
  if (process.client) {
    nextTick(() => {
      const header = document.querySelector('.mobile-header-container') as HTMLElement
      if (header && headerPlaceholder.value) {
        headerPlaceholder.value.style.height = `${header.offsetHeight}px`
      }
    })
  }
})

onUnmounted(() => {
  if (noticeInterval) {
    clearInterval(noticeInterval)
  }
})
</script>

<style lang="scss" scoped>
.mobile-header-container {
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.notification-ticker {
  .notification-slide-enter-active,
  .notification-slide-leave-active {
    transition: all 0.5s ease;
  }
  
  .notification-slide-enter-from {
    opacity: 0;
    transform: translateY(20px);
  }
  
  .notification-slide-leave-to {
    opacity: 0;
    transform: translateY(-20px);
  }
}

.hamburger {
  width: 18px;
  height: 14px;
  position: relative;
  cursor: pointer;
  
  span {
    display: block;
    position: absolute;
    height: 2px;
    width: 100%;
    background: white;
    border-radius: 1px;
    opacity: 1;
    left: 0;
    transform: rotate(0deg);
    transition: .25s ease-in-out;
    
    &:nth-child(1) {
      top: 0;
    }
    
    &:nth-child(2) {
      top: 6px;
    }
    
    &:nth-child(3) {
      top: 12px;
    }
  }
  
  &.active {
    span {
      &:nth-child(1) {
        top: 6px;
        transform: rotate(135deg);
      }
      
      &:nth-child(2) {
        opacity: 0;
        left: -60px;
      }
      
      &:nth-child(3) {
        top: 6px;
        transform: rotate(-135deg);
      }
    }
  }
}

.mobile-menu {
  animation: slideDown 0.3s ease;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 渐变样式
.bg-gradient-primary {
  background: linear-gradient(135deg, var(--color-primary, #1AC6FF), var(--color-secondary, #FF6B6B));
}
</style>
