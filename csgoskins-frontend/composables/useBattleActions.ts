// 🚀 Battle操作管理 Composable
// 分离API调用和用户操作逻辑

import { useBattleState } from '~/composables/useBattleState' // 类型提示用

export const useBattleActions = (
  battleId: string,
  sharedBattleState?: ReturnType<typeof useBattleState> // eslint-disable-line @typescript-eslint/ban-types
) => {
  const { t } = useI18n()
  const userStore = useUserStore()
  const battleState = sharedBattleState
  
  // 🎯 Toast通知状态
  const toastMessage = ref<string>('')
  const toastType = ref<'success' | 'error' | 'info' | 'warning'>('info')
  const showToast = ref(false)
  
  // 🎯 获取对战详情
  const fetchBattleDetail = async (showLoadingState = true) => {

    
    try {
      const { BattleApi } = await import('~/services/battle-api')
      const battleApi = new BattleApi()
      const response = await battleApi.getRoomDetail(battleId)
      
      if (response.success && response.data) {
        return response.data
      } else {
        throw new Error(response.message || t('battle.detail.fetch_failed'))
      }
    } catch (e) {
      console.error('[🎰BATTLE-ACTIONS] ❌ 获取失败:', e)
      const errorMessage = e instanceof Error ? e.message : t('battle.detail.fetch_failed')
      showToastMessage(errorMessage, 'error')
      throw new Error(errorMessage)
    }
  }
  
  // 🎯 加入对战
  const joinBattle = async () => {
    if (!userStore.user) {
      showToastMessage(t('auth.login_required'), 'warning')
      return false
    }
    
    try {
      const { BattleApi } = await import('~/services/battle-api')
      const battleApi = new BattleApi()
      const response = await battleApi.joinRoom(battleId)
      
      if (response.success) {
        showToastMessage(t('battle.detail.join_success'), 'success')
        return true
      } else {
        showToastMessage(response.message || t('battle.detail.join_failed'), 'error')
        return false
      }
    } catch (error) {
      console.error('[🎰BATTLE-ACTIONS] 加入对战失败:', error)
      showToastMessage(t('battle.detail.join_failed'), 'error')
      return false
    }
  }
  
  // 🎯 离开对战
  const leaveBattle = async () => {
    if (!userStore.user) return false
    
    try {
      console.log('[🎰BATTLE-ACTIONS] 离开对战:', battleId)
      const { BattleApi } = await import('~/services/battle-api')
      const battleApi = new BattleApi()
      const response = await battleApi.leaveRoom(battleId)
      
      if (response.success) {
        showToastMessage(t('battle.detail.leave_success'), 'success')
        return true
      } else {
        showToastMessage(response.message || t('battle.detail.leave_failed'), 'error')
        return false
      }
    } catch (error) {
      console.error('[🎰BATTLE-ACTIONS] 离开对战失败:', error)
      showToastMessage(t('battle.detail.leave_failed'), 'error')
      return false
    }
  }
  
    // 🎯 取消对战
  const cancelBattle = async () => {
    if (!userStore.user) return false
    
    try {
      console.log('[🎰BATTLE-ACTIONS] 取消对战:', battleId)
      const { BattleApi } = await import('~/services/battle-api')
      const battleApi = new BattleApi()
      const response = await battleApi.dismissRoom(battleId)
      
      if (response.success) {
        showToastMessage(t('battle.detail.cancel_success'), 'success')
        return true
      } else {
        showToastMessage(response.message || t('battle.detail.cancel_failed'), 'error')
        return false
      }
    } catch (error) {
      console.error('[🎰BATTLE-ACTIONS] 取消对战失败:', error)
      showToastMessage(t('battle.detail.cancel_failed'), 'error')
      return false
    }
  }

  // 🎯 开始对战
  // 注意：根据WebSocket API文档，对战是在房间满员时自动开始的
  // 没有手动开始对战的API，这个方法主要用于UI反馈
  const startBattle = async () => {
    if (!userStore.user) return false
    
    try {
      console.log('[🎰BATTLE-ACTIONS] 对战将自动开始 (房间满员时):', battleId)
      
      // 对战系统是自动管理的：
      // 1. 房间满员时，WebSocket会发送 ["boxroom", "start", roomData] 消息
      // 2. 然后自动进入对战流程，无需手动API调用
      // 3. 前端只需要监听WebSocket消息并更新UI状态
      
      showToastMessage(t('battle.detail.start_auto_info'), 'info')
      return true
    } catch (error) {
      console.error('[🎰BATTLE-ACTIONS] 开始对战失败:', error)
      showToastMessage(t('battle.detail.start_failed'), 'error')
      return false
    }
  }
  
  // 🎯 复制对战ID
  const copyBattleId = async () => {
    try {
      await navigator.clipboard.writeText(battleId)
      showToastMessage(t('battle.detail.copy_success'), 'success')
    } catch (error) {
      console.error('[🎰BATTLE-ACTIONS] 复制失败:', error)
      showToastMessage(t('battle.detail.copy_failed'), 'error')
    }
  }
  
  // 🎯 分享对战
  const shareBattle = async () => {
    const shareUrl = `${window.location.origin}/battle/${battleId}`
    
    if (navigator.share) {
      try {
        await navigator.share({
          title: t('battle.detail.share_title'),
          text: t('battle.detail.share_text'),
          url: shareUrl
        })
        showToastMessage(t('battle.detail.share_success'), 'success')
      } catch (error) {
        if (error instanceof Error && error.name !== 'AbortError') {
          console.error('[🎰BATTLE-ACTIONS] 分享失败:', error)
          await copyBattleId() // 降级到复制
        }
      }
    } else {
      // 降级到复制URL
      try {
        await navigator.clipboard.writeText(shareUrl)
        showToastMessage(t('battle.detail.share_copy_success'), 'success')
      } catch (error) {
        console.error('[🎰BATTLE-ACTIONS] 分享复制失败:', error)
        showToastMessage(t('battle.detail.share_failed'), 'error')
      }
    }
  }
  
  // 🎯 重试操作
  const retryOperation = async (operation: () => Promise<any>, maxRetries = 3) => {
    let lastError: Error | null = null
    
    for (let i = 0; i < maxRetries; i++) {
      try {
        return await operation()
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error))
        console.warn(`[🎰BATTLE-ACTIONS] 操作失败，重试 ${i + 1}/${maxRetries}:`, lastError.message)
        
        if (i < maxRetries - 1) {
          // 指数退避延迟
          await new Promise(resolve => setTimeout(resolve, Math.pow(2, i) * 1000))
        }
      }
    }
    
    throw lastError
  }
  
  // 🎯 Toast通知管理
  const showToastMessage = (message: string, type: 'success' | 'error' | 'info' | 'warning' = 'info') => {
    toastMessage.value = message
    toastType.value = type
    showToast.value = true
    
    // 自动隐藏
    setTimeout(() => {
      showToast.value = false
    }, 5000)
  }
  
  const hideToast = () => {
    showToast.value = false
  }
  
  // 🎯 获取Toast图标
  const getToastIcon = (type: string) => {
    switch (type) {
      case 'success':
        return 'material-symbols:check-circle'
      case 'error':
        return 'material-symbols:error'
      case 'warning':
        return 'material-symbols:warning'
      case 'info':
      default:
        return 'material-symbols:info'
    }
  }
  
  // 🎯 处理页面操作
  const handleJoinBattle = async () => {
    const success = await joinBattle()
    if (success) {
      // 重新获取对战数据并同步到全局状态
      const detail = await fetchBattleDetail(false)
      if (detail && battleState) {
        console.log('[🎰BATTLE-ACTIONS] 👥 joinBattle 后同步 updateBattleData', { betsLength: detail.bets?.length })
        battleState.updateBattleData(detail)
      }
      return detail
    }
    return null
  }
  
  const handleLeaveBattle = async () => {
    const success = await leaveBattle()
    if (success) {
      // 退出成功后延迟跳转到对战列表
      setTimeout(() => {
        navigateTo('/battle')
      }, 1500)
    }
    return success
  }
  
  const handleDismissBattle = async () => {
    const success = await cancelBattle()
    if (success) {
      // 延迟跳转到对战列表
      setTimeout(() => {
        navigateTo('/battle')
      }, 2000)
    }
    return success
  }
  
  const handleStartBattle = async () => {
    const success = await startBattle()
    if (success) {
      const detail = await fetchBattleDetail(false)
      if (detail && battleState) {
        console.log('[🎰BATTLE-ACTIONS] 🚀 startBattle 后同步 updateBattleData', { state: detail.state })
        battleState.updateBattleData(detail)
      }
      return detail
    }
    return null
  }
  
  const handleCopyId = () => {
    copyBattleId()
  }
  
  const handleShare = () => {
    shareBattle()
  }
  
  const handleRetry = async () => {
    try {
      return await retryOperation(() => fetchBattleDetail())
    } catch (error) {
      console.error('[🎰BATTLE-ACTIONS] 重试失败:', error)
      return null
    }
  }
  
  return {
    // Toast状态
    toastMessage: readonly(toastMessage),
    toastType: readonly(toastType),
    showToast: readonly(showToast),
    
    // API方法
    fetchBattleDetail,
    joinBattle,
    leaveBattle,
    cancelBattle,
    startBattle,
    copyBattleId,
    shareBattle,
    retryOperation,
    
    // Toast方法
    showToastMessage,
    hideToast,
    getToastIcon,
    
    // 页面操作方法
    handleJoinBattle,
    handleLeaveBattle,
    handleDismissBattle,
    handleStartBattle,
    handleCopyId,
    handleShare,
    handleRetry
  }
} 