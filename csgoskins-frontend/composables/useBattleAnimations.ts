// 🚀 Battle动画管理 Composable
// 分离复杂的动画逻辑，提高可维护性

export const useBattleAnimations = () => {
  const { t } = useI18n()
  
  // 🎯 动画状态
  const isAnimating = ref(false)
  const animationProgress = ref(0)
  const currentAnimationId = ref<string | null>(null)
  const animationQueue = ref<Array<{ id: string, type: string, data: any }>>([])
  
  // 🎯 动画配置
  const animationConfig = ref({
    duration: 8000,
    easeType: 'power2.out',
    staggerDelay: 200,
    particleCount: 20,
    enableEffects: true,
    enableSound: true
  })
  
  // 🎯 动画元素引用
  const caseElements = ref<Map<string, HTMLElement>>(new Map())
  const playerElements = ref<Map<string, HTMLElement>>(new Map())
  
  // 🎯 时间同步状态
  const timeSync = ref({
    serverOffset: 0,
    lastSyncTime: 0,
    syncQuality: 'good' as 'good' | 'poor' | 'bad'
  })
  
  // 🐞 调试辅助：统一的动画调试日志
  const DEBUG_ANIM = true
  const dbg = (...args: any[]) => { if (DEBUG_ANIM) console.log('[ANIM-DBG]', ...args) }
  
  // 🎯 注册动画元素
  const registerCaseElement = (caseId: string, element: HTMLElement) => {
    caseElements.value.set(caseId, element)
    console.log('[🎰BATTLE-ANIM] 注册箱子元素:', caseId, '总数:', caseElements.value.size)
    dbg('registerCaseElement', caseId, !!element, 'all keys:', Array.from(caseElements.value.keys()))
  }
  
  const registerPlayerElement = (playerId: string, element: HTMLElement) => {
    playerElements.value.set(playerId, element)
    console.log('[🎰BATTLE-ANIM] 注册玩家元素:', playerId)
    dbg('registerPlayerElement', playerId, !!element)
  }
  
  const unregisterCaseElement = (caseId: string) => {
    caseElements.value.delete(caseId)
  }
  
  const unregisterPlayerElement = (playerId: string) => {
    playerElements.value.delete(playerId)
  }
  
  // 🎯 时间同步更新
  const updateTimeSync = (serverTimestamp: number, clientTimestamp: number) => {
    const networkDelay = (Date.now() - clientTimestamp) / 2
    const serverOffset = serverTimestamp - clientTimestamp - networkDelay
    
    timeSync.value = {
      serverOffset,
      lastSyncTime: Date.now(),
      syncQuality: networkDelay < 100 ? 'good' : networkDelay < 300 ? 'poor' : 'bad'
    }
    
    console.log('[🎰BATTLE-ANIM] 时间同步更新:', timeSync.value)
  }
  
  // 🎯 计算同步后的开始时间
  const calculateSyncedStartTime = (serverStartTimestamp: number) => {
    return serverStartTimestamp - timeSync.value.serverOffset
  }
  
  // 🎯 开箱动画 - 根据WebSocket API文档实现
  const startOpeningAnimation = async (data: {
    animation_id: string
    animation_start_timestamp?: number
    server_timestamp?: number
    participants: Array<{
      user: { username: string }
      animation_duration?: number
    }>
    sync_config?: {
      tolerance_ms?: number
      max_delay_compensation?: number
    }
  }) => {
    console.log('[🎰BATTLE-ANIM] 开始开箱动画:', data)
    dbg('startOpeningAnimation', data.animation_id, 'participants', data.participants?.length || 0)
    
    // ⚠️ 检查元素注册情况
    if (caseElements.value.size === 0 || playerElements.value.size === 0) {
      console.warn('[ANIM-DBG] 警告：尚未注册任何 case/player 元素，动画可能无法播放', {
        caseElementsSize: caseElements.value.size,
        playerElementsSize: playerElements.value.size
      })
    }
    
    currentAnimationId.value = data.animation_id
    isAnimating.value = true
    animationProgress.value = 0
    
    // 时间戳同步处理
    let startDelay = 0
    if (data.animation_start_timestamp && data.server_timestamp) {
      updateTimeSync(data.server_timestamp, Date.now())
      const syncedStartTime = calculateSyncedStartTime(data.animation_start_timestamp)
      startDelay = Math.max(syncedStartTime - Date.now(), 0)
      
      console.log('[🎰BATTLE-ANIM] 同步延迟:', startDelay, 'ms')
    }
    
    // 延迟补偿
    const tolerance = data.sync_config?.tolerance_ms || 100
    const maxCompensation = data.sync_config?.max_delay_compensation || 500
    
    if (startDelay > maxCompensation) {
      console.warn('[🎰BATTLE-ANIM] 延迟过大，跳过同步')
      startDelay = 0
    }
    
    // 开始动画
    setTimeout(() => {
      performSynchronizedAnimation(data)
    }, startDelay)
  }
  
  // 🎯 执行同步动画
  const performSynchronizedAnimation = async (data: any) => {
    dbg('performSynchronizedAnimation', currentAnimationId.value)
    const duration = data.participants[0]?.animation_duration || animationConfig.value.duration
    
    console.log('[🎰BATTLE-ANIM] 执行同步动画，时长:', duration)
    dbg('performSynchronizedAnimation', currentAnimationId.value, 'duration', duration)
    
    const resolveKey = (u: any): string | undefined => {
      return u?.username || u?.uid || u?.profile?.nickname
    }

    data.participants.forEach((participant: any, index: number) => {
      const { user } = participant
      const key = resolveKey(user)
      const caseElement = key ? getCaseElementForPlayer(key) : null
      const playerElement = key ? playerElements.value.get(key) : null
      
      if (caseElement) {
        animateCaseOpening(caseElement, {
          duration,
          delay: index * animationConfig.value.staggerDelay,
          onProgress: (progress: number) => {
            animationProgress.value = Math.max(animationProgress.value, progress)
          },
          onComplete: () => {
            console.log('[🎰BATTLE-ANIM] 玩家动画完成:', key)
          }
        })
      } else {
        console.warn('[ANIM-DBG] 未找到caseElement，无法播放动画', { player: key })
      }
      
      if (playerElement) {
        animatePlayerState(playerElement, 'opening')
      } else {
        console.warn('[ANIM-DBG] 未找到playerElement', { player: key })
      }
    })
    
    // 总动画完成处理
    setTimeout(() => {
      isAnimating.value = false
      animationProgress.value = 1
      console.log('[🎰BATTLE-ANIM] 所有动画完成')
    }, duration + (data.participants.length * animationConfig.value.staggerDelay))
  }
  
  // 🎯 箱子开启动画
  const animateCaseOpening = (element: HTMLElement, options: {
    duration: number
    delay?: number
    onProgress?: (progress: number) => void
    onComplete?: () => void
  }) => {
    if (!element) return
    
    // 使用GSAP进行动画 (如果可用)
    if (typeof window !== 'undefined' && (window as any).gsap) {
      const gsap = (window as any).gsap
      
      const timeline = gsap.timeline({
        delay: options.delay || 0,
        onUpdate: () => {
          const progress = timeline.progress()
          options.onProgress?.(progress)
        },
        onComplete: options.onComplete
      })
      
      // 动画序列
      timeline
        .to(element, {
          rotation: 360,
          scale: 1.1,
          duration: options.duration * 0.3,
          ease: 'power2.out'
        })
        .to(element, {
          scale: 1.2,
          boxShadow: '0 0 30px rgba(255, 215, 0, 0.8)',
          duration: options.duration * 0.4,
          ease: 'power2.inOut'
        })
        .to(element, {
          scale: 1,
          rotation: 0,
          boxShadow: 'none',
          duration: options.duration * 0.3,
          ease: 'power2.in'
        })
    } else {
      // 降级到CSS动画
      element.classList.add('case-opening-animation')
      
      setTimeout(() => {
        element.classList.remove('case-opening-animation')
        options.onComplete?.()
      }, options.duration)
    }
  }
  
  // 🎯 玩家状态动画
  const animatePlayerState = (element: HTMLElement, state: 'waiting' | 'opening' | 'completed') => {
    if (!element) return
    
    // 移除之前的状态类
    element.classList.remove('player-waiting', 'player-opening', 'player-completed')
    
    // 添加新的状态类
    element.classList.add(`player-${state}`)
    
    console.log('[🎰BATTLE-ANIM] 玩家状态动画:', state)
  }
  
  // 🎯 结果揭晓动画
  const animateResultReveal = (results: Array<{
    user: { username: string }
    items: Array<any>
    victory?: boolean | null
  }>) => {
    console.log('[🎰BATTLE-ANIM] 开始结果揭晓动画')
    
    results.forEach((result, index) => {
      setTimeout(() => {
        const playerElement = playerElements.value.get(result.user.username)
        if (playerElement) {
          animatePlayerState(playerElement, 'completed')
          
          // 胜利特效
          if (result.victory === true) {
            animateVictoryEffect(playerElement)
          }
        }
        
        // 物品揭晓动画
        result.items.forEach((item, itemIndex) => {
          setTimeout(() => {
            animateItemReveal(item, result.user.username)
          }, itemIndex * 300)
        })
        
      }, index * 1000)
    })
  }
  
  // 🎯 胜利特效动画
  const animateVictoryEffect = (element: HTMLElement) => {
    if (!element) return
    
    element.classList.add('victory-effect')
    
    // 创建粒子效果
    if (animationConfig.value.enableEffects) {
      createParticleEffect(element)
    }
    
    // 播放胜利音效
    if (animationConfig.value.enableSound) {
      playVictorySound()
    }
    
    setTimeout(() => {
      element.classList.remove('victory-effect')
    }, 3000)
  }
  
  // 🎯 物品揭晓动画
  const animateItemReveal = (item: any, playerUsername: string) => {
    console.log('[🎰BATTLE-ANIM] 物品揭晓:', item.name, '玩家:', playerUsername)
    
    // 创建临时物品元素进行动画
    const itemElement = document.createElement('div')
    itemElement.className = 'item-reveal-animation'
    itemElement.innerHTML = `
      <img src="${item.image}" alt="${item.name}" />
      <div class="item-name">${item.name}</div>
      <div class="item-price">$${item.item_price?.price || 0}</div>
    `
    
    const playerElement = playerElements.value.get(playerUsername)
    if (playerElement) {
      playerElement.appendChild(itemElement)
      
      // 动画后移除
      setTimeout(() => {
        if (itemElement.parentNode) {
          itemElement.parentNode.removeChild(itemElement)
        }
      }, 2000)
    }
  }
  
  // 🎯 粒子效果
  const createParticleEffect = (element: HTMLElement) => {
    for (let i = 0; i < animationConfig.value.particleCount; i++) {
      const particle = document.createElement('div')
      particle.className = 'particle'
      particle.style.cssText = `
        position: absolute;
        width: 4px;
        height: 4px;
        background: gold;
        border-radius: 50%;
        pointer-events: none;
        z-index: 1000;
      `
      
      element.appendChild(particle)
      
      // 随机粒子动画
      if (typeof window !== 'undefined' && (window as any).gsap) {
        const gsap = (window as any).gsap
        gsap.to(particle, {
          x: (Math.random() - 0.5) * 200,
          y: (Math.random() - 0.5) * 200,
          opacity: 0,
          duration: 1.5,
          ease: 'power2.out',
          onComplete: () => {
            if (particle.parentNode) {
              particle.parentNode.removeChild(particle)
            }
          }
        })
      }
    }
  }
  
  // 🎯 音效播放
  const playVictorySound = () => {
    try {
      const audio = new Audio('/sounds/victory.mp3')
      audio.volume = 0.5
      audio.play().catch(console.warn)
    } catch (error) {
      console.warn('[🎰BATTLE-ANIM] 音效播放失败:', error)
    }
  }
  
  const playOpeningSound = () => {
    try {
      const audio = new Audio('/sounds/case-opening.mp3')
      audio.volume = 0.3
      audio.play().catch(console.warn)
    } catch (error) {
      console.warn('[🎰BATTLE-ANIM] 音效播放失败:', error)
    }
  }

  // 🎯 调试函数：检查注册的元素
  const debugRegisteredElements = () => {
    console.log('[🎰BATTLE-ANIM] 调试注册的元素:')
    console.log('Case Elements:', Array.from(caseElements.value.keys()))
    console.log('Player Elements:', Array.from(playerElements.value.keys()))
    return {
      caseElements: Array.from(caseElements.value.keys()),
      playerElements: Array.from(playerElements.value.keys())
    }
  }
  
  // 🎯 获取玩家对应的箱子元素
  const getCaseElementForPlayer = (playerUsername: string) => {
    dbg('getCaseElementForPlayer', playerUsername, 'registered elements:', Array.from(caseElements.value.keys()))

    // 尝试多种匹配方式
    for (const [caseId, element] of caseElements.value.entries()) {
      // 1. 直接匹配
      if (caseId === playerUsername) {
        dbg('Found exact match:', caseId)
        return element
      }

      // 2. 包含匹配
      if (caseId.includes(playerUsername) || playerUsername.includes(caseId)) {
        dbg('Found includes match:', caseId)
        return element
      }

      // 3. 数据属性匹配
      if (element.dataset.player === playerUsername) {
        dbg('Found dataset match:', caseId)
        return element
      }

      // 4. 玩家索引匹配（如果playerUsername是数字或包含数字）
      const playerIndex = playerUsername.match(/\d+/)?.[0]
      if (playerIndex && caseId.includes(`player-${playerIndex}`)) {
        dbg('Found index match:', caseId)
        return element
      }
    }

    dbg('No match found for:', playerUsername)
    return null
  }
  
  // 🎯 停止所有动画
  const stopAllAnimations = () => {
    isAnimating.value = false
    currentAnimationId.value = null
    animationProgress.value = 0
    
    // 清除所有动画类
    caseElements.value.forEach(element => {
      element.classList.remove('case-opening-animation')
    })
    
    playerElements.value.forEach(element => {
      element.classList.remove('player-waiting', 'player-opening', 'player-completed', 'victory-effect')
    })
    
    console.log('[🎰BATTLE-ANIM] 停止所有动画')
  }
  
  // 🎯 重置动画状态
  const resetAnimationState = () => {
    stopAllAnimations()
    animationQueue.value = []
    timeSync.value = {
      serverOffset: 0,
      lastSyncTime: 0,
      syncQuality: 'good'
    }
  }
  
  // 🎯 更新动画配置
  const updateAnimationConfig = (newConfig: Partial<typeof animationConfig.value>) => {
    animationConfig.value = { ...animationConfig.value, ...newConfig }
    console.log('[🎰BATTLE-ANIM] 动画配置更新:', animationConfig.value)
  }
  
  return {
    // 状态
    isAnimating: readonly(isAnimating),
    animationProgress: readonly(animationProgress),
    currentAnimationId: readonly(currentAnimationId),
    animationConfig: readonly(animationConfig),
    timeSync: readonly(timeSync),
    
    // 元素注册
    registerCaseElement,
    registerPlayerElement,
    unregisterCaseElement,
    unregisterPlayerElement,
    
    // 时间同步
    updateTimeSync,
    calculateSyncedStartTime,
    
    // 动画控制
    startOpeningAnimation,
    animateResultReveal,
    animateVictoryEffect,
    animateItemReveal,
    stopAllAnimations,
    resetAnimationState,
    updateAnimationConfig,
    
    // 音效
    playVictorySound,
    playOpeningSound,

    // 调试
    debugRegisteredElements
  }
} 