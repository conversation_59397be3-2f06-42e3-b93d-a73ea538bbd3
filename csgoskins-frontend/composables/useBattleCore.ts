import { ref, computed, watch, onBeforeUnmount, readonly } from 'vue'
import { useRoute } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { useUserStore } from '~/stores/user'
import { useBattleState } from './useBattleState'
import { useBattleActions } from './useBattleActions'
import { useBattleWebSocket } from './useBattleWebSocket'
import { useBattleAnimations } from './useBattleAnimations'
import { BattleStatus } from '~/services/battle-api'
import type { BattleCoreInterface } from '~/types/battle-core'

// 🎯 Battle核心管理器 - 统一管理所有Battle相关的composables
export const useBattleCore = (): BattleCoreInterface => {
  const route = useRoute()
  const { t } = useI18n()
  const userStore = useUserStore()

  // 🎯 核心状态
  const battleId = computed(() => route.params.id as string)
  const isInitialized = ref(false)
  const isDestroyed = ref(false)

  // 🎯 初始化所有composables（按依赖顺序）
  const battleState = useBattleState()
  const battleActions = useBattleActions(battleId.value, battleState)
  const battleWebSocket = useBattleWebSocket(battleId.value)
  const battleAnimations = useBattleAnimations()

  // 🎯 初始化状态
  const initialize = async (): Promise<void> => {
    if (isInitialized.value || isDestroyed.value) return

    try {

      
      // 1. 初始化状态
      battleState.isLoading.value = true
      battleState.error.value = null

      // 2. 获取对战数据
      const battleData = await battleActions.fetchBattleDetail()
      if (battleData) {
        battleState.updateBattleData(battleData)

        // 🎯 检查对战是否已结束 - 已结束的对战不需要WebSocket连接
        const isFinished = battleData.state === 11 || battleData.state === 20 // 11=FINISHED, 20=CANCELLED
        
        if (isFinished) {
          
          // 对于已结束的对战，直接设置为静态展示模式
          battleState.battleState.value = battleData.state === 11 ? BattleStatus.COMPLETED : BattleStatus.CANCELLED
          
          // 设置事件处理器（用于UI交互，不涉及WebSocket）
          setupEventHandlers()
          
        } else {
          
          // 注册额外的房间标识符，确保后续WebSocket消息识别
          battleWebSocket.registerAdditionalId(battleData.uid)
          battleWebSocket.registerAdditionalId(battleData.short_id)

          // 加入房间专属Socket频道，确保接收玩家加入/离开消息
          battleWebSocket.joinRoomChannel(battleData.uid)
          
          // 3. 初始化WebSocket连接（仅对进行中的对战）
          await battleWebSocket.initialize()

          // 4. 设置事件处理器
          setupEventHandlers()
        }
      } else {
        throw new Error('Failed to fetch battle detail')
      }

      isInitialized.value = true
    } catch (error) {
      console.error('[🎰BATTLE-CORE] 初始化失败:', error)
      battleState.error.value = error instanceof Error ? error.message : t('battle.detail.init_failed')
    } finally {
      battleState.isLoading.value = false
    }
  }

  // 🎯 设置事件处理器
  const setupEventHandlers = () => {
    // 房间更新事件
    battleWebSocket.onRoomUpdate(async (data) => {
      console.log('[🎰BATTLE-CORE] 房间更新事件:', data)
      battleState.updateBattleData(data)

      // 🛠️ 若WebSocket消息未包含最新bets列表，但joiner_count已变化，主动请求房间详情保证玩家列表同步
      const noPlayerArray = !Array.isArray(data.bets) && !Array.isArray(data.users) && !Array.isArray(data.participants)

      if (!Array.isArray(data.bets) || noPlayerArray) {
        const currentCount = battleState.battleData.value?.bets?.length || 0
        const serverCount = typeof data.joiner_count === 'number' ? data.joiner_count : undefined

        const countMismatch = serverCount !== undefined && serverCount !== currentCount

        if (countMismatch || noPlayerArray) {
          console.log('[🎰BATTLE-CORE] 玩家数组缺失/人数不一致，重新拉取房间详情', {
            serverJoinerCount: serverCount,
            localBetsCount: currentCount,
            hasPlayerArray: !noPlayerArray
          })

          try {
            battleState.isLoading.value = true
            const latestDetail = await battleActions.fetchBattleDetail()
            if (latestDetail) {
              battleState.updateBattleData(latestDetail)
              console.log('[🎰BATTLE-CORE] ✅ 房间详情刷新完成，bets.length=', latestDetail.bets?.length)
            } else {
              console.warn('[🎰BATTLE-CORE] ⚠️ API返回空房间详情，保持现有数据')
            }
          } catch (err) {
            console.error('[🎰BATTLE-CORE] ❌ 拉取房间详情失败:', err)
          } finally {
            battleState.isLoading.value = false
          }
        }
      }
    })

    // 对战开始事件
    battleWebSocket.onBattleStart((data) => {
      console.log('[🎰BATTLE-CORE] 对战开始事件:', data)
      battleState.updateBattleData(data)
      battleState.battleState.value = BattleStatus.IN_PROGRESS
    })

    // 回合开始事件
    battleWebSocket.onRoundStart((data) => {
      console.log('[🎰BATTLE-CORE] 回合开始事件:', data)
      
      // ✅ 更新battleData的state字段，确保UI正确显示
      battleState.updateBattleData({ 
        state: 5, // 5 = IN_PROGRESS
        round_count_current: data.round
      })
      
      battleState.battleState.value = BattleStatus.IN_PROGRESS
      
      if (data.round !== undefined) {
        battleState.currentRound.value = data.round
      }
    })

    // 开箱动画开始事件
    battleWebSocket.onOpeningStart((data) => {
      console.log('[🎰BATTLE-CORE] 开箱动画开始事件:', data)
      
      // ✅ 更新battleData的state字段
      battleState.updateBattleData({ state: 5 }) // 5 = IN_PROGRESS
      
      battleState.battleState.value = BattleStatus.IN_PROGRESS
      
      // 🎯 关键修复：使用本地currentRound，因为WebSocket消息中round字段可能为undefined
      const openingRound = data.round || battleState.currentRound.value
      console.log('[🎰BATTLE-CORE] 开箱轮次信息:', {
        messageRound: data.round,
        localCurrentRound: battleState.currentRound.value,
        usingRound: openingRound
      })
      
      // 🎯 清除之前的开箱状态，准备设置新的开箱状态
      battleState.resetOpeningState()
      
      // 启动动画
      battleAnimations.startOpeningAnimation(data)
      
      // 🎯 同步开箱状态到battleState，方便UI组件显示"开箱中"
      try {
        // 🎯 关键修复：使用openingRound而不是currentRound，确保与动画执行逻辑一致
        // openingRound是当前正在开箱的轮次，currentRound可能已经推进到下一轮
        const roundIdx = openingRound > 0 ? openingRound - 1 : 0
        const currentCase = battleState.displayCases.value[roundIdx]
        
        if (currentCase) {
          console.log('[🎰BATTLE-CORE] 设置开箱状态:', {
            caseId: currentCase.id,
            caseName: currentCase.name,
            roundIdx,
            openingRound,
            currentRound: battleState.currentRound.value,
            totalCases: battleState.displayCases.value.length
          })
          // 修复参数顺序：setOpeningState(caseId, playerName, playerIndex, roundIndex)
          console.log('[🎰BATTLE-CORE] 调用setOpeningState:', {
            caseId: currentCase.id,
            playerName: '正在开箱',
            playerIndex: -1,
            roundIdx,
            openingRound,
            currentRound: battleState.currentRound.value
          });
          battleState.setOpeningState(currentCase.id, '正在开箱', -1, roundIdx)
        } else {
          console.warn('[🎰BATTLE-CORE] 未找到当前轮次对应的箱子:', {
            roundIdx,
            openingRound,
            currentRound: battleState.currentRound.value,
            displayCasesLength: battleState.displayCases.value.length,
            displayCases: battleState.displayCases.value.map((c, i) => ({ index: i, name: c.name }))
          })
        }
      } catch (err) {
        console.warn('[🎰BATTLE-CORE] 设置开箱状态失败', err)
      }
      
      // 播放音效
      if (battleAnimations.animationConfig.value.enableSound) {
        battleAnimations.playOpeningSound()
      }
    })

    // 回合结果事件
    battleWebSocket.onRoundResult((data) => {
      console.log('[🎰BATTLE-CORE] 回合结果事件:', data)
      
      // ✅ 更新battleData的state字段
      battleState.updateBattleData({ state: 5 }) // 5 = IN_PROGRESS
      
      battleState.battleState.value = BattleStatus.IN_PROGRESS
      
      // 处理结果数据
      if (data.results) {
        data.results.forEach((result: any) => {
          if (result.user && result.items) {
            const playerUid = result.user.uid || result.user.username
            const item = result.items[0] // 取第一个物品
            if (playerUid && item) {
              battleState.updatePlayerRoundResult(playerUid, item)
            }
          }
        })
      }

      // 标记轮次完成
      battleState.roundCompleted.value = true
      
      // 🎯 修复：不要在轮次结果时清除开箱状态，保持当前轮次的开箱状态
      // 开箱状态将在下一个opening_start事件到达时自动更新
      console.log('[🎰BATTLE-CORE] 轮次完成，保持开箱状态直到下一个opening_start')
      
      // 延迟进入下一轮或结束
      setTimeout(() => {
        if (battleState.currentRound.value < battleState.totalRounds.value) {
          battleState.nextRound()
        } else {
          // ✅ 对战结束时更新battleData状态
          battleState.updateBattleData({ state: 11 }) // 11 = COMPLETED
          battleState.battleState.value = BattleStatus.COMPLETED
          battleState.showFinalCalculation()
        }
      }, 3000)
    })

    // 对战结束事件
    battleWebSocket.onBattleEnd((data) => {
      console.log('[🎰BATTLE-CORE] 对战结束事件:', data)
      
      // ✅ 更新battleData的state字段
      battleState.updateBattleData({ state: 11 }) // 11 = COMPLETED
      
      battleState.battleState.value = BattleStatus.COMPLETED
      
      // 🎯 关键修复：不要立即清除开箱状态，让计算动画先完成
      // battleState.resetOpeningState()
      
      // 处理最终结果
      if (data.winner) {
        battleState.finalWinner.value = data.winner
      }
      
      if (data.final_results) {
        // 更新最终结果数据
        data.final_results.forEach((result: any) => {
          const player = battleState.allPlayers.value.find((p: any) => p.uid === result.user?.uid)
          if (player) {
            player.isWinner = result.victory === 1
            player.winAmount = result.win_amount || 0
          }
        })
      }

      // 🎯 关键修复：确保计算动画能正常显示
      // 如果计算动画还没有显示，先显示计算动画
      if (!battleState.showCalculationAnimation.value) {
        console.log('[🎰BATTLE-CORE] 显示计算动画')
        battleState.showFinalCalculation()
      }

      // 🎯 延迟清除开箱状态和显示结果弹窗，确保计算动画有足够时间显示
      setTimeout(() => {
        console.log('[🎰BATTLE-CORE] 清除开箱状态并显示胜利者弹窗')
        battleState.resetOpeningState()
        battleState.closeCalculationAnimation()
        // 🎯 使用BattleWinnerModal，触发页面组件中的showWinnerModal
        // 这里不需要直接设置showResultModal，而是让页面组件自己处理
      }, 8000) // 给计算动画足够的时间显示（8秒）
    })

    // 房间取消事件
    battleWebSocket.onRoomCancel((data: any) => {
      console.log('[🎰BATTLE-CORE] 房间取消事件:', data)
      
      // ✅ 更新battleData的state字段
      battleState.updateBattleData({ state: 20 }) // 20 = CANCELLED
      
      battleState.battleState.value = BattleStatus.CANCELLED
      battleState.error.value = data.cancel_reason || t('battle.detail.room_cancelled')
    })
  }

  // 🎯 重新初始化（用于路由变化）
  const reinitialize = async () => {
    console.log('[🎰BATTLE-CORE] 重新初始化Battle核心')
    await cleanup()
    isInitialized.value = false
    isDestroyed.value = false
    await initialize()
  }

  // 🎯 清理资源
  const cleanup = async () => {
    if (isDestroyed.value) return

    console.log('[🎰BATTLE-CORE] 清理Battle核心资源')
    
    try {
      // 清理WebSocket
      await battleWebSocket.cleanup()
      
      // 清理动画
      battleAnimations.resetAnimationState()
      
      // 重置状态 - 使用现有的重置方法
      battleState.resetOpeningState()
      battleState.error.value = null
      battleState.battleData.value = null
      
      isDestroyed.value = true
      console.log('[🎰BATTLE-CORE] Battle核心资源清理完成')
    } catch (error) {
      console.error('[🎰BATTLE-CORE] 清理资源失败:', error)
    }
  }

  // 🎯 监听路由变化 - 优化初始化逻辑
  watch(
    () => route.params.id,
    (newId, oldId) => {
      console.log('[CORE-DBG] route watch triggered', { newId, oldId })
      if (newId && typeof newId === 'string' && newId !== oldId) {
        console.log('[CORE-DBG] calling initialize()')
        initialize()
      }
    },
    { immediate: true } // ✅ 立即执行，确保首次进入页面时触发
  )

  // 🎯 页面可见性变化处理
  if (process.client) {
    document.addEventListener('visibilitychange', () => {
      if (document.visibilityState === 'visible' && battleWebSocket.connectionState.value.isConnected) {
        console.log('[🎰BATTLE-CORE] 页面重新可见，请求最新数据')
        battleWebSocket.requestBattleData()
      }
    })
  }

  // 🎯 组件卸载时清理
  onBeforeUnmount(() => {
    cleanup()
  })

  // 🎯 开发环境测试函数
  const startTestAnimation = () => {
    if (!process.dev) return
    
    const testData = {
      animation_id: `test_${Date.now()}`,
      animation_start_timestamp: Date.now() + 2000,
      server_timestamp: Date.now(),
      participants: battleState.allPlayers.value.map((player: any) => ({
        user: { username: player.nickname },
        animation_duration: 8000
      })),
      sync_config: {
        tolerance_ms: 100,
        max_delay_compensation: 500
      }
    }
    
    battleAnimations.startOpeningAnimation(testData)
  }

  // 🎯 检查对战状态，决定返回WebSocket接口
  const createWebSocketInterface = () => {
    const battleData = battleState.battleData.value
    const isFinished = battleData && (battleData.state === 11 || battleData.state === 20) // 11=FINISHED, 20=CANCELLED
    
    if (isFinished) {
      console.log('[🎰BATTLE-CORE] 对战已结束，返回简化WebSocket接口')
      // 为已结束的对战提供简化的WebSocket接口（避免依赖实时连接）
      return {
        connectionState: ref({
          isConnected: false,
          isReconnecting: false,
          reconnectAttempts: 0,
          lastError: null
        }),
        animationState: ref({
          currentAnimationId: null,
          animationProgress: 0,
          animationStage: 'completed',
          participantStates: new Map()
        }),
        timeSync: ref({
          clockOffset: 0,
          networkDelay: 0,
          lastSyncTime: 0,
          syncQuality: 'good'
        }),
        getMessageStats: () => ({ received: 0, sent: 0, errors: 0 }),
        validateStateConsistency: (): boolean => true
      }
    } else {
      console.log('[🎰BATTLE-CORE] 对战进行中，返回完整WebSocket接口')
      // 为进行中的对战提供完整的WebSocket接口
      return {
        connectionState: battleWebSocket.connectionState,
        animationState: battleWebSocket.animationState,
        timeSync: battleWebSocket.timeSync,
        getMessageStats: battleWebSocket.getMessageStats,
        validateStateConsistency: (): boolean => {
          try {
            const result = battleWebSocket.validateStateConsistency()
            return result && typeof result === 'object' && 'isConsistent' in result ? Boolean(result.isConsistent) : false
          } catch {
            return false
          }
        }
      }
    }
  }

  // 🎯 按照接口定义返回对象
  return {
    // 🎯 状态管理
    battleState: {
      isLoading: battleState.isLoading,
      error: battleState.error,
      battleData: battleState.battleData,
      battleState: battleState.battleState,
      currentRound: battleState.currentRound,
      totalRounds: battleState.totalRounds,
      displayCases: battleState.displayCases,
      allPlayers: battleState.allPlayers,
      openingPlayerName: battleState.openingPlayerName,
      openingCaseId: battleState.openingCaseId,
      openingPlayerIndex: battleState.openingPlayerIndex,
      isUserJoined: battleState.isUserJoined,
      isUserCreator: battleState.isUserCreator,
      isBattleWaiting: battleState.isBattleWaiting,
      isBattleInProgress: battleState.isBattleInProgress,
      isBattleCompleted: battleState.isBattleCompleted,
      isBattleCancelled: battleState.isBattleCancelled,
      isBattleFinished: battleState.isBattleFinished,
      showCalculationAnimation: battleState.showCalculationAnimation,
      showResultModal: battleState.showResultModal,
      finalWinner: battleState.finalWinner,
      closeCalculationAnimation: battleState.closeCalculationAnimation,
      closeResultModal: battleState.closeResultModal
    },
    
    // 🎯 WebSocket 管理（智能切换）
    battleWebSocket: createWebSocketInterface(),
    
    // 🎯 操作方法
    battleActions: {
      showToast: battleActions.showToast,
      toastMessage: battleActions.toastMessage,
      toastType: battleActions.toastType,
      handleJoinBattle: battleActions.handleJoinBattle,
      handleLeaveBattle: battleActions.handleLeaveBattle,
      handleDismissBattle: battleActions.handleDismissBattle,
      handleStartBattle: battleActions.handleStartBattle,
      handleCopyId: battleActions.handleCopyId,
      handleShare: battleActions.handleShare,
      getToastIcon: battleActions.getToastIcon,
      hideToast: battleActions.hideToast
    },
    
    // 🎯 核心方法
    initialize,
    cleanup,
    startTestAnimation
  }
} 