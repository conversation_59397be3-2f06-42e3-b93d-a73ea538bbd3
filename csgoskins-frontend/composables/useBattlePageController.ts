/**
 * 对战页面控制器
 * 实现智能连接策略：根据对战状态选择静态模式或实时模式
 */
import { ref, computed, onUnmounted, watch, readonly } from 'vue'
import { useNuxtApp } from '#app'
import { useBattleStaticData } from './useBattleStaticData'

export interface BattlePageState {
  isInitialized: boolean
  isRealtimeMode: boolean
  isLoading: boolean
  error: string | null
  connectionStatus: {
    isConnected: boolean
    isReconnecting: boolean
    lastConnectedTime: Date | null
  }
}

export const useBattlePageController = () => {
  // 状态管理
  const pageState = ref<BattlePageState>({
    isInitialized: false,
    isRealtimeMode: false,
    isLoading: false,
    error: null,
    connectionStatus: {
      isConnected: false,
      isReconnecting: false,
      lastConnectedTime: null
    }
  })

  // 数据管理器
  const staticDataManager = useBattleStaticData()

  // 当前对战数据
  const battleData = ref<any>(null)
  const hasCalculated = ref(false)
  const hasShownWinnerModal = ref(false)

  // 统一的轮次数据管理
  const currentRound = ref(1)
  const totalRounds = ref(1)
  const staticRoundResults = ref<any[]>([])
  const winnerData = ref<any>(null)
  const currentCaseItems = ref<any[]>([])
  const displayCases = ref<any[]>([])

  // 用户状态检查函数
  const checkIsUserCreator = (battleData: any) => {
    const userStore = useUserStore()
    return battleData?.user?.uid === userStore.userId
  }

  const checkIsUserJoined = (battleData: any) => {
    const userStore = useUserStore()
    return battleData?.bets?.some((bet: any) => bet.user?.uid === userStore.userId) || false
  }

  /**
   * 检查对战是否已结束
   */
  const isBattleFinished = (state: number): boolean => {
    // 11: 计算完成, 20: 已结束
    return state === 11 || state === 20
  }

  /**
   * 加载对战基本信息（用于判断模式）
   */
  const loadBattleBasicData = async (battleId: string) => {
    console.log('[🎰CONTROLLER] 加载对战基本信息:', battleId)
    
    try {
      const response = await $fetch<{
        code: number
        message?: string
        body: any
      }>(`/api/box/battle/detail/`, {
        params: { uid: battleId },
        method: 'GET'
      })
      
      if (response.code !== 0) {
        throw new Error(response.message || '获取对战信息失败')
      }
      
      return response.body
    } catch (error) {
      console.error('[🎰CONTROLLER] 加载基本信息失败:', error)
      throw error
    }
  }

  /**
   * 初始化静态模式（已结束的对战）
   */
  const initializeStaticMode = async (data: any) => {
    console.log('[🎰CONTROLLER] 初始化静态模式')
    
    try {
      // 设置静态数据
      battleData.value = data
      pageState.value.isRealtimeMode = false
      
      // 统一设置轮次数据
      totalRounds.value = data.round_count || 1
      currentRound.value = totalRounds.value // 静态模式下当前轮次就是总轮次
      
      // 设置已完成状态
      hasCalculated.value = true
      hasShownWinnerModal.value = false // 允许显示胜利者信息
      
      // 处理静态轮次结果
      staticRoundResults.value = staticDataManager.processStaticResults(data)
      
      // 获取胜利者数据
      winnerData.value = staticDataManager.getFinalWinner(data)
      
      // 设置箱子数据：从 rounds 派生
      if (Array.isArray(data.rounds) && data.rounds.length > 0) {
        displayCases.value = data.rounds.map((r: any) => ({
          id: r.case.case_key,
          key: r.case.case_key,
          case_key: r.case.case_key,
          name: r.case.name,
          name_en: r.case.name_en,
          name_zh_hans: r.case.name_zh_hans,
          cover: r.case.cover,
          price: r.case.price,
          count: 1
        }))
        // 静态模式下当前案例项可留空或按需使用
        currentCaseItems.value = []
      }
      
      // 预加载静态资源（性能优化）
      await staticDataManager.preloadStaticAssets(data)
      
      console.log('[🎰CONTROLLER] 静态模式初始化完成:', {
        totalRounds: totalRounds.value,
        currentRound: currentRound.value,
        staticResults: staticRoundResults.value.length,
        winnerData: winnerData.value?.user?.username
      })
      
      return staticRoundResults.value
      
    } catch (error) {
      console.error('[🎰CONTROLLER] 静态模式初始化失败:', error)
      throw error
    }
  }

  /**
   * 初始化实时模式（进行中的对战）
   */
  const initializeRealtimeMode = async (data: any) => {
    console.log('[🎰CONTROLLER] 初始化实时模式')
    
    try {
      // 设置实时数据
      battleData.value = data
      pageState.value.isRealtimeMode = true
      
      // 初始化轮次数据
      totalRounds.value = data.round_count || 1
      currentRound.value = data.current_round || 1
      
      // 初始化箱子数据
      if (data.cases && data.cases.length > 0) {
        displayCases.value = data.cases
        currentCaseItems.value = data.cases.flatMap((c: any) => c.items || [])
      }
      
      console.log('[🎰CONTROLLER] 实时模式初始化完成:', {
        totalRounds: totalRounds.value,
        currentRound: currentRound.value,
        casesCount: displayCases.value.length
      })
      
    } catch (error) {
      console.error('[🎰CONTROLLER] 实时模式初始化失败:', error)
      throw error
    }
  }

  /**
   * 智能连接策略主入口
   */
  const initializeBattleMode = async (battleId: string) => {
    try {
      pageState.value.isLoading = true
      pageState.value.error = null
      
      console.log('[🎰CONTROLLER] 开始智能连接策略:', battleId)
      
      // 1. 首先获取对战基本信息
      const basicData = await loadBattleBasicData(battleId)
      
      // 2. 智能判断连接模式
      const isFinished = isBattleFinished(basicData.state)
      
      if (isFinished) {
        console.log('[🎰CONTROLLER] ✅ 已结束对战，使用静态数据模式')
        console.log('[🎰CONTROLLER] 对战状态:', basicData.state, '更新时间:', basicData.update_time)
        
        // 静态模式：仅使用API数据，不初始化WebSocket连接
        await initializeStaticMode(basicData)
        
        // 直接展示最终结果
        showFinalResults(basicData)
        
      } else {
        console.log('[🎰CONTROLLER] ⚡ 进行中对战，启用实时同步模式')
        console.log('[🎰CONTROLLER] 对战状态:', basicData.state, '创建时间:', basicData.create_time)
        
        // 实时模式：启用WebSocket同步
        await initializeRealtimeMode(basicData)
      }
      
      pageState.value.isInitialized = true
      console.log('[🎰CONTROLLER] 智能连接策略完成，模式:', isFinished ? '静态' : '实时')
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      pageState.value.error = errorMessage
      console.error('[🎰CONTROLLER] 初始化失败:', error)
      
      // 处理初始化错误
      handleInitializationError(error)
    } finally {
      pageState.value.isLoading = false
    }
  }

  /**
   * 显示最终结果（静态模式）
   */
  const showFinalResults = (data: any) => {
    console.log('[🎰CONTROLLER] 显示最终结果')
    
    // 获取胜利者信息
    const winner = staticDataManager.getFinalWinner(data)
    if (winner) {
      console.log('[🎰CONTROLLER] 对战胜利者:', winner.user?.username, '获胜金额:', winner.winAmount)
    }
    
    // 设置显示胜利者弹窗的标志
    // 这里可以根据需要控制是否自动显示弹窗
    setTimeout(() => {
      hasShownWinnerModal.value = true
    }, 1000) // 延迟1秒显示，给用户查看结果的时间
  }

  /**
   * 处理初始化错误
   */
  const handleInitializationError = (error: any) => {
    console.error('[🎰CONTROLLER] 处理初始化错误:', error)
    
    // 根据错误类型进行不同处理
    if (error.message?.includes('网络')) {
      // 网络错误，可能需要重试
      pageState.value.error = '网络连接失败，请检查网络后重试'
    } else if (error.message?.includes('未找到')) {
      // 对战不存在
      pageState.value.error = '对战不存在或已被删除'
    } else {
      // 其他错误
      pageState.value.error = '加载对战失败，请刷新页面重试'
    }
  }

  /**
   * 处理对战操作（如重新开始、查看详情等）
   */
  const handleBattleAction = async (action: string, payload?: any) => {
    console.log('[🎰CONTROLLER] 处理对战操作:', action, payload)
    
    try {
      switch (action) {
        case 'refresh-data':
          // 重新加载数据
          if (battleData.value?.uid) {
            await initializeBattleMode(battleData.value.uid)
          }
          break
          
        case 'show-winner-modal':
          hasShownWinnerModal.value = true
          break
          
        case 'join-battle':
          // 实时模式下的加入对战逻辑
          console.log('[🎰CONTROLLER] 加入对战请求')
          try {
            // 使用全局 $fetch 发起加入对战请求，以触发 CSRF 拦截器
            const roomUid = battleData.value?.uid
            if (!roomUid) throw new Error('房间UID不存在')
            const joinResp = await (globalThis as any).$fetch('/api/box/battle/join/', {
              method: 'POST',
              body: { uid: roomUid, team: 1 }
            }) as { code: number; message?: string; body: any }
            if (joinResp.code !== 0) {
              throw new Error(joinResp.message || '加入对战失败')
            }
            // 更新本地对战数据并刷新页面
            Object.assign(battleData.value, joinResp.body)
            await initializeBattleMode(battleData.value.uid)
          } catch (err: any) {
            console.error('[🎰CONTROLLER] 加入对战失败:', err)
            pageState.value.error = err.message || '加入对战失败'
          }
          break

        case 'quit-battle':
          // 实时模式下的退出对战逻辑
          console.log('[🎰CONTROLLER] 退出对战请求')
          try {
            const roomUid = battleData.value?.uid
            if (!roomUid) throw new Error('房间UID不存在')

            // 使用BattleApi类来确保正确的认证和CSRF token
            const { BattleApi } = await import('~/services/battle-api')
            const battleApi = new BattleApi()
            const quitResp = await battleApi.leaveRoom(roomUid)

            if (!quitResp.success) {
              throw new Error(quitResp.message || '退出对战失败')
            }

            // 退出成功后导航到对战列表页面
            console.log('[🎰CONTROLLER] 退出对战成功，导航到对战列表')
            setTimeout(() => {
              navigateTo('/battle')
            }, 1000)
          } catch (err: any) {
            console.error('[🎰CONTROLLER] 退出对战失败:', err)
            pageState.value.error = err.message || '退出对战失败'
          }
          break

        case 'dismiss-battle':
          // 实时模式下的解散对战逻辑
          console.log('[🎰CONTROLLER] 解散对战请求')
          try {
            const roomUid = battleData.value?.uid
            if (!roomUid) throw new Error('房间UID不存在')

            // 使用BattleApi类来确保正确的认证和CSRF token
            const { BattleApi } = await import('~/services/battle-api')
            const battleApi = new BattleApi()
            const dismissResp = await battleApi.dismissRoom(roomUid)

            if (!dismissResp.success) {
              throw new Error(dismissResp.message || '解散对战失败')
            }

            // 解散成功后导航到对战列表页面
            console.log('[🎰CONTROLLER] 解散对战成功，导航到对战列表')
            setTimeout(() => {
              navigateTo('/battle')
            }, 1000)
          } catch (err: any) {
            console.error('[🎰CONTROLLER] 解散对战失败:', err)
            pageState.value.error = err.message || '解散对战失败'
          }
          break
        
        case 'start-battle':
          // 实时模式下的开始对战逻辑
          console.log('[🎰CONTROLLER] 开始对战请求')
          break
          
        default:
          console.warn('[🎰CONTROLLER] 未知操作:', action)
      }
    } catch (error) {
      console.error('[🎰CONTROLLER] 操作失败:', action, error)
    }
  }

  /**
   * 清理资源
   */
  const cleanup = () => {
    console.log('[🎰CONTROLLER] 清理资源')
    
    // 清理静态数据
    staticDataManager.clearStaticData()
    
    // 重置状态
    pageState.value.isInitialized = false
    battleData.value = null
    hasCalculated.value = false
    hasShownWinnerModal.value = false
  }

  // 组件卸载时清理资源
  onUnmounted(() => {
    cleanup()
  })

  // 计算属性
  const isRealtimeMode = computed(() => pageState.value.isRealtimeMode)
  const isConnected = computed(() => pageState.value.connectionStatus.isConnected)
  const canShowWinnerModal = computed(() => {
    return hasCalculated.value && !hasShownWinnerModal.value && battleData.value
  })

  return {
    // 状态
    pageState: readonly(pageState),
    battleData: readonly(battleData),
    hasCalculated: readonly(hasCalculated),
    hasShownWinnerModal: readonly(hasShownWinnerModal),
    
    // 统一的轮次数据
    currentRound: readonly(currentRound),
    totalRounds: readonly(totalRounds),
    staticRoundResults: readonly(staticRoundResults),
    winnerData: readonly(winnerData),
    currentCaseItems: readonly(currentCaseItems),
    displayCases: readonly(displayCases),
    
    // 计算属性
    isRealtimeMode,
    isConnected,
    canShowWinnerModal,
    
    // 方法
    initializeBattleMode,
    handleBattleAction,
    cleanup,
    checkIsUserCreator,
    checkIsUserJoined,
    
    // 子管理器
    staticDataManager
  }
}
