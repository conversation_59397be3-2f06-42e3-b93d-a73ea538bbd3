// 🚀 Battle WebSocket事件处理 Composable
// 根据WebSocket API文档v2024-12-29实现

export const useBattleWebSocket = (battleId: string) => {
  const { t } = useI18n()
  const socketStore = useSocketStore()
  
  // 🎯 集成新的WebSocket处理器
  const wsHandler = useBattleWebSocketHandler()
  
  // 🎯 响应式状态
  const connectionState = ref({
    isConnected: false,
    isReconnecting: false,
    reconnectAttempts: 0,
    lastError: null as string | null
  })
  
  const animationState = ref({
    currentAnimationId: null as string | null,
    animationProgress: 0,
    animationStage: 'idle' as 'idle' | 'countdown' | 'opening' | 'revealing' | 'completed',
    participantStates: new Map<string, 'waiting' | 'animating' | 'completed'>()
  })
  
  const timeSync = ref({
    clockOffset: 0,
    networkDelay: 0,
    lastSyncTime: 0,
    syncQuality: 'good' as 'good' | 'poor' | 'bad'
  })
  
  // 🎯 事件回调函数
  const callbacks = {
    onRoomUpdate: null as ((data: any) => void) | null,
    onBattleStart: null as ((data: any) => void) | null,
    onRoundStart: null as ((data: any) => void) | null,
    onOpeningStart: null as ((data: any) => void) | null,
    onAnimationProgress: null as ((data: any) => void) | null,
    onRoundResult: null as ((data: any) => void) | null,
    onBattleEnd: null as ((data: any) => void) | null,
    onRoomCancel: null as ((data: any) => void) | null
  }
  
  // 🎯 接受的房间标识符集合（uid或short_id）
  const acceptedIds = ref<string[]>([battleId])

  // 🎯 调试：显示当前注册的房间ID
  console.warn('[🎰BATTLE-WS] ✅ 初始化WebSocket，注册房间ID:', battleId)

  const isTargetRoom = (data: any): boolean => {
    const ids = acceptedIds.value
    return ids.includes(data.uid) || ids.includes(data.short_id)
  }

  const registerAdditionalId = (id: string) => {
    if (id && !acceptedIds.value.includes(id)) {
      acceptedIds.value.push(id)
    }
  }

  // 🎯 根据WebSocket API文档的正确事件监听方式
  const setupSocketListeners = () => {
    if (!process.client) return

    console.log('[🎰BATTLE-WEBSOCKET] 设置Socket监听器')
    console.log('[ANIM-WS]', 'setupSocketListeners')

    // ✅ 修复：使用API文档要求的正确事件名称，与Socket插件分发的事件完全匹配
    // Socket插件分发: socket:round_start, socket:opening_start, socket:round_result, socket:battle_end
    window.addEventListener('socket:round_start', handleRoundStart as EventListener)
    window.addEventListener('socket:opening_start', handleOpeningStart as EventListener)
    window.addEventListener('socket:round_result', handleRoundResult as EventListener)
    window.addEventListener('socket:battle_end', handleBattleEnd as EventListener)
    
    // 房间相关事件（boxroom）- ✅ 修复：使用Socket插件实际分发的事件名称
    window.addEventListener('socket:room_updated', handleRoomUpdate as EventListener)
    window.addEventListener('socket:battle_started', handleBattleStart as EventListener)  
    window.addEventListener('socket:room_cancelled', handleRoomCancel as EventListener)
    
    // 连接状态管理
    window.addEventListener('socket:connected', handleSocketConnected as EventListener)
    window.addEventListener('socket:disconnected', handleSocketDisconnected as EventListener)

    window.addEventListener('socket:animation_progress', handleAnimationProgress as EventListener)

    // ✅ 修复：额外监听 stores/socket.ts 派发的 "socket-battle-*" 事件前缀，保持两页面一致
    const battleEventBindings: Array<[string, EventListener]> = [
      ['socket-battle-update', handleRoomUpdate as EventListener],
      ['socket-battle-start', handleBattleStart as EventListener],
      ['socket-battle-cancel', handleRoomCancel as EventListener],
      ['socket-battle-round', handleRoundStart as EventListener],
      ['socket-battle-detail-end', handleBattleEnd as EventListener]
    ]

    battleEventBindings.forEach(([eventName, handler]: [string, EventListener]) => {
      window.addEventListener(eventName, handler)
    })

    // 将绑定信息存储，便于移除
    ;(window as any).__battleEventBindings = battleEventBindings

    console.log('[🎰BATTLE-WEBSOCKET] ✅ Socket监听器设置完成')
  }
  
  // 🎯 移除事件监听器
  const removeSocketListeners = () => {
    if (!process.client) return

    console.log('[🎰BATTLE-WEBSOCKET] 移除Socket监听器')

    // ✅ 修复：使用API文档要求的正确事件名称
    window.removeEventListener('socket:round_start', handleRoundStart as EventListener)
    window.removeEventListener('socket:opening_start', handleOpeningStart as EventListener)
    window.removeEventListener('socket:round_result', handleRoundResult as EventListener)
    window.removeEventListener('socket:battle_end', handleBattleEnd as EventListener)
    
    // 房间相关事件（boxroom）- ✅ 修复：使用Socket插件实际分发的事件名称
    window.removeEventListener('socket:room_updated', handleRoomUpdate as EventListener)
    window.removeEventListener('socket:battle_started', handleBattleStart as EventListener)
    window.removeEventListener('socket:room_cancelled', handleRoomCancel as EventListener)
    
    // 连接状态管理
    window.removeEventListener('socket:connected', handleSocketConnected as EventListener)
    window.removeEventListener('socket:disconnected', handleSocketDisconnected as EventListener)

    window.removeEventListener('socket:animation_progress', handleAnimationProgress as EventListener)

    // 🎯 移除 stores/socket.ts 派发的 "socket-battle-*" 事件监听器
    const battleEventBindings = (window as any).__battleEventBindings
    if (battleEventBindings) {
      battleEventBindings.forEach(([eventName, handler]: [string, EventListener]) => {
        window.removeEventListener(eventName, handler)
      })
    }

    console.log('[🎰BATTLE-WEBSOCKET] ✅ Socket监听器移除完成')
  }
  
  // 🎯 事件处理函数
  const handleRoomUpdate = (event: CustomEvent) => {
    const data = event.detail
    console.log('[🎰BATTLE-WS] 房间更新事件触发:', data)
    
    if (!isTargetRoom(data)) {
      console.warn('[🎰BATTLE-WS] ❌ 房间ID不匹配，跳过处理:', {
        acceptedIds: Array.from(acceptedIds.value),
        messageUid: data.uid,
        messageShortId: data.short_id,
        currentBattleId: battleId,
        dataId: data.uid || data.short_id
      })
      return
    }
    
    // 🎯 使用新的统一WebSocket处理器
    wsHandler.handleWebSocketMessage('boxroom', 'update', data)
    
    // ✅ 修复：WebSocket消息字段映射
    // WebSocket消息: participants/users → API格式: bets
    const mappedData = { ...data }
    
    // 处理participants字段映射（boxroomdetail消息）
    if (data.participants && !data.bets) {
      mappedData.bets = data.participants
      console.log('[🎰BATTLE-WS] 字段映射: participants → bets', { 
        participantsCount: data.participants.length, 
        betsCount: mappedData.bets.length 
      })
    }
    
    // 处理users字段映射（boxroom消息）
    if (data.users && !data.bets) {
      mappedData.bets = data.users
      console.log('[🎰BATTLE-WS] 字段映射: users → bets', { 
        usersCount: data.users.length, 
        betsCount: mappedData.bets.length 
      })
    }
    
    console.log('[🎰BATTLE-WS] ✅ 调用房间更新回调')
    callbacks.onRoomUpdate?.(mappedData)
  }
  
  const handleBattleStart = (event: CustomEvent) => {
    const data = event.detail
    console.log('[🎰BATTLE-WS] 对战开始事件触发:', data)
    
    if (!isTargetRoom(data)) {
      console.warn('[🎰BATTLE-WS] ❌ 房间ID不匹配，跳过处理:', { ids: Array.from(acceptedIds.value), dataId: data.uid || data.short_id })
      return
    }
    
    // 🎯 使用新的统一WebSocket处理器
    wsHandler.handleWebSocketMessage('boxroom', 'start', data)
    
    // ✅ 修复：WebSocket消息字段映射
    // WebSocket消息: participants/users → API格式: bets
    const mappedData = { ...data }
    
    // 处理participants字段映射（boxroomdetail消息）
    if (data.participants && !data.bets) {
      mappedData.bets = data.participants
      console.log('[🎰BATTLE-WS] 字段映射: participants → bets', { 
        participantsCount: data.participants.length, 
        betsCount: mappedData.bets.length 
      })
    }
    
    // 处理users字段映射（boxroom消息）
    if (data.users && !data.bets) {
      mappedData.bets = data.users
      console.log('[🎰BATTLE-WS] 字段映射: users → bets', { 
        usersCount: data.users.length, 
        betsCount: mappedData.bets.length 
      })
    }
    
    console.log('[🎰BATTLE-WS] ✅ 调用对战开始回调')
    callbacks.onBattleStart?.(mappedData)
  }
  
  const handleRoomCancel = (event: CustomEvent) => {
    const data = event.detail
    console.log('[🎰BATTLE-WS] 房间取消事件触发:', data)
    
    if (!isTargetRoom(data)) {
      console.warn('[🎰BATTLE-WS] ❌ 房间ID不匹配，跳过处理:', { ids: Array.from(acceptedIds.value), dataId: data.uid || data.short_id })
      return
    }
    
    // 🎯 使用新的统一WebSocket处理器
    wsHandler.handleWebSocketMessage('boxroom', 'cancel', data)
    
    console.log('[🎰BATTLE-WS] ✅ 调用房间取消回调')
    callbacks.onRoomCancel?.(data)
  }
  
  const handleRoundStart = (event: CustomEvent) => {
    const { data, socketId, timestamp } = event.detail
    console.log('[🎰BATTLE-WS] 回合开始事件触发:', data)
    
    // 🎯 使用新的统一WebSocket处理器
    wsHandler.handleWebSocketMessage('boxroomdetail', 'round_start', data, socketId)
    
    // ✅ 字段映射：participants/users → bets，保持数据结构一致
    const mappedData = { ...data }
    if (data.participants && !data.bets) {
      mappedData.bets = data.participants
    }
    if (data.users && !data.bets) {
      mappedData.bets = data.users
    }
    
    // 更新动画状态
    animationState.value.animationStage = 'countdown'
    
    // 处理时间戳同步
    if (data.round_start_timestamp && data.server_timestamp) {
      updateTimeSync(data.server_timestamp, timestamp)
    }
    
    console.log('[🎰BATTLE-WS] ✅ 调用回合开始回调')
    callbacks.onRoundStart?.(mappedData)
  }
  
  const handleOpeningStart = (event: CustomEvent) => {
    const { data, socketId, timestamp } = event.detail
    console.log('[🎰BATTLE-WS] 开箱开始事件触发:', data)
    console.log('[ANIM-WS]', 'handleOpeningStart', 'animation_id:', data.animation_id)
    
    // 🎯 使用新的统一WebSocket处理器
    wsHandler.handleWebSocketMessage('boxroomdetail', 'opening_start', data, socketId)
    
    // ✅ 字段映射：participants/users → bets
    const mappedData = { ...data }
    if (data.participants && !data.bets) {
      mappedData.bets = data.participants
    }
    if (data.users && !data.bets) {
      mappedData.bets = data.users
    }
    
    // 更新动画状态
    animationState.value.currentAnimationId = data.animation_id
    animationState.value.animationStage = 'opening'
    animationState.value.animationProgress = 0
    
    // 处理时间戳同步
    if (data.animation_start_timestamp && data.server_timestamp) {
      updateTimeSync(data.server_timestamp, timestamp)
    }
    
    console.log('[🎰BATTLE-WS] ✅ 调用开箱开始回调')
    callbacks.onOpeningStart?.(mappedData)
  }
  
  const handleAnimationProgress = (event: CustomEvent) => {
    const { data, socketId, timestamp } = event.detail
    console.log('[🎰BATTLE-WS] 动画进度:', data)
    
    // ✅ 字段映射：participants/users → bets（某些实现可能在进度消息中包含参与者更新）
    const mappedData = { ...data }
    if (data.participants && !data.bets) {
      mappedData.bets = data.participants
    }
    if (data.users && !data.bets) {
      mappedData.bets = data.users
    }
    
    // 只处理当前动画的进度
    if (data.animation_id === animationState.value.currentAnimationId) {
      animationState.value.animationProgress = data.progress || 0
      animationState.value.animationStage = data.stage || 'opening'
    }
    
    callbacks.onAnimationProgress?.(mappedData)
  }
  
  const handleRoundResult = (event: CustomEvent) => {
    // ✅ 兼容legacy事件格式：{ data, socketId } 或新格式：{ data, socketId, timestamp }
    const { data, socketId, timestamp } = event.detail
    console.log('[🎰BATTLE-WS] 回合结果/Legacy轮次:', data)
    
    // 🎯 使用新的统一WebSocket处理器
    wsHandler.handleWebSocketMessage('boxroomdetail', 'round_result', data, socketId)
    
    // 更新动画状态
    animationState.value.animationStage = 'revealing'
    
    // ✅ 修复：处理回合结果数据映射
    // API文档: round_result消息包含 results 数组
    const mappedData = { ...data }
    
    // 确保results字段存在且格式正确
    if (data.results && Array.isArray(data.results)) {
      console.log('[🎰BATTLE-WS] 回合结果包含results数据:', data.results.length, '个玩家')
    } else if (data.participants) {
      // 如果有participants但没有results，创建results格式
      mappedData.results = data.participants.map((participant: any) => ({
        user: participant.user,
        items: participant.items || []
      }))
      console.log('[🎰BATTLE-WS] 字段映射: participants → results', mappedData.results.length, '个玩家')
    }
    
    callbacks.onRoundResult?.(mappedData)
  }
  
  const handleBattleEnd = (event: CustomEvent) => {
    // ✅ 兼容legacy事件格式：{ data, socketId } 或新格式：{ data, socketId, timestamp }
    const { data, socketId, timestamp } = event.detail
    console.log('[🎰BATTLE-WS] 对战结束/Legacy结束:', data)
    
    // 🎯 使用新的统一WebSocket处理器
    wsHandler.handleWebSocketMessage('boxroomdetail', 'battle_end', data, socketId)
    
    // 更新动画状态
    animationState.value.animationStage = 'completed'
    
    // ✅ 修复：处理对战结束数据映射
    const mappedData = { ...data }
    
    // 处理participants字段映射（boxroomdetail消息）
    if (data.participants && !data.bets) {
      mappedData.bets = data.participants
      console.log('[🎰BATTLE-WS] 字段映射: participants → bets', { 
        participantsCount: data.participants.length, 
        betsCount: mappedData.bets.length 
      })
    }
    
    // 处理users字段映射（boxroom消息）
    if (data.users && !data.bets) {
      mappedData.bets = data.users
      console.log('[🎰BATTLE-WS] 字段映射: users → bets', { 
        usersCount: data.users.length, 
        betsCount: mappedData.bets.length 
      })
    }
    
    // 确保final_results字段正确
    if (data.final_results && Array.isArray(data.final_results)) {
      console.log('[🎰BATTLE-WS] 对战结束包含final_results:', data.final_results.length, '个玩家')
    }
    
    callbacks.onBattleEnd?.(mappedData)
  }
  
  const handleTimeSyncRequest = (event: CustomEvent) => {
    const { data, socketId } = event.detail
    console.log('[🎰BATTLE-WS] 时间同步请求:', data)
    
    // 发送同步响应
    if (socketStore.socket && socketStore.isConnected) {
      socketStore.socket.emit('time_sync_response', {
        sync_id: data.sync_id,
        client_timestamp: Date.now(),
        server_request_timestamp: data.sync_request_timestamp
      })
    }
  }
  
  // ✅ 删除：不再需要的兼容性事件处理函数
  // handleLegacyRound 和 handleLegacyEnd 已合并到主处理函数中
  
  // 🎯 连接状态处理
  const handleSocketConnected = (event: CustomEvent) => {
    console.log('[🎰BATTLE-WS] Socket连接成功')
    connectionState.value.isConnected = true
    connectionState.value.isReconnecting = false
    connectionState.value.reconnectAttempts = 0
    connectionState.value.lastError = null
  }
  
  const handleSocketDisconnected = (event: CustomEvent) => {
    const { reason } = event.detail
    console.log('[🎰BATTLE-WS] Socket连接断开:', reason)
    connectionState.value.isConnected = false
    
    if (reason !== 'io client disconnect') {
      connectionState.value.isReconnecting = true
      connectionState.value.reconnectAttempts++
    }
  }
  
  // 🎯 时间同步处理
  const updateTimeSync = (serverTimestamp: number, clientTimestamp: number) => {
    const networkDelay = (Date.now() - clientTimestamp) / 2
    const clockOffset = serverTimestamp - clientTimestamp - networkDelay
    
    timeSync.value = {
      clockOffset,
      networkDelay,
      lastSyncTime: Date.now(),
      syncQuality: networkDelay < 100 ? 'good' : networkDelay < 300 ? 'poor' : 'bad'
    }
    
    console.log('[🎰BATTLE-WS] 时间同步更新:', timeSync.value)
  }
  
  // 🎯 注册回调函数
  const onRoomUpdate = (callback: (data: any) => void) => {
    callbacks.onRoomUpdate = callback
  }
  
  const onBattleStart = (callback: (data: any) => void) => {
    callbacks.onBattleStart = callback
  }
  
  const onRoundStart = (callback: (data: any) => void) => {
    callbacks.onRoundStart = callback
  }
  
  const onOpeningStart = (callback: (data: any) => void) => {
    callbacks.onOpeningStart = callback
  }
  
  const onAnimationProgress = (callback: (data: any) => void) => {
    callbacks.onAnimationProgress = callback
  }
  
  const onRoundResult = (callback: (data: any) => void) => {
    callbacks.onRoundResult = callback
  }
  
  const onBattleEnd = (callback: (data: any) => void) => {
    callbacks.onBattleEnd = callback
  }
  
  const onRoomCancel = (callback: (data: any) => void) => {
    callbacks.onRoomCancel = callback
  }
  
  // 🎯 发送WebSocket请求
  const requestBattleData = () => {
    if (socketStore.socket && socketStore.isConnected) {
      socketStore.socket.emit('request_battle_data', { battle_id: battleId })
    }
  }
  
  const requestRoundData = (round: number) => {
    if (socketStore.socket && socketStore.isConnected) {
      socketStore.socket.emit('request_round_data', { battle_id: battleId, round })
    }
  }
  
  // 🎯 加入房间专属频道，让后端推送专属消息（解决多客户端同步）
  const joinRoomChannel = (roomUid: string) => {
    if (socketStore.socket && socketStore.isConnected && roomUid) {
      console.log('[🎰BATTLE-WS] 加入房间专属频道:', roomUid)
      socketStore.socket.emit('join', roomUid)
    }
  }
  
  // 🎯 初始化和清理
  const initialize = () => {
    setupSocketListeners()
    connectionState.value.isConnected = socketStore.isConnected
  }
  
  const cleanup = () => {
    removeSocketListeners()
  }
  
  return {
    // 状态
    connectionState: readonly(connectionState),
    animationState: readonly(animationState),
    timeSync: readonly(timeSync),
    
    // 事件注册
    onRoomUpdate,
    onBattleStart,
    onRoundStart,
    onOpeningStart,
    onAnimationProgress,
    onRoundResult,
    onBattleEnd,
    onRoomCancel,
    
    // 方法
    requestBattleData,
    requestRoundData,
    joinRoomChannel,
    registerAdditionalId,
    initialize,
    cleanup,
    
    // 🎯 新增：WebSocket处理器统计信息
    getMessageStats: wsHandler.getMessageStats,
    validateStateConsistency: wsHandler.validateStateConsistency
  }
} 