# API服务命名统一总结

## 优化概述

本次优化主要针对`services`目录下API服务的命名规范进行统一，提升代码的一致性和可维护性。

## 主要优化内容

### 1. 类命名统一

**之前的命名（不统一）：**
- `BattleApiService`
- `CaseApiService` 
- `AuthApiService`
- `MonitorApiService`
- `SiteApiService`
- `SkinApiService`

**现在的命名（统一）：**
- `BattleApi`
- `CaseApi`
- `AuthApi`
- `MonitorApi`
- `SiteApi`
- `SkinApi`

### 2. 实例导出统一

**统一的导出模式：**
```typescript
// 导出单例实例
export const xxxApi = new XxxApi()

// 保持向后兼容性
export const XxxApiService = XxxApi
export { XxxApi as default }
```

**具体实例：**
- `battleApi` - 对战相关API
- `caseApi` - 箱子相关API
- `authApi` - 认证相关API
- `monitorApi` - 监控数据API
- `siteApi` - 站点配置API
- `skinApi` - 皮肤相关API
- `commonApi` - 通用API服务

### 3. 文件重命名

**重命名的文件：**
- `services/api-fixed.ts` → `services/common-api.ts`
- 删除空文件：`services/api-broken.ts`

**更新的引用：**
- `services/battle-api.ts` 中的导入路径已更新

## 命名规范标准

### 文件命名
- 格式：`xxx-api.ts`
- 示例：`battle-api.ts`、`case-api.ts`、`auth-api.ts`

### 类命名
- 格式：`XxxApi`（PascalCase）
- 示例：`BattleApi`、`CaseApi`、`AuthApi`

### 实例命名
- 格式：`xxxApi`（camelCase）
- 示例：`battleApi`、`caseApi`、`authApi`

### 向后兼容性
- 保留旧的类名别名：`export const XxxApiService = XxxApi`
- 提供默认导出：`export { XxxApi as default }`

## 使用示例

### 推荐的使用方式
```typescript
// 导入实例（推荐）
import { battleApi } from '~/services/battle-api'
import { caseApi } from '~/services/case-api'
import { authApi } from '~/services/auth-api'

// 使用
const battles = await battleApi.getBattleList()
const cases = await caseApi.getHotCases()
const user = await authApi.getCurrentUser()
```

### 向后兼容的使用方式
```typescript
// 仍然支持旧的导入方式
import { BattleApiService } from '~/services/battle-api'
import { CaseApiService } from '~/services/case-api'

// 使用
const battleService = new BattleApiService()
const caseService = new CaseApiService()
```

## 技术改进

### 1. 类型安全
- 所有API服务都有完整的TypeScript类型定义
- 统一的响应接口格式
- 严格的参数类型检查

### 2. 错误处理
- 统一的错误处理机制
- 详细的错误日志输出
- 优雅的降级处理

### 3. 代码复用
- 单例模式避免重复实例化
- 统一的请求封装方法
- 共享的类型定义

## 构建验证

✅ **构建成功** - 所有API服务命名统一后构建无错误
✅ **类型检查通过** - TypeScript类型定义正确
✅ **向后兼容** - 旧的导入方式仍然可用
✅ **代码一致性** - 所有API服务遵循统一命名规范

## 后续维护

### 新增API服务时的规范
1. 文件命名：`xxx-api.ts`
2. 类命名：`XxxApi`
3. 实例导出：`export const xxxApi = new XxxApi()`
4. 向后兼容：`export const XxxApiService = XxxApi`
5. 默认导出：`export { XxxApi as default }`

### 代码审查要点
- 检查命名是否符合统一规范
- 确保导出方式一致
- 验证类型定义完整性
- 测试向后兼容性

现在所有API服务都遵循统一的命名规范，代码更加整洁和可维护。 