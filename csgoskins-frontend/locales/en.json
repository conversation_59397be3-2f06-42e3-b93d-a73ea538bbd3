{"common": {"loading": "Loading", "processing": "Processing...", "cancel": "Cancel", "confirm": "Confirm", "error": "Error", "retry": "Retry", "error_loading": "Error loading", "view_all": "View All", "refresh": "Refresh", "live": "Live", "get_started": "Get Started", "banner_image": "Banner Image", "banner_background": "Banner Background", "hero_image": "Hero Image", "case_image": "Case Image", "hero_carousel": "<PERSON>", "hero_carousel_desc": "Discover amazing CS:GO skins and cases", "enabled": "Enabled", "disabled": "Disabled", "back_to_home": "Back to Home", "back_to_demo": "Back to Demo", "featured": "Featured", "premium_content": "Premium Content", "high_quality": "High Quality", "trusted_by": "Trusted By", "thousands_users": "Thousands of Users", "premium": "Premium", "quality_guaranteed": "Quality Guaranteed", "clear": "Clear", "close": "Close", "goBack": "Back"}, "time": {"days": "Days", "hours": "Hours", "minutes": "Minutes", "seconds": "Seconds", "oneDay": "1 Day", "oneHour": "1 Hour", "fiveMinutes": "5 Minutes", "oneMinute": "1 Minute", "thirtySeconds": "30 Seconds", "stop": "Stop", "countdown": "Countdown", "timeRemaining": "Time Remaining", "countdownEnded": "Countdown Ended"}, "carousel": {"slide_indicator": "Slide {index}", "previous_slide": "Previous slide", "next_slide": "Next slide", "demo_title": "HeroCarousel Component Demo", "demo_description": "Interactive demonstration of the enhanced HeroCarousel component with multiple modes and configurations", "api_carousel": "API Data Carousel", "custom_carousel": "Custom Configuration Carousel", "autoplay": "Autoplay", "interval": "Interval", "height": "Height", "features": "Features & Capabilities", "responsive_design": "Responsive Design", "mobile_optimized": "Mobile Optimized", "touch_gestures": "Touch Gestures Support", "keyboard_navigation": "Keyboard Navigation", "advanced_features": "Advanced Features", "i18n_support": "Internationalization Support", "seo_optimized": "SEO Optimized", "performance_optimized": "Performance Optimized", "no_banner_data": "No Banner Data Available", "load_banner_data": "Load Banner Data", "data_info": "Data Information", "banner_statistics": "Banner Statistics", "total_banners": "Total Banners", "current_language": "Current Language", "status": "Status", "api_features": "API Features", "real_time_data": "Real-time Data", "automatic_refresh": "Automatic Refresh", "multilingual_content": "Multilingual Content", "custom_mode": "Custom Mode", "custom_config_desc": "Customizable settings demonstration", "loaded": "Loaded", "loading": "Loading", "error": "Error", "no_data": "No Data"}, "nav": {"home": "Home", "cases": "Cases", "skins": "Skins", "battle": "Battle", "activity": "Activity", "profile": "Profile", "about": "About", "terms": "Terms", "privacy": "Privacy", "faq": "FAQ", "support": "Support", "contact": "Contact", "demo": "Demo", "shortcuts": "Shortcuts", "help": "Help", "main": "Main Navigation", "demo_desc": "Demo", "help_desc": "Help", "faq_desc": "View frequently asked questions", "support_desc": "Get professional technical support", "contact_desc": "Get in touch with us"}, "battle": {"no_history": "No History", "no_history_desc": "No history", "subtitle": "Battle", "loading": "Loading...", "total_spent": "Total Spent", "refresh": "Refresh", "battle": "Battle", "waiting_player": "Waiting Player", "create": "Create Battle", "create_battle": "Create Battle", "join": "Join Battle", "join_battle": "Join Battle", "waiting": "Waiting", "in_progress": "In Progress", "finished": "Finished", "round": "Round", "round_progress": "Round Progress", "current_round": "Round {round}", "opening": "Opening", "opening_now": "Opening Now", "completed": "Completed", "pending": "Pending", "case_id": "Case ID", "price": "Price", "total_value": "Total Value", "total_items": "{count} Items Opened", "cases": {"title": "Battle Cases", "current": "Current", "opening": "Opening", "completing": "Completing", "interval": "Interval", "completed": "Completed", "empty_slot": "Empty Slot", "total_cases": "Total Cases", "total_value": "Total Value"}, "opening_records": {"title": "Opening Records", "all_rounds": "All Rounds", "no_records": "No Records Yet", "no_records_desc": "Opening records will appear here after the battle starts", "just_now": "Just Now", "minutes_ago": "{count} minutes ago", "hours_ago": "{count} hours ago", "total_value": "Total Value", "rare_items": "Rare Items", "best_item": "Best Item"}, "state": {"loading": {"title": "Loading", "description": "Loading battle data", "badge": "Loading"}, "preparing": {"title": "Preparing", "description": "Battle is about to start, please wait", "badge": "Preparing"}, "calculating": {"title": "Calculating", "description": "Calculating final results", "badge": "Calculating"}, "finished": {"title": "Finished", "description": "Battle completed, view results", "badge": "Finished"}, "unknown": "Unknown State", "waiting": {"title": "Waiting", "description": "Waiting for other players to join the battle", "badge": "Waiting"}, "countdown": {"title": "Countdown", "description": "Battle is about to start", "badge": "Countdown"}, "battle": {"title": "Battle", "description": "Battle in progress", "badge": "Battle"}, "completed": {"title": "Completed", "description": "Battle completed", "badge": "Completed"}, "cancelled": {"title": "Cancelled", "description": "Battle cancelled", "badge": "Cancelled"}}, "connection": {"connected": "Connected", "reconnecting": "Reconnecting", "disconnected": "Disconnected", "status": "Connection Status", "state": "Connection State", "attempts": "Reconnect Attempts", "uptime": "Connection Uptime", "quality": {"quality": "Network Quality", "excellent": "Excellent", "good": "Good", "poor": "Poor", "reconnecting": "Reconnecting"}}, "next_step": {"loading": "Loading", "waiting": "Waiting", "preparing": "Preparing", "countdown": "Countdown", "battle": "Battle", "calculating": "Calculating", "finished": "Finished", "completed": "Completed", "cancelled": "Cancelled"}, "detail": {"join_battle": "Join Battle", "joined": "Joined", "waiting": "Waiting", "full": "Full", "finished": "Finished", "available": "Available", "battle_id": "ID", "round_progress": "Round Progress", "title": "Battle Details", "description": "View battle details and real-time progress", "keywords": "battle details,CS:GO battle,case opening battle,real-time battle", "og_title": "Battle Details - CSGOSKINS", "og_description": "View CS:GO case opening battle details and real-time progress", "time": "Time", "loading": "Loading...", "round_completed": "Round Completed!", "battle_completed": "Battle Completed!", "winner_announced": "Winner Determined", "error_title": "Error", "error_desc": "An error occurred while processing the battle", "retry": "Retry", "not_found": "Battle Not Found", "not_found_message": "The battle you're looking for doesn't exist or has been deleted", "back_to_list": "Back to Battle List", "players": "Players", "host": "Host", "winner": "Winner", "opening": "Opening", "rounds": "Rounds", "win_amount": "Win Amount", "opening_case": "Opening Case", "waiting_for_player": "Waiting for player", "waiting_for_players": "Waiting for players", "need_players": "Need {current}/{max} players to start battle", "players_ready": "Players Ready", "tip_players": "Waiting for other players to join", "tip_cases": "Each player selects cases to open", "tip_winner": "Player with highest total value wins", "join_to_participate": "Click to join and participate", "opening_results": "Opening Results", "win_items": "Win Items", "round": "Round", "join": "Join", "leave": "Leave", "dismiss": "<PERSON><PERSON><PERSON>", "copy_id": "Copy ID", "share": "Share", "total_value": "Total Value", "items_won": "Items Won", "battle_in_progress": "Battle in Progress", "status": {"title": "Status", "waiting": "Waiting", "full": "Full", "joined": "Joined", "finished": "Finished", "cancelled": "Cancelled", "in_progress": "In Progress", "available": "Available"}, "debug": {"title": "Debug Information", "animation_state": "Animation State", "time_sync": "Time Sync", "test_animation": "Test Animation"}}, "calculation": {"title": "Calculation Progress", "title_complete": "Calculation Complete", "completion_message": "Calculation complete! Determining results...", "steps_title": "Calculation Process", "subtitle": "Calculating all players' opening results...", "progress": "Calculation Progress", "total_value": "Total Value", "total_items": "{count} Items Opened", "winner_announcement": "🏆 Winner", "winner_value": "Total Value: ${value}", "close": "Close", "continue": "Continue"}, "animation": {"title": "Case Opening Animation", "subtitle": "Ready for the surprise?", "open_case": "Open Case", "opening": "Opening", "opening_desc": "Opening case...", "loading": "Loading...", "button_hint": "Click the button to start your exciting unboxing experience", "cost_hint": "Opening cost: ${price}", "result_title": "Congratulations on your new item!", "result_subtitle": "You've unboxed a beautiful item", "hint": "Ready? Click the button to start opening!", "loading_data": "Loading data...", "no_case_selected": "Please select a case", "checking_user_status": "Checking user status...", "checking_balance": "Checking user balance...", "checking_case_status": "Checking case status...", "preparing_animation": "Preparing to open: shuffling, loading animation...", "ready_go": "Ready, GO! 🚀", "congratulations": "🎉 Congratulations on your new item!", "from_case": "From {caseName}", "sell": "<PERSON>ll", "keep": "Keep", "open_again": "Open Again", "sold_success": "<PERSON><PERSON>", "sold_message": "Item has been successfully sold", "kept_success": "<PERSON><PERSON>", "kept_message": "Item has been added to your inventory", "open_success": "Opening Success!", "open_fail": "Opening Failed", "please_wait": "Please wait...", "generating_items": "Generating animation items...", "force_generate": "Force Generate Items", "resetting": "Resetting...", "preparing_new_round": "Preparing for a new round...", "rarity": {"consumer": "Consumer Grade", "industrial": "Industrial Grade", "mil_spec": "Mil-Spec Grade", "restricted": "Restricted", "classified": "Classified", "covert": "<PERSON><PERSON>", "contraband": "Contraband", "extraordinary": "Extraordinary"}, "opening_cases": "Opening Cases...", "opening_items": "Opening Items", "skip": "Skip Animation", "preparing": "Preparing", "preparing_desc": "Preparing opening animation...", "revealing": "Revealing", "revealing_desc": "Revealing result...", "your_turn": "Round {round} - Your Turn", "waiting_turn": "Waiting for other players"}, "spectate": "Spectate", "prev_page": "Previous Page", "next_page": "Next Page", "selected_cases_count": "Selected Cases Count", "x_players": "{count} Players", "ongoing_battles": "Current Battles", "user_joined_notification": "Has joined the battle", "my_battles_desc": "View and manage your battles", "total_my_battles_count": "Total My Battles Count", "total_history_count": "Total History Battles Count", "history_battles_desc": "View your battle history", "participated_by_me": "Participated by Me", "created_by_me": "Created by <PERSON>", "all_battles": "All Battles", "view_result": "View Result", "participants": "Participants", "view_details": "View Detail", "active_battles": "Active Battles", "my_battles": "My Battles", "history_battles": "History Battles", "title": "Battle System", "players_joined": "Players Joined", "cases_selected": "Cases Selected", "battle_info": "Battle Info", "battle_id": "Battle ID", "created_at": "Created At", "winner_gets_all": "Winner gets all items", "loser_gets_gift": "Loser gets system gift", "all_items_sorted_by_price": "All items sorted by price", "system_gift_item": "System gift item", "system_gift": "System Gift", "opening_history": "Opening History", "winning_items": "Winning Items", "from_other_players": "From Other Players", "gift_items": "System Gift", "all_winner_items": "All Winner Items", "original_owner": "Original Owner", "gift_type": "Gift Type", "no_opening_records": "No opening records", "showing": "Showing", "records": "records", "add_test_record": "Add Test Record", "time_unknown": "Unknown Time", "time_just_now": "Just now", "time_minutes_ago": "{minutes} minutes ago", "time_hours_ago": "{hours} hours ago", "time_days_ago": "{days} days ago", "opening_result_title": "Opening Result", "opening_result_description": "Congratulations on your new item!", "view_in_inventory": "View in Inventory", "status": {"ended": "Ended", "joinable": "Joinable", "waiting": "Waiting", "starting": "Starting", "battle": "Battling", "battling": "Battling", "calculating": "Calculating", "finished": "Finished", "ongoing": "Ongoing", "winner": "Winner", "opening": "Opening", "ready": "Ready", "batting": "Batting", "in_progress": "In Progress", "cancelled": "Cancelled", "unknown": "Unknown"}, "modal": {"go_to_login": "Go to Login", "cancel": "Cancel", "confirm": "Confirm", "view_details": "View Details", "back_to_list": "Back to List", "retry": "Retry", "joining_title": "Joining...", "joining_desc": "You have successfully joined the battle", "joining_loading": "Joining...", "join_success_title": "Join Battle Success", "join_success_desc": "You have successfully joined the battle", "login_required_title": "<PERSON><PERSON> Required", "login_required_desc": "Please login to join the battle", "join_failed_title": "Join Battle Failed", "join_failed_desc": "You have successfully joined the battle", "room_id": "Room ID", "room_price": "Room Price", "join_success_message": "You have successfully joined the battle room"}, "players_progress": "Players Progress", "join_to_start": "Join to Start", "click_to_join": "Click to Join <PERSON>", "room_full": "Room Full", "battle_finished": "Battle Finished", "battle_running": "Battle Running", "battle_waiting": "Waiting to Start", "battle_calculating": "Calculating", "battle_ready": "Ready", "battle_full": "Room Full", "battle_cancelled": "Cancelled", "battle_ended": "Ended", "battle_created": "Created", "battle_joined": "Joined", "battle_left": "Left", "battle_quit": "Quit", "battle_started": "Started", "battle_completed": "Completed", "battle_failed": "Failed", "battle_success": "Success", "battle_error": "Error", "battle_loading": "Loading", "battle_saving": "Saving", "battle_deleting": "Deleting", "battle_updating": "Updating", "battle_creating": "Creating", "battle_joining": "Joining", "battle_leaving": "Leaving", "battle_quitting": "Quitting", "battle_starting": "Starting", "battle_completing": "Completing", "battle_failing": "Failing", "battle_succeeding": "Succeeding", "battle_erroring": "Erroring", "result": {"title": "Battle Result", "winner_declaration": "🏆 Congratulations!", "unknown_winner": "Unknown Winner", "unknown_player": "Unknown Player", "unknown_item": "Unknown Item", "no_items": "No Items", "summary": "Battle Summary", "players": "Players", "rounds": "Rounds", "total_value": "Total Value", "duration": "Duration", "ranking": "Player Ranking", "share": "Share Result", "share_title": "CSGO Battle Result", "share_text": "{winner} won the battle with total value ${value}!", "new_battle": "New Battle", "back_to_list": "Back to List"}, "cancel_battle": "Cancel Battle", "leave_battle": "Leave Battle", "dissolve_battle": "Dissolve Battle", "confirm": {"join": {"title": "Confirm Join Battle", "subtitle": "Are you sure you want to join this battle? You'll need to participate in the complete battle process.", "confirm": "Confirm Join", "loading": "Joining battle..."}, "leave": {"title": "Confirm Leave Battle", "subtitle": "Are you sure you want to leave this battle? The room will remain active for other players to continue.", "confirm": "Confirm Leave", "loading": "Leaving battle..."}, "dismiss": {"title": "Confirm Dismiss Room", "subtitle": "Are you sure you want to dismiss this room? All participants will be removed and the room will no longer be available.", "confirm": "Confirm <PERSON>", "loading": "Dismissing room..."}}, "rounds_won": "Rounds Won", "win_items": "Win Items"}, "auth": {"login": {"title": "<PERSON><PERSON>", "subtitle": "Don't have an account?", "link": "Login now", "description": "Login to your CSGOSKINS account and start your CS:GO skin unboxing journey", "keywords": "CSGOSKINS login,CS:GO unboxing,user login,skin trading"}, "register": {"title": "Register", "subtitle": "Already have an account?", "link": "Register now", "description": "Register for a CSGOSKINS account and start your CS:GO skin unboxing journey immediately", "keywords": "CSGOSKINS register,CS:GO unboxing,new user registration,skin trading"}, "forgotPassword": {"title": "Forgot Password", "subtitle": "Remember your password?", "link": "Forgot password", "description": "Reset your CSGOSKINS account password and quickly regain access to your account", "keywords": "CSGOSKINS reset password,forgot password,account recovery,password recovery"}, "login_or_register": "<PERSON><PERSON>", "logout": "Logout", "logout_desc": "Safely logout from current account", "email": "Email", "password": "Password", "confirm_password": "Confirm Password", "forgot_password": "Forgot Password", "reset_password": "Reset Password", "change_password": "Change Password", "new_password": "New Password", "old_password": "Current Password", "username": "Username", "nickname": "Nickname", "remember_me": "Remember me", "login_with": "Login with {provider}", "register_with": "Register with {provider}", "no_account": "No account", "register_now": "Register now", "email_placeholder": "Enter your email", "password_placeholder": "Enter your password", "confirm_password_placeholder": "Confirm your password", "new_password_placeholder": "Enter new password", "old_password_placeholder": "Enter current password", "nickname_placeholder": "Enter nickname (optional)", "or_continue_with": "Or continue with", "login_here": "Login here", "send_code": "Send Code", "verification_code": "Verification Code", "email_verification_code": "Email Verification Code", "email_verification_code_placeholder": "Enter email verification code", "captcha": "<PERSON><PERSON>", "captcha_placeholder": "Enter cap<PERSON>a", "captcha_required": "Captcha is required", "terms": "Terms of Service", "privacy": "Privacy Policy", "agree_to": "Agree to", "and": "and", "already_have_account": "Already have an account", "verify_email": "<PERSON><PERSON><PERSON>", "set_new_password": "Set New Password", "complete": "Complete", "verify_proceed": "Verify and Continue", "back_to_login": "Back to Login", "back_to_register": "Back to Register", "email_required": "Email is required", "password_required": "Password is required", "confirm_password_required": "Confirm password is required", "code_required": "Verification code is required", "terms_required": "Please agree to the terms and privacy policy", "loading": "Loading...", "logging_in": "Logging in...", "registering": "Registering...", "resetting": "Resetting...", "sending": "Sending...", "changing": "Changing...", "login_success": "Login successful", "register_success": "Registration successful", "reset_success": "Reset successful", "change_success": "Change successful", "code_sent": "Verification code sent", "login_failed": "<PERSON><PERSON> failed, please try again", "register_failed": "Registration failed, please try again", "reset_failed": "Reset failed, please try again", "change_failed": "Change failed, please try again", "login_error": "<PERSON><PERSON> failed, please try again", "register_error": "Registration failed, please try again", "reset_error": "Reset failed, please try again", "change_error": "Change failed, please try again", "email_invalid": "Invalid email format", "password_too_short": "Password must be at least 8 characters", "password_weak": "Password strength: Weak", "password_medium": "Password strength: Medium", "password_strong": "Password strength: Strong", "password_mismatch": "Passwords do not match", "password_requirements": "Password requirements met", "code_countdown": "Resend in {seconds}s", "code_resend": "Resend", "refresh_captcha": "Refresh cap<PERSON>a", "captcha_error": "<PERSON><PERSON> error", "code_send_failed": "Failed to send verification code", "code_send_error": "Error sending verification code", "google_login_coming_soon": "Google login coming soon", "captcha_dialog_tip": "Please enter the captcha", "captcha_invalid": "Invalid cap<PERSON>a", "captcha_dialog_title": "Security Verification", "captcha_dialog_subtitle": "Please enter the captcha code", "captcha_refresh_hint": "Click to refresh", "processing": "Processing...", "verifying": "Verifying...", "reset_success_message": "Your password has been successfully reset. You can now login with your new password.", "code_invalid": "Invalid verification code format"}, "breadcrumb": {"account": "Account", "settings": "Settings", "inventory": "Inventory", "transactions": "Transactions", "history": "History", "about": "About Us", "terms": "Terms of Service", "privacy": "Privacy Policy", "faq": "FAQ", "support": "Support", "contact": "Contact Us", "help": "Help Center", "demo": "Demo", "caseDetail": "Case #{id}", "battleDetail": "Battle #{id}", "userProfile": "User Profile"}, "home": {"live_openings": "Live Openings", "no_records": "No opening records yet", "opened_from": "opened from", "mystery_case": "Mystery Case", "mystery_item": "Mystery Item"}, "live_openings": {"title": "Live Openings", "refresh": "Refresh", "loading": "Loading live openings...", "connection_failed": "Connection Failed", "network_error": "Unable to connect to server, please check your network connection", "retry_connection": "Retry Connection", "no_data": "No Data Available", "waiting_for_openings": "Waiting for opening records", "opened": "opened", "waiting": "Waiting", "waiting_for_new": "Waiting for new opening records", "case_name": "Case", "item_condition": "Condition", "click_for_details": "Click for details", "mystery_case": "Mystery Case", "mystery_item": "Unknown Item", "unknown_time": "Unknown", "just_now": "Just now", "seconds_ago": "{n} seconds ago", "minutes_ago": "{n} minutes ago", "hours_ago": "{n} hours ago", "paused": "Paused", "view_details": "View Details", "placeholder": "Placeholder", "unknown_item": "Unknown Item", "tooltip": {"case_info": "Case Info", "item_info": "Item Info", "opened_time": "Opened Time", "user_info": "User Info", "unknown_case": "Unknown Case", "unknown_skin": "Unknown Skin", "unknown_exterior": "Unknown Exterior", "unknown_user": "Anonymous User", "case_image_error": "Case image failed to load", "item_image_error": "Item image failed to load", "user_image_error": "User avatar failed to load"}}, "cases": {"title": "Case Center", "subtitle": "Discover the latest and hottest CS:GO weapon cases", "search_and_filter": "Search & Filter", "loading": "Loading...", "discount_cases": "Discount Cases", "discount_subtitle": "Limited Time Offers, Great Value", "hot_cases": "Hot Cases", "hot_subtitle": "Popular Picks, Trending Now", "new_cases": "New Cases", "new_subtitle": "Fresh Arrivals, Early Access", "open": "Open", "no_cases_available": "No cases available", "new": "NEW", "hot": "HOT", "discount": "DISCOUNT", "all_cases": "All Cases", "filter_by_tag": "Filter by Tag", "filter_by_tags": "Filter by Tags", "clear_filters": "Clear Filters", "search_placeholder": "Search cases...", "load_more": "Load More", "refresh": "Refresh", "price": "Price", "opens": "Opens", "view_detail": "View Details", "sort_by_popular": "Sort by Popular", "sort_by_newest": "Sort by Newest", "sort_by_price_low": "Price: Low to High", "sort_by_price_high": "Price: High to Low", "error_text": "Loading Failed", "retry_text": "Retry", "no_data_text": "No Data Available", "contact_text": "Please contact support for more information", "no_cases_text": "No cases in this category", "collapse": "Collapse", "show_all": "Show All", "quick_open": "Quick Open", "loading_animation": "Animation loading...", "load_error_title": "Failed to Load Case Details", "load_error_description": "Unable to fetch case information", "back_to_list": "Back to Case List", "no_recent_openings_title": "No Recent Openings", "no_recent_openings_description": "No one has opened this case yet", "be_first_to_open": "Be the first to open it", "no_items_title": "No Items Available", "no_items_description": "Unable to fetch case items information", "reload_items": "Reload Items", "case_detail_title": "Case Details - {name}", "case_detail_description": "View {name} details, including all available items and opening records", "open_case": "Open Case", "recent_openings": "Recent Openings", "available_items": "Available Items", "live_updates": "Live Updates", "times_opened": "times opened", "case_description": "This is a {name} containing various exquisite skins, each opening has a chance to get rare items", "open_now": "Open Now", "anonymous_user": "Anonymous User", "time_unknown": "Unknown Time", "time_just_now": "Just now", "time_minutes_ago": "{minutes} minutes ago", "time_hours_ago": "{hours} hours ago", "time_days_ago": "{days} days ago", "opening_result_title": "Opening Result", "opening_result_description": "Congratulations on your new item!", "view_in_inventory": "View in Inventory", "animation": {"title": "Case Opening Animation", "subtitle": "Ready for the surprise?", "open_case": "Open Case", "opening": "Opening", "opening_desc": "Opening case...", "loading": "Loading...", "button_hint": "Click the button to start your exciting unboxing experience", "cost_hint": "Opening cost: ${price}", "result_title": "Congratulations on your new item!", "result_subtitle": "You've unboxed a beautiful item", "hint": "Ready? Click the button to start opening!", "loading_data": "Loading data...", "no_case_selected": "Please select a case", "checking_user_status": "Checking user status...", "checking_balance": "Checking user balance...", "checking_case_status": "Checking case status...", "preparing_animation": "Preparing to open: shuffling, loading animation...", "ready_go": "Ready, GO! 🚀", "congratulations": "🎉 Congratulations on your new item!", "from_case": "From {caseName}", "sell": "<PERSON>ll", "keep": "Keep", "open_again": "Open Again", "sold_success": "<PERSON><PERSON>", "sold_message": "Item has been successfully sold", "kept_success": "<PERSON><PERSON>", "kept_message": "Item has been added to your inventory", "open_success": "Opening Success!", "open_fail": "Opening Failed", "please_wait": "Please wait...", "generating_items": "Generating animation items...", "force_generate": "Force Generate Items", "resetting": "Resetting...", "preparing_new_round": "Preparing for a new round...", "rarity": {"consumer": "Consumer Grade", "industrial": "Industrial Grade", "mil_spec": "Mil-Spec Grade", "restricted": "Restricted", "classified": "Classified", "covert": "<PERSON><PERSON>", "contraband": "Contraband", "extraordinary": "Extraordinary"}}}, "skins": {"title": "Skin Showcase", "subtitle": "Explore the finest CS:GO skin collection", "search_placeholder": "Search skin names...", "filter_type": "Type", "filter_rarity": "<PERSON><PERSON>", "filter_quality": "Quality", "filter_exterior": "Exterior", "filter_price": "Price", "reset_filters": "Reset Filters", "filters_applied": "{count} filters applied", "showing_results": "Showing {count} results of {total}", "load_error": "Load Failed", "retry": "Retry", "no_results": "No matching skins found", "adjust_filters": "Try adjusting your filters", "end_of_list": "End of results", "stattrak_yes": "Yes", "stattrak_no": "No", "available_cases": "Available Cases", "similar": "Similar Skins", "details": "Details", "category": "Category", "wear": "Wear", "exterior": "Exterior", "update_time": "Update Time", "wear_range": "Wear Range", "factory_new": "Factory New", "minimal_wear": "<PERSON><PERSON>", "field_tested": "Field-Tested", "well_worn": "Well-Worn", "battle_scarred": "Battle-<PERSON><PERSON>red", "description": "Description", "market_price": "Market Price", "featured_skins": "Featured Skins", "random_skins": "Random Skins", "view": "View", "csgoskins": "CSGO Skins", "random_subtitle": "Curated Collection, Random Picks", "unknown": "Unknown Item", "image_error": "Image not available", "all": "All", "price": "Price", "rarity": "<PERSON><PERSON>", "weapon": "Weapon", "skin": "Skin", "condition": "Condition", "stattrak": "StatTrak™", "quality": "Quality"}, "caseRecords": {"title": "Latest Case Openings", "user": "User", "caseName": "Case", "skinName": "<PERSON><PERSON>", "quality": "Quality", "time": "Time", "just_now": "Just now", "minutes_ago": "minutes ago", "hours_ago": "hours ago", "days_ago": "days ago", "no_records": "No opening records yet"}, "stats": {"users": "Total Users", "cases": "Cases Opened", "battles": "Battles", "online": "Online Now", "total_users": "Total Users", "cases_opened": "Cases Opened", "battles_played": "Battles Played", "online_users": "Online Users"}, "ui": {"offlineMode": {"title": "Connection Lost", "message": "Unable to connect to server, trying to reconnect...", "reconnecting": "Reconnecting", "attempt": "Attempt", "reconnect": "Reconnect Now"}, "mobileNav": {"home": "Home", "cases": "Cases", "battle": "Battle", "profile": "Profile", "activity": "Activity"}, "captcha_dialog": {"title": "Captcha Verification", "subtitle": "Please enter the verification code to continue", "placeholder": "Enter verification code", "confirm": "Confirm", "cancel": "Cancel", "error": "Invalid verification code", "processing": "Processing..."}}, "footer": {"description": "Professional CS:GO skin case opening simulator, providing the most realistic unboxing experience", "services": "Services", "help": "Help & Support", "legal": "Legal", "cases": "Case Opening", "skins": "Skins Gallery", "battle": "Battle Mode", "activity": "Activity Center", "market": "Trading Market", "faq": "FAQ", "support": "Customer Support", "contact": "Contact Us", "tutorial": "Beginner's Guide", "about": "About Us", "terms": "Terms of Service", "privacy": "Privacy Policy", "disclaimer": "Disclaimer", "newsletter": "Newsletter", "newsletter_desc": "Subscribe to our newsletter for latest activities and updates", "email_placeholder": "Enter your email address", "subscribe": "Subscribe", "subscribing": "Subscribing...", "rights": "All rights reserved", "version": "Version", "build": "Build time", "follow_us": "Follow Us", "users": "Users", "cases_opened": "Cases Opened", "secure": "Secure"}, "notification": {"welcome": "Welcome to CSGO Case Simulator, experience the most realistic unboxing fun", "activity": "New Event: Limited-time Case Opening Championship is in full swing", "maintenance": "System Maintenance: Daily maintenance from 2:00-3:00 AM", "achievement": "Congratulations to player <PERSON><PERSON><PERSON> for unboxing legendary AK47 Redline skin"}, "demo": {"title": "Demo Center", "description": "One-stop demo center showcasing all functional components and test pages", "main_features": "Main Features", "i18n_tests": "Internationalization Tests", "system_tests": "System Function Tests", "layout_tests": "Layout Tests", "debug_tools": "Debug Tools", "quick_preview": "Quick Preview", "quick_tools": "Quick Tools", "component_library": "Component Library", "component_library_desc": "Complete UI component library demo", "loading_components": "Loading Components", "loading_components_desc": "Various loading states and skeleton screens", "live_openings": "Live Openings", "live_openings_desc": "Real-time opening records display", "socket_test": "Socket Test", "socket_test_desc": "WebSocket connection testing", "hero_carousel": "<PERSON>", "hero_carousel_desc": "Complete carousel component demo", "home_components_desc": "Home page components showcase", "language_persistence": "Language Persistence Test", "language_persistence_desc": "Test language switching and persistence", "skin_language": "Skin Language Test", "skin_language_desc": "Skin name multilingual display", "case_language": "Case Language Test", "case_language_desc": "Case name multilingual display", "card_i18n": "Card I18n Test", "card_i18n_desc": "Opening result card multilingual", "heartbeat_test": "Heartbeat Test", "heartbeat_test_desc": "WebSocket heartbeat mechanism test", "api_test": "API Test", "api_test_desc": "API interface function testing", "route_test": "Route Test", "route_test_desc": "Route navigation function test", "date_format": "Date Format Test", "date_format_desc": "Date time formatting function", "font_test": "Font Test", "font_test_desc": "Font display effect testing", "framework_test": "Framework Test", "framework_test_desc": "Home page framework structure", "layout_test": "Layout Test", "layout_test_desc": "Page layout component testing", "style_showcase": "Style Showcase", "style_showcase_desc": "Live openings style effects", "banner_debug": "Banner Debug", "banner_debug_desc": "Banner component debug tool", "store_debug": "Store Debug", "store_debug_desc": "State management debug tool", "translation_debug": "Translation Debug", "translation_debug_desc": "Multilingual translation debugging", "error_debug": "<PERSON><PERSON><PERSON>", "error_debug_desc": "Component error debugging tool", "refresh_data": "Refresh Data", "toggle_language": "Toggle Language", "simulate_offline": "Simulate Offline", "back_to_home": "Back to Home", "real_time_stats": "Real-time Stats", "latest_records": "Latest Records", "components": {"title": "Vue3 Components Demo", "socket": "Socket Components", "data": "Data Display Components", "navigation": "Navigation Components", "i18n": "Internationalization Test"}, "home_components": {"title": "Home Components Demo", "description": "Demonstration of home page components including stats, case cards, and skin cards", "stats_component": "Statistics Component", "case_card_component": "Case Card Component", "skin_card_component": "Skin Card Component", "features": "Features & Capabilities", "real_time_stats": "Real-time Statistics", "real_time_stats_desc": "Live updating statistics with socket connection and smooth animations", "responsive_design": "Responsive Design", "responsive_design_desc": "Optimized for all screen sizes with mobile-first approach", "i18n_support": "Internationalization", "i18n_support_desc": "Full support for multiple languages with dynamic content localization"}, "live_openings_i18n": {"title": "LiveOpenings Component I18n Test", "description": "Test the internationalization features of the LiveOpenings component", "current_status": "Current Status", "current_language": "Current Language", "last_updated": "Last Updated", "component_test": "Component Test", "test_items": "Test Items", "test_title": "Component Title Translation", "test_loading": "Loading State Messages", "test_error_messages": "Error Messages", "test_time_format": "Time Format Localization", "test_tooltips": "Tooltip Localization", "time_format_examples": "Time Format Examples", "just_now": "Just Now", "seconds_ago": "30 Seconds Ago", "minutes_ago": "5 Minutes Ago", "hours_ago": "2 Hours Ago", "back_to_language_test": "Back to Language Test", "page_title": "LiveOpenings I18n Test", "page_description": "Test page for LiveOpenings component internationalization"}, "opened_skin_card_i18n": {"title": "OpenedSkinCardSimple Component I18n Test", "description": "Test the internationalization features of the OpenedSkinCardSimple component", "current_status": "Current Status", "current_language": "Current Language", "test_cards_count": "Test Cards", "test_instructions": "Test Instructions", "hover_instruction": "Hover over cards to see tooltip translation", "language_switch_instruction": "Switch language to see text changes", "fallback_instruction": "Check fallback behavior for missing data", "test_case": "Test Case", "test_items": "Test Items", "test_mystery_item": "Mystery Item Translation", "test_tooltip_text": "Tooltip Text Translation", "test_localized_names": "Localized Names Display", "test_language_switching": "Language Switching Behavior", "translation_examples": "Translation Examples", "mystery_item_label": "Mystery Item", "click_details_label": "<PERSON>lick Details", "current_locale_label": "Current Locale", "back_to_live_openings": "Back to LiveOpenings Test", "back_to_language_test": "Back to Language Test"}, "battle_separated": {"title": "Battle Component Separation Demo", "description": "Demonstrates BattleAnimation component focusing on animation effects, BattlePlayerList component responsible for player information and opening records"}, "battle_real": {"title": "Real Battle Demo", "description": "Complete multi-round battle system demo with player management, opening animations, result calculations and more", "battle_info": "Battle Info", "battle_id": "Battle ID", "created_at": "Created At", "round_progress": "Round Progress", "total_value": "Total Value", "case_display": "Case Display", "case": "Case", "cases": "Cases", "players": "Players", "player": "Player", "opening_records": "Opening Records", "round": "Round", "rewards": "Rewards", "battle_animation": "Battle Animation", "live": "Live", "calculating_results": "Calculating Results", "please_wait": "Please wait...", "start_battle": "Start Battle", "reset": "Reset Demo", "animation_on": "Animation On", "animation_off": "Animation Off", "debug_info": "Debug Info", "current_state": "Current State", "current_round": "Current Round", "animation_enabled": "Animation Enabled", "calculation_progress": "Calculation Progress", "calculation_process": "Calculation Process", "total_openings": "Total Openings", "average_value": "Average Value", "rounds": "Rounds", "step_analyzing": "Analyzing opening data...", "step_calculating": "Calculating total values...", "step_comparing": "Comparing player scores...", "step_determining": "Determining winner..."}, "battle_vertical": {"title": "Vertical Battle Demo", "battle_id": "Battle ID", "current_round": "Current Round", "players": "Players", "total_value": "Total Value", "current_case": "Current Case", "round": "Round", "waiting": "Waiting...", "round_result": "Round Result", "opening_history": "Opening History", "start_battle": "Start Battle", "restart": "<PERSON><PERSON>", "animation_on": "Animation On", "animation_off": "Animation Off"}}, "header": {"no_notifications": "No notifications"}, "user": {"menu": "User <PERSON>u", "profile": "Profile", "profile_desc": "Manage personal information and settings", "inventory": "Inventory", "inventory_desc": "View my skin collection", "history": "History", "history_desc": "View opening and trading records", "recharge": "Recharge", "balance": "Balance"}}