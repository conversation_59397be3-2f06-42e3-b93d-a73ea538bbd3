const API_TARGET = process.env.NUXT_PUBLIC_API_TARGET || 'https://api.cs2.net.cn'
const SOCKET_TARGET = process.env.NUXT_PUBLIC_SOCKET_TARGET || 'https://socket.cs2.net.cn'

export default defineNuxtConfig({
  compatibilityDate: '2025-06-03',
  ssr: false,
  css: ['animate.css', '@/assets/css/main.scss'],
  app: {
    head: {
      link: [
        { rel: 'stylesheet', href: 'https://at.alicdn.com/t/c/font_3193044_q8xqmhjvze.css' },
        { rel: 'stylesheet', href: 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css' }
      ]
    }
  },
  modules: ['@pinia/nuxt', '@vueuse/nuxt', '@nuxtjs/i18n', '@nuxtjs/tailwindcss', '@nuxt/image', '@nuxt/icon'],
  plugins: [
    { src: '~/plugins/i18n.client.ts', mode: 'client' as const },
    { src: '~/plugins/app-init.client.ts', mode: 'client' as const },
    { src: '~/plugins/socket.client.ts', mode: 'client' as const },
    { src: '~/plugins/gsap.client.ts', mode: 'client' as const }
  ],
  i18n: {
    strategy: 'no_prefix',
    defaultLocale: 'zh-hans',
    lazy: true,
    langDir: 'locales',
    compilation: {
      strictMessage: false,
      escapeHtml: false
    },
    bundle: {
      optimizeTranslationDirective: false
    },
    locales: [
      {
        code: 'en',
        file: 'en.json',
        name: 'English',
        iso: 'en-US'
      },
      {
        code: 'zh-hans',
        file: 'zh-hans.json',
        name: '简体中文',
        iso: 'zh-CN'
      }
    ],
    detectBrowserLanguage: {
      useCookie: true,
      cookieKey: 'i18n_redirected',
      redirectOn: 'root',
      alwaysRedirect: false,
      fallbackLocale: 'zh-hans'
    },
    vueI18n: './i18n.config.ts',
    experimental: {
      generatedLocaleFilePathFormat: 'relative'
    },
    restructureDir: false
  },
  postcss: {
    plugins: {
      tailwindcss: {},
      autoprefixer: {}
    }
  },
  typescript: {
    strict: true
  },
  build: {
    transpile: []
  },
  runtimeConfig: {
    public: {
      apiBase: '/api',
      apiTarget: API_TARGET,
      socketUrl: process.env.NUXT_PUBLIC_SOCKET_URL || SOCKET_TARGET,
      siteUrl: process.env.NUXT_PUBLIC_SITE_URL || 'https://csgoskins.hk'
    }
  },
  // 路由规则 - 生产环境使用API代理，开发环境可以通过环境变量控制
  routeRules: process.env.DISABLE_API_PROXY ? {} : {
    '/api/**': {
      proxy: { to: API_TARGET + '/api/**' }
    },
    '/socket.io/**': {
      proxy: { to: SOCKET_TARGET + '/socket.io/**' }
    }
  },
  // 开发服务器配置
  devServer: {
    port: 3000,
    host: '0.0.0.0'
    },
  // Vite配置 - 生产环境使用代理，开发环境可以通过环境变量控制
  vite: {
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: '@use "@/assets/css/theme-variables.scss" as *;'
        }
      }
    },
    resolve: {
      alias: {
        dayjs: '~/utils/date.ts'
      }
    },
    optimizeDeps: {
      include: ['socket.io-client']
    },
    define: {
      global: 'globalThis'
    },
    server: process.env.DISABLE_API_PROXY ? {} : {
      proxy: {
        '/api': {
          target: API_TARGET,
          changeOrigin: true,
          secure: false,
          ws: false
        },
        '/socket.io': {
          target: SOCKET_TARGET,
          ws: true,
          changeOrigin: true,
        }
      }
    }
  },
  // Nitro配置 - 生产环境使用代理，开发环境可以通过环境变量控制
  nitro: process.env.DISABLE_API_PROXY ? {} : {
    devProxy: {
      '/api': {
        target: API_TARGET,
        changeOrigin: true,
        cookieDomainRewrite: { '*': '' },
        headers: {
          'X-Forwarded-Host': 'localhost',
          'X-Forwarded-Proto': 'http'
        }
      },
      '/socket.io': {
        target: SOCKET_TARGET,
        ws: true,
        changeOrigin: true,
        headers: {
          'X-Forwarded-Host': 'socket.cs2.net.cn',
          'X-Forwarded-Proto': 'https'
        }
      }
    }
  }
})