<template>
  <div class="case-battle-container bg-zinc-800/50 rounded-xl overflow-hidden">
    <!-- 对战头部信息 -->
    <div class="battle-header bg-gradient-to-r from-zinc-900 to-zinc-800 p-4 md:p-6">
      <div class="flex flex-col md:flex-row justify-between items-center gap-4">
        <div>
          <h3 class="text-xl md:text-2xl font-bold mb-1">{{ battleData.title }}</h3>
          <p class="text-white/60 text-sm">
            {{ battleData.players.length }}人对战 · {{ formatDate(battleData.created) }}
          </p>
        </div>
        
        <div class="flex items-center gap-3">
          <!-- 总价值 -->
          <div class="bg-zinc-800/80 py-1.5 px-3 rounded-lg flex items-center gap-2">
            <span class="text-white/60 text-sm">总价值:</span>
            <div class="flex items-center">
              <img src="/images/dollar.svg" alt="price" class="w-4 h-4 mr-1">
              <span class="text-emerald-400 font-bold">{{ getTotalPrice().toFixed(2) }}</span>
            </div>
          </div>
          
          <!-- 箱子数量 -->
          <div class="bg-zinc-800/80 py-1.5 px-3 rounded-lg flex items-center gap-2">
            <span class="text-white/60 text-sm">箱子数:</span>
            <span class="text-white font-bold">{{ battleData.totalCases }}个</span>
          </div>
          
          <!-- 状态标签 -->
          <div 
            class="py-1 px-3 rounded-full text-sm font-medium"
            :class="getBattleStatusClass()"
          >
            {{ getBattleStatusText() }}
          </div>
        </div>
      </div>
    </div>
    
    <!-- 玩家对战区域 -->
    <div class="battle-content p-4 md:p-6">
      <!-- 玩家列表 -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        <div 
          v-for="player in battleData.players" 
          :key="player.id"
          class="player-card"
          :class="{'winner': player.id === battleData.winnerId}"
        >
          <!-- 玩家信息 -->
          <div class="player-info flex items-center gap-3 mb-4">
            <div class="relative">
              <img 
                :src="player.avatar" 
                :alt="player.username" 
                class="w-12 h-12 rounded-full object-cover border-2"
                :class="player.id === battleData.winnerId ? 'border-yellow-400' : 'border-zinc-700'"
              >
              <div 
                v-if="player.id === battleData.winnerId"
                class="absolute -top-2 -right-2 bg-yellow-400 text-black w-6 h-6 rounded-full flex items-center justify-center"
              >
                <i class="i-ph-crown text-sm"></i>
              </div>
            </div>
            
            <div>
              <div class="font-bold">{{ player.username }}</div>
              <div class="text-sm text-white/60">
                总价值: {{ getPlayerTotal(player).toFixed(2) }}
              </div>
            </div>
          </div>
          
          <!-- 玩家开出的物品 -->
          <div class="grid grid-cols-2 gap-2">
            <div 
              v-for="(item, index) in player.items" 
              :key="`${player.id}-${index}`"
              class="item-card bg-zinc-800/80 rounded-lg p-2 relative overflow-hidden"
              :class="{'pulse-animation': item.isHighlighted}"
            >
              <!-- 稀有度指示器 -->
              <div 
                class="absolute top-0 left-0 right-0 h-1"
                :class="getRarityBgClass(item.rarity)"
              ></div>
              
              <!-- 物品图片 -->
              <div class="relative pt-[80%] mb-2">
                <img 
                  :src="item.image" 
                  :alt="`${item.weapon} | ${item.skin}`"
                  class="absolute inset-0 w-full h-full object-contain p-2"
                >
              </div>
              
              <!-- 物品信息 -->
              <div class="text-center">
                <div class="truncate text-xs font-medium">{{ item.weapon }}</div>
                <div class="truncate text-xs" :class="getRarityTextClass(item.rarity)">{{ item.skin }}</div>
                <div class="flex items-center justify-center mt-1">
                  <img src="/images/dollar.svg" alt="price" class="w-3 h-3 mr-0.5">
                  <span 
                    class="text-xs font-bold"
                    :class="item.price > 50 ? 'text-yellow-400' : 'text-emerald-400'"
                  >
                    {{ item.price.toFixed(2) }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 控制按钮 -->
      <div class="flex justify-center gap-4 mb-6" v-if="battleData.status === 'waiting'">
        <button 
          @click="startBattle"
          class="bg-primary hover:bg-primary/90 text-black font-bold py-3 px-8 rounded-lg transition-all duration-300 flex items-center gap-2"
          :disabled="!canStart"
        >
          <i class="i-ph-play"></i>
          开始对战
        </button>
        
        <button 
          @click="inviteFriend"
          class="bg-zinc-700 hover:bg-zinc-600 text-white py-3 px-6 rounded-lg transition-all duration-300 flex items-center gap-2"
        >
          <i class="i-ph-user-plus"></i>
          邀请好友
        </button>
      </div>
      
      <!-- 箱子列表 -->
      <div class="mt-6">
        <h4 class="text-lg font-bold mb-3">对战箱子</h4>
        <div class="flex flex-wrap gap-3">
          <div 
            v-for="(caseItem, index) in battleData.cases" 
            :key="index"
            class="flex bg-zinc-800/80 rounded-lg overflow-hidden"
          >
            <div class="w-12 h-12 relative flex-shrink-0">
              <img 
                :src="caseItem.image" 
                :alt="caseItem.name"
                class="w-full h-full object-contain p-1"
              >
            </div>
            <div class="px-3 py-1.5 flex flex-col justify-center">
              <div class="text-sm font-medium truncate">{{ caseItem.name }}</div>
              <div class="flex items-center">
                <img src="/images/dollar.svg" alt="price" class="w-3 h-3 mr-0.5">
                <span class="text-xs text-emerald-400">{{ caseItem.price.toFixed(2) }}</span>
                <span class="text-xs text-white/50 ml-2">× {{ caseItem.count }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 最近对战记录 -->
      <div class="mt-8" v-if="recentBattles.length > 0">
        <h4 class="text-lg font-bold mb-3">最近对战</h4>
        <div class="grid grid-cols-1 sm:grid-cols-2 gap-3">
          <div 
            v-for="battle in recentBattles" 
            :key="battle.id"
            class="bg-zinc-800/50 rounded-lg p-3 flex items-center gap-3 cursor-pointer hover:bg-zinc-700/50 transition-colors duration-300"
            @click="viewBattle(battle.id)"
          >
            <div class="flex -space-x-2">
              <img 
                v-for="(player, idx) in battle.players.slice(0, 3)" 
                :key="idx"
                :src="player.avatar" 
                :alt="player.username"
                class="w-8 h-8 rounded-full border border-zinc-800 object-cover"
              >
              <div 
                v-if="battle.players.length > 3" 
                class="w-8 h-8 rounded-full bg-zinc-700 flex items-center justify-center text-xs font-bold"
              >
                +{{ battle.players.length - 3 }}
              </div>
            </div>
            
            <div class="flex-1 min-w-0">
              <div class="text-sm font-medium truncate">{{ battle.title }}</div>
              <div class="text-xs text-white/60">{{ formatDate(battle.created) }}</div>
            </div>
            
            <div class="flex items-center">
              <img src="/images/dollar.svg" alt="price" class="w-3 h-3 mr-0.5">
              <span class="text-xs text-emerald-400">{{ battle.totalPrice.toFixed(2) }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';

const props = defineProps({
  battleData: {
    type: Object,
    required: true
  },
  recentBattles: {
    type: Array,
    default: () => []
  }
});

const emit = defineEmits(['start-battle', 'invite-friend', 'view-battle']);

// 计算是否可以开始对战
const canStart = computed(() => {
  // 检查所有座位是否已被玩家占据
  return props.battleData.players.length >= 2;
});



// 查看历史对战
const viewBattle = (battleId) => {
  emit('view-battle', battleId);
};

// 计算对战总价值
const getTotalPrice = () => {
  let total = 0;
  props.battleData.players.forEach(player => {
    player.items.forEach(item => {
      total += item.price;
    });
  });
  return total;
};

// 计算玩家获得物品总价值
const getPlayerTotal = (player) => {
  return player.items.reduce((sum, item) => sum + item.price, 0);
};

// 格式化日期
const formatDate = (dateString) => {
  const date = new Date(dateString);
  const now = new Date();
  const diff = Math.floor((now - date) / 1000);
  
  if (diff < 60) {
    return '刚刚';
  } else if (diff < 3600) {
    return `${Math.floor(diff / 60)}分钟前`;
  } else if (diff < 86400) {
    return `${Math.floor(diff / 3600)}小时前`;
  } else {
    return `${date.getMonth() + 1}月${date.getDate()}日`;
  }
};

// 获取对战状态文本
const getBattleStatusText = () => {
  switch (props.battleData.status) {
    case 'waiting':
      return '等待开始';
    case 'in_progress':
      return '进行中';
    case 'completed':
      return '已结束';
    default:
      return '未知状态';
  }
};

// 获取对战状态样式
const getBattleStatusClass = () => {
  switch (props.battleData.status) {
    case 'waiting':
      return 'bg-blue-500/20 text-blue-400';
    case 'in_progress':
      return 'bg-amber-500/20 text-amber-400';
    case 'completed':
      return 'bg-green-500/20 text-green-400';
    default:
      return 'bg-gray-500/20 text-gray-400';
  }
};

// 获取稀有度文本颜色
const getRarityTextClass = (rarity) => {
  switch (rarity) {
    case 'blue': return 'text-blue-400';
    case 'purple': return 'text-purple-400';
    case 'pink': return 'text-pink-400';
    case 'red': return 'text-red-400';
    case 'gold': return 'text-yellow-400';
    default: return 'text-gray-400';
  }
};

// 获取稀有度背景颜色
const getRarityBgClass = (rarity) => {
  switch (rarity) {
    case 'blue': return 'bg-blue-500';
    case 'purple': return 'bg-purple-500';
    case 'pink': return 'bg-pink-500';
    case 'red': return 'bg-red-500';
    case 'gold': return 'bg-yellow-400';
    default: return 'bg-gray-500';
  }
};
</script>

<style scoped>
.player-card {
  @apply bg-zinc-800/30 rounded-xl p-4 border border-transparent transition-all duration-300;
}

.player-card.winner {
  @apply border-yellow-400/50 bg-gradient-to-b from-yellow-400/10 to-zinc-800/30;
}

.pulse-animation {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}
</style> 