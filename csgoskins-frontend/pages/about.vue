<template>
  <div class="min-h-screen relative">
    <!-- 增强背景层 -->
    <div class="absolute inset-0 overflow-hidden">
      <!-- 动态渐变背景 -->
      <div class="absolute inset-0 bg-gradient-to-b from-background-darker via-background to-background-dark z-0" />
      <!-- 粒子效果层 -->
      <div class="particle-container absolute inset-0 z-0" />
      <!-- 动态光线效果 -->
      <div class="absolute top-0 left-0 w-full h-full overflow-hidden opacity-15 z-0">
        <div class="light-beam light-beam-1" />
        <div class="light-beam light-beam-3" />
        <div class="light-beam light-beam-2" />
      </div>
      <!-- 网格线效果 -->
      <div class="absolute inset-0 bg-grid-pattern opacity-[0.04] z-0" />
    </div>

    <!-- PC版内容 -->
    <div class="container mx-auto px-4 py-6 relative z-10" v-if="!isMobile">
      <div class="max-w-6xl mx-auto">
        <!-- 页面头部 -->
        <div class="page-header mb-12">
          <h1 class="text-4xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-primary to-secondary mb-4">
            {{ $t('about.title') }}
          </h1>
          <p class="text-white/60 text-lg">{{ $t('about.subtitle') }}</p>
        </div>

        <!-- 平台简介 -->
        <div class="about-section mb-16">
          <div class="section-content mb-8">
            <h2 class="section-title text-transparent bg-clip-text bg-gradient-to-r from-primary to-primary-600">
              {{ $t('about.platform_intro.title') }}
            </h2>
            <p class="section-text">{{ $t('about.platform_intro.paragraph1') }}</p>
            <p class="section-text">{{ $t('about.platform_intro.paragraph2') }}</p>
          </div>
          <div class="stats-grid">
            <div class="stat-card backdrop-blur-sm hover:transform hover:scale-105 transition-all duration-300">
              <div class="stat-value text-transparent bg-clip-text bg-gradient-primary">1,500,000+</div>
              <div class="stat-label">{{ $t('about.stats.registered_users') }}</div>
            </div>
            <div class="stat-card backdrop-blur-sm hover:transform hover:scale-105 transition-all duration-300">
              <div class="stat-value text-transparent bg-clip-text bg-gradient-primary">25,000,000+</div>
              <div class="stat-label">{{ $t('about.stats.opening_count') }}</div>
            </div>
            <div class="stat-card backdrop-blur-sm hover:transform hover:scale-105 transition-all duration-300">
              <div class="stat-value text-transparent bg-clip-text bg-gradient-primary">500,000+</div>
              <div class="stat-label">{{ $t('about.stats.delivered_items') }}</div>
            </div>
            <div class="stat-card backdrop-blur-sm hover:transform hover:scale-105 transition-all duration-300">
              <div class="stat-value text-transparent bg-clip-text bg-gradient-primary">100+</div>
              <div class="stat-label">{{ $t('about.stats.case_types') }}</div>
            </div>
          </div>
        </div>

        <!-- 我们的使命 -->
        <div class="about-section mb-16">
          <div class="mission-card backdrop-blur-sm">
            <div class="mission-content">
              <h2 class="section-title text-center text-transparent bg-clip-text bg-gradient-primary">
                {{ $t('about.mission.title') }}
              </h2>
              <p class="section-text text-center">{{ $t('about.mission.description') }}</p>
            </div>
          </div>
        </div>

        <!-- 核心价值观 -->
        <div class="about-section mb-16">
          <h2 class="section-title text-transparent bg-clip-text bg-gradient-to-r from-primary to-primary-600">
            {{ $t('about.values.title') }}
          </h2>
          <div class="values-grid">
            <div class="value-card backdrop-blur-sm transform transition-all duration-300 hover:-translate-y-2">
              <div class="value-icon">
                <i class="i-ph-scales text-3xl"></i>
              </div>
              <h3 class="value-title">{{ $t('about.values.fairness.title') }}</h3>
              <p class="value-text">{{ $t('about.values.fairness.description') }}</p>
            </div>
            <div class="value-card backdrop-blur-sm transform transition-all duration-300 hover:-translate-y-2">
              <div class="value-icon">
                <i class="i-ph-shield-check-fill text-3xl"></i>
              </div>
              <h3 class="value-title">{{ $t('about.values.security.title') }}</h3>
              <p class="value-text">{{ $t('about.values.security.description') }}</p>
            </div>
            <div class="value-card backdrop-blur-sm transform transition-all duration-300 hover:-translate-y-2">
              <div class="value-icon">
                <i class="i-ph-user-focus-fill text-3xl"></i>
              </div>
              <h3 class="value-title">{{ $t('about.values.user_first.title') }}</h3>
              <p class="value-text">{{ $t('about.values.user_first.description') }}</p>
            </div>
            <div class="value-card backdrop-blur-sm transform transition-all duration-300 hover:-translate-y-2">
              <div class="value-icon">
                <i class="i-ph-lightbulb-fill text-3xl"></i>
              </div>
              <h3 class="value-title">{{ $t('about.values.innovation.title') }}</h3>
              <p class="value-text">{{ $t('about.values.innovation.description') }}</p>
            </div>
          </div>
        </div>

        <!-- 平台特色 -->
        <div class="about-section mb-16">
          <h2 class="section-title text-transparent bg-clip-text bg-gradient-to-r from-primary to-primary-600">
            {{ $t('about.features.title') }}
          </h2>
          <div class="features-container">
            <div class="feature-item backdrop-blur-sm hover:border-primary/30 border border-transparent transition-all duration-300">
              <div class="feature-number">01</div>
              <div class="feature-content">
                <h3 class="feature-title">{{ $t('about.features.case_selection.title') }}</h3>
                <p class="feature-text">{{ $t('about.features.case_selection.description') }}</p>
              </div>
            </div>
            <div class="feature-item backdrop-blur-sm hover:border-primary/30 border border-transparent transition-all duration-300">
              <div class="feature-number">02</div>
              <div class="feature-content">
                <h3 class="feature-title">{{ $t('about.features.opening_modes.title') }}</h3>
                <p class="feature-text">{{ $t('about.features.opening_modes.description') }}</p>
              </div>
            </div>
            <div class="feature-item backdrop-blur-sm hover:border-primary/30 border border-transparent transition-all duration-300">
              <div class="feature-number">03</div>
              <div class="feature-content">
                <h3 class="feature-title">{{ $t('about.features.real_time_delivery.title') }}</h3>
                <p class="feature-text">{{ $t('about.features.real_time_delivery.description') }}</p>
              </div>
            </div>
            <div class="feature-item backdrop-blur-sm hover:border-primary/30 border border-transparent transition-all duration-300">
              <div class="feature-number">04</div>
              <div class="feature-content">
                <h3 class="feature-title">{{ $t('about.features.activities.title') }}</h3>
                <p class="feature-text">{{ $t('about.features.activities.description') }}</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 团队介绍 -->
        <div class="about-section mb-16">
          <h2 class="section-title text-transparent bg-clip-text bg-gradient-to-r from-primary to-primary-600">
            {{ $t('about.team.title') }}
          </h2>
          <div class="team-grid">
            <div class="team-card backdrop-blur-sm group">
              <div class="team-avatar">
                <img src="/demo/avatar1.png" alt="团队成员头像" class="group-hover:scale-110 transition-transform duration-300" />
              </div>
              <h3 class="team-name">{{ $t('about.team.member1.name') }}</h3>
              <p class="team-role">{{ $t('about.team.member1.role') }}</p>
              <p class="team-desc">{{ $t('about.team.member1.description') }}</p>
            </div>
            <div class="team-card backdrop-blur-sm group">
              <div class="team-avatar">
                <img src="/demo/avatar2.png" alt="团队成员头像" class="group-hover:scale-110 transition-transform duration-300" />
              </div>
              <h3 class="team-name">{{ $t('about.team.member2.name') }}</h3>
              <p class="team-role">{{ $t('about.team.member2.role') }}</p>
              <p class="team-desc">{{ $t('about.team.member2.description') }}</p>
            </div>
            <div class="team-card backdrop-blur-sm group">
              <div class="team-avatar">
                <img src="/demo/avatar3.png" alt="团队成员头像" class="group-hover:scale-110 transition-transform duration-300" />
              </div>
              <h3 class="team-name">{{ $t('about.team.member3.name') }}</h3>
              <p class="team-role">{{ $t('about.team.member3.role') }}</p>
              <p class="team-desc">{{ $t('about.team.member3.description') }}</p>
            </div>
            <div class="team-card backdrop-blur-sm group">
              <div class="team-avatar">
                <img src="/demo/avatar4.png" alt="团队成员头像" class="group-hover:scale-110 transition-transform duration-300" />
              </div>
              <h3 class="team-name">{{ $t('about.team.member4.name') }}</h3>
              <p class="team-role">{{ $t('about.team.member4.role') }}</p>
              <p class="team-desc">{{ $t('about.team.member4.description') }}</p>
            </div>
          </div>
        </div>

        <!-- 联系我们 -->
        <div class="about-section">
          <div class="contact-card backdrop-blur-sm hover:border-primary/30 border border-gray-700/30 transition-all duration-300">
            <h2 class="section-title text-transparent bg-clip-text bg-gradient-primary">
              {{ $t('about.join_us.title') }}
            </h2>
            <p class="section-text mb-6">{{ $t('about.join_us.description') }}</p>
            <NuxtLink to="/contact" class="primary-button">
              <i class="i-ph-envelope mr-2"></i>
              {{ $t('about.join_us.contact_button') }}
            </NuxtLink>
          </div>
        </div>
      </div>
    </div>

    <!-- 移动端内容 -->
    <div class="container mx-auto px-3 py-4 relative z-10" v-else>
      <div class="max-w-6xl mx-auto">
        <!-- 页面头部 -->
        <div class="page-header mb-8">
          <h1 class="text-2xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-primary to-secondary mb-3">
            {{ $t('about.title') }}
          </h1>
          <p class="text-white/60 text-sm">{{ $t('about.subtitle') }}</p>
        </div>

        <!-- 平台简介 -->
        <div class="about-section mb-12">
          <div class="section-content mb-6">
            <h2 class="section-title text-transparent bg-clip-text bg-gradient-to-r from-primary to-primary-600">
              {{ $t('about.platform_intro.title') }}
            </h2>
            <p class="section-text">{{ $t('about.platform_intro.paragraph1') }}</p>
            <p class="section-text">{{ $t('about.platform_intro.paragraph2') }}</p>
          </div>
          <div class="stats-grid">
            <div class="stat-card backdrop-blur-sm">
              <div class="stat-value text-transparent bg-clip-text bg-gradient-primary">1,500,000+</div>
              <div class="stat-label">{{ $t('about.stats.registered_users') }}</div>
            </div>
            <div class="stat-card backdrop-blur-sm">
              <div class="stat-value text-transparent bg-clip-text bg-gradient-primary">25,000,000+</div>
              <div class="stat-label">{{ $t('about.stats.opening_count') }}</div>
            </div>
            <div class="stat-card backdrop-blur-sm">
              <div class="stat-value text-transparent bg-clip-text bg-gradient-primary">500,000+</div>
              <div class="stat-label">{{ $t('about.stats.delivered_items') }}</div>
            </div>
            <div class="stat-card backdrop-blur-sm">
              <div class="stat-value text-transparent bg-clip-text bg-gradient-primary">100+</div>
              <div class="stat-label">{{ $t('about.stats.case_types') }}</div>
            </div>
          </div>
        </div>

        <!-- 我们的使命 -->
        <div class="about-section mb-12">
          <div class="mission-card backdrop-blur-sm">
            <div class="mission-content">
              <h2 class="section-title text-center text-transparent bg-clip-text bg-gradient-primary">
                {{ $t('about.mission.title') }}
              </h2>
              <p class="section-text text-center">{{ $t('about.mission.description') }}</p>
            </div>
          </div>
        </div>

        <!-- 核心价值观 -->
        <div class="about-section mb-12">
          <h2 class="section-title text-transparent bg-clip-text bg-gradient-to-r from-primary to-primary-600">
            {{ $t('about.values.title') }}
          </h2>
          <div class="values-grid">
            <div class="value-card backdrop-blur-sm">
              <div class="value-icon">
                <i class="i-ph-scales text-2xl"></i>
              </div>
              <h3 class="value-title">{{ $t('about.values.fairness.title') }}</h3>
              <p class="value-text">{{ $t('about.values.fairness.description') }}</p>
            </div>
            <div class="value-card backdrop-blur-sm">
              <div class="value-icon">
                <i class="i-ph-shield-check-fill text-2xl"></i>
              </div>
              <h3 class="value-title">{{ $t('about.values.security.title') }}</h3>
              <p class="value-text">{{ $t('about.values.security.description') }}</p>
            </div>
            <div class="value-card backdrop-blur-sm">
              <div class="value-icon">
                <i class="i-ph-user-focus-fill text-2xl"></i>
              </div>
              <h3 class="value-title">{{ $t('about.values.user_first.title') }}</h3>
              <p class="value-text">{{ $t('about.values.user_first.description') }}</p>
            </div>
            <div class="value-card backdrop-blur-sm">
              <div class="value-icon">
                <i class="i-ph-lightbulb-fill text-2xl"></i>
              </div>
              <h3 class="value-title">{{ $t('about.values.innovation.title') }}</h3>
              <p class="value-text">{{ $t('about.values.innovation.description') }}</p>
            </div>
          </div>
        </div>

        <!-- 平台特色 -->
        <div class="about-section mb-12">
          <h2 class="section-title text-transparent bg-clip-text bg-gradient-to-r from-primary to-primary-600">
            {{ $t('about.features.title') }}
          </h2>
          <div class="features-container">
            <div class="feature-item backdrop-blur-sm border border-transparent">
              <div class="feature-number">01</div>
              <div class="feature-content">
                <h3 class="feature-title">{{ $t('about.features.case_selection.title') }}</h3>
                <p class="feature-text">{{ $t('about.features.case_selection.description') }}</p>
              </div>
            </div>
            <div class="feature-item backdrop-blur-sm border border-transparent">
              <div class="feature-number">02</div>
              <div class="feature-content">
                <h3 class="feature-title">{{ $t('about.features.opening_modes.title') }}</h3>
                <p class="feature-text">{{ $t('about.features.opening_modes.description') }}</p>
              </div>
            </div>
            <div class="feature-item backdrop-blur-sm border border-transparent">
              <div class="feature-number">03</div>
              <div class="feature-content">
                <h3 class="feature-title">{{ $t('about.features.real_time_delivery.title') }}</h3>
                <p class="feature-text">{{ $t('about.features.real_time_delivery.description') }}</p>
              </div>
            </div>
            <div class="feature-item backdrop-blur-sm border border-transparent">
              <div class="feature-number">04</div>
              <div class="feature-content">
                <h3 class="feature-title">{{ $t('about.features.activities.title') }}</h3>
                <p class="feature-text">{{ $t('about.features.activities.description') }}</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 团队介绍 -->
        <div class="about-section mb-12">
          <h2 class="section-title text-transparent bg-clip-text bg-gradient-to-r from-primary to-primary-600">
            {{ $t('about.team.title') }}
          </h2>
          <div class="team-grid">
            <div class="team-card backdrop-blur-sm">
              <div class="team-avatar">
                <img src="/demo/avatar1.png" alt="团队成员头像" />
              </div>
              <h3 class="team-name">{{ $t('about.team.member1.name') }}</h3>
              <p class="team-role">{{ $t('about.team.member1.role') }}</p>
              <p class="team-desc">{{ $t('about.team.member1.description') }}</p>
            </div>
            <div class="team-card backdrop-blur-sm">
              <div class="team-avatar">
                <img src="/demo/avatar2.png" alt="团队成员头像" />
              </div>
              <h3 class="team-name">{{ $t('about.team.member2.name') }}</h3>
              <p class="team-role">{{ $t('about.team.member2.role') }}</p>
              <p class="team-desc">{{ $t('about.team.member2.description') }}</p>
            </div>
            <div class="team-card backdrop-blur-sm">
              <div class="team-avatar">
                <img src="/demo/avatar3.png" alt="团队成员头像" />
              </div>
              <h3 class="team-name">{{ $t('about.team.member3.name') }}</h3>
              <p class="team-role">{{ $t('about.team.member3.role') }}</p>
              <p class="team-desc">{{ $t('about.team.member3.description') }}</p>
            </div>
            <div class="team-card backdrop-blur-sm">
              <div class="team-avatar">
                <img src="/demo/avatar4.png" alt="团队成员头像" />
              </div>
              <h3 class="team-name">{{ $t('about.team.member4.name') }}</h3>
              <p class="team-role">{{ $t('about.team.member4.role') }}</p>
              <p class="team-desc">{{ $t('about.team.member4.description') }}</p>
            </div>
          </div>
        </div>

        <!-- 联系我们 -->
        <div class="about-section">
          <div class="contact-card backdrop-blur-sm border border-gray-700/30">
            <h2 class="section-title text-transparent bg-clip-text bg-gradient-primary">
              {{ $t('about.join_us.title') }}
            </h2>
            <p class="section-text mb-6">{{ $t('about.join_us.description') }}</p>
            <NuxtLink to="/contact" class="primary-button">
              <i class="i-ph-envelope mr-2"></i>
              {{ $t('about.join_us.contact_button') }}
            </NuxtLink>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useAppStore } from '~/stores/app'

// SEO配置
const { $i18n } = useNuxtApp()
useHead({
  title: () => $i18n.t('about.title'),
  meta: [
    {
      name: 'description',
      content: () => $i18n.t('about.description')
    },
    {
      name: 'keywords',
      content: 'CSGO, CS:GO, about, company, team, mission, values, 关于我们, 公司介绍'
    },
    {
      property: 'og:title',
      content: () => $i18n.t('about.title')
    },
    {
      property: 'og:description',
      content: () => $i18n.t('about.description')
    }
  ]
})

// 获取设备状态
const store = useAppStore()
const isMobile = computed(() => store.isMobile)
</script>

<style scoped>
/* 背景网格 */
.bg-grid-pattern {
  background-image: 
    linear-gradient(to right, rgba(255, 255, 255, 0.05) 0.0625rem, transparent 0.0625rem),
    linear-gradient(to bottom, rgba(255, 255, 255, 0.05) 0.0625rem, transparent 0.0625rem);
  background-size: 1.25rem 1.25rem;
}

/* 粒子效果 */
.particle-container {
  background-image: radial-gradient(circle at 25% 25%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
                    radial-gradient(circle at 75% 75%, rgba(245, 158, 11, 0.1) 0%, transparent 50%);
}

/* 光线效果 */
.light-beam {
  position: absolute;
  width: 1px;
  height: 100%;
  background: linear-gradient(to bottom, transparent, rgba(59, 130, 246, 0.3), transparent);
  animation: light-beam-animation 8s ease-in-out infinite;
}

.light-beam-1 {
  left: 20%;
  animation-delay: 0s;
}

.light-beam-2 {
  left: 50%;
  animation-delay: 2s;
}

.light-beam-3 {
  left: 80%;
  animation-delay: 4s;
}

@keyframes light-beam-animation {
  0%, 100% { opacity: 0; transform: scaleY(0); }
  50% { opacity: 1; transform: scaleY(1); }
}

.page-header {
  @apply py-8 bg-gradient-to-r from-gray-900/80 to-gray-800/80 rounded-xl;
}

.about-section {
  @apply mb-16;
}

.section-title {
  @apply text-2xl font-bold mb-6;
}

.section-text {
  @apply text-white/80 leading-relaxed mb-4;
}

.stats-grid {
  @apply grid grid-cols-2 md:grid-cols-4 gap-4 mt-8;
}

.stat-card {
  @apply bg-gray-800/40 rounded-xl p-6 text-center border border-gray-700/30 shadow-lg;
}

.stat-value {
  @apply text-2xl md:text-3xl font-bold mb-2;
}

.stat-label {
  @apply text-white/70 text-sm;
}

.mission-card {
  @apply bg-gradient-to-r from-primary/5 to-secondary/5 rounded-xl p-10 border border-gray-700/30 shadow-lg;
}

.mission-content {
  @apply max-w-2xl mx-auto;
}

.values-grid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6;
}

.value-card {
  @apply bg-gray-800/40 rounded-xl p-6 border border-gray-700/30 shadow-lg;
}

.value-icon {
  @apply w-12 h-12 flex items-center justify-center text-primary mb-4;
}

.value-title {
  @apply text-white font-bold text-lg mb-2;
}

.value-text {
  @apply text-white/70 text-sm;
}

.features-container {
  @apply space-y-6;
}

.feature-item {
  @apply flex gap-6 items-start bg-gray-800/40 rounded-xl p-6 shadow-lg;
}

.feature-number {
    @apply text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-primary to-secondary;
}

.feature-title {
  @apply text-white font-bold text-lg mb-2;
}

.feature-text {
  @apply text-white/70;
}

.team-grid {
  @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6;
}

.team-card {
  @apply bg-gray-800/40 rounded-xl p-6 text-center border border-gray-700/30 shadow-lg;
}

.team-avatar {
  @apply w-24 h-24 mx-auto mb-4 overflow-hidden rounded-full;
}

.team-avatar img {
  @apply w-full h-full object-cover rounded-full border-2 border-primary/40 transition-all duration-300;
}

.team-name {
  @apply text-white font-bold text-lg mb-1;
}

.team-role {
  @apply text-primary text-sm mb-3;
}

.team-desc {
  @apply text-white/70 text-sm;
}

.contact-card {
  @apply bg-gray-800/40 rounded-xl p-8 text-center max-w-2xl mx-auto shadow-lg;
}

.primary-button {
  @apply inline-flex items-center bg-gradient-to-r from-primary to-secondary hover:from-primary-light hover:to-secondary-light text-white font-medium py-2 px-6 rounded-lg transition-all duration-300 transform hover:-translate-y-0.5 shadow-md;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .feature-item {
    @apply flex-col;
  }
  
  .feature-number {
    @apply mb-2;
  }
  
  .stats-grid {
    @apply grid-cols-2 gap-3;
  }
  
  .stat-card {
    @apply p-4;
  }
  
  .stat-value {
    @apply text-xl;
  }
  
  .values-grid {
    @apply grid-cols-1 gap-4;
  }
  
  .team-grid {
    @apply grid-cols-1 gap-4;
  }
  
  .team-avatar {
    @apply w-20 h-20;
  }
}
</style> 