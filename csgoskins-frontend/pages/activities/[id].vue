<template>
  <div class="min-h-screen relative">
    <!-- 背景装饰 -->
    <div class="fixed inset-0 -z-10">
      <div class="absolute top-0 -left-32 w-96 h-96 rounded-full blur-3xl bg-primary/5 animate-pulse-slow"></div>
      <div class="absolute bottom-0 -right-32 w-96 h-96 rounded-full blur-3xl bg-secondary/5 animate-pulse-slow"></div>
      <div class="absolute inset-0 bg-grid-pattern opacity-10"></div>
      <div class="absolute inset-0 bg-noise opacity-30 mix-blend-soft-light"></div>
    </div>
    
    <!-- PC端内容 -->
    <div class="container mx-auto px-4 py-8 relative z-10" v-if="!isMobile">
      <!-- 导航条 -->
      <div class="navigation-bar backdrop-blur-md mb-6">
        <div class="flex items-center">
          <NuxtLink to="/" class="text-white/70 hover:text-white text-sm flex items-center transition-all duration-300">
            <i class="i-heroicons-arrow-left w-4 h-4 mr-1"></i>
            {{ $t('common.home') }}
          </NuxtLink>
          <span class="mx-2 text-white/30">/</span>
          <NuxtLink to="/activities" class="text-white/70 hover:text-white text-sm transition-all duration-300">
            {{ $t('activities.title') }}
          </NuxtLink>
          <span class="mx-2 text-white/30">/</span>
          <span class="text-white/90 text-sm font-medium">{{ activity.title }}</span>
        </div>
      </div>
      
      <!-- 活动横幅 -->
      <div class="activity-banner relative mb-8 rounded-2xl overflow-hidden" :style="{ backgroundImage: `url(${activity.image})` }">
        <div class="banner-overlay backdrop-blur-sm">
          <div class="p-8">
            <div class="max-w-4xl">
              <h1 class="text-3xl md:text-4xl font-bold mb-4 text-gradient">{{ activity.title }}</h1>
              
              <!-- 活动状态标签 -->
              <div class="status-badge inline-block" :class="getStatusClass(activity.status)">
                {{ getStatusText(activity.status) }}
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 活动内容 -->
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- 左侧: 活动详情和任务 -->
        <div class="lg:col-span-2">
          <!-- 活动信息和倒计时 -->
          <div class="backdrop-card mb-8">
            <div class="flex flex-col md:flex-row md:items-center justify-between mb-6">
              <div>
                <div class="text-white/60 text-sm mb-1">{{ getTimeLabel }}</div>
                <div v-if="activity.status !== 'ended'" class="text-2xl font-bold text-primary">
                  {{ formatCountdown }}
                </div>
                <div v-else class="text-white/40 font-medium">
                  {{ $t('activities.ended_at', { date: formatDate(activity.endTime) }) }}
                </div>
              </div>
              
              <!-- 分享和提醒按钮 -->
              <div class="flex mt-4 md:mt-0 space-x-3">
                <button v-if="activity.status === 'upcoming'" class="btn-reminder">
                  <i class="i-heroicons-bell mr-1"></i>
                  {{ $t('activities.set_reminder') }}
                </button>
                <button v-else-if="activity.status === 'active'" class="btn-participate">
                  <i class="i-heroicons-play mr-1"></i>
                  {{ $t('activities.participate_now') }}
                </button>
                <button class="btn-share">
                  <i class="i-heroicons-share mr-1"></i>
                  {{ $t('activities.share') }}
                </button>
              </div>
            </div>
            
            <!-- 活动描述 -->
            <div class="mb-8">
              <h2 class="section-title text-gradient-subtle">{{ $t('activities.details') }}</h2>
              <div class="h-0.5 w-16 bg-gradient-primary rounded-full mb-4"></div>
              <p class="text-white/80 leading-relaxed">
                {{ activity.description }}
              </p>
              <div v-if="activity.longDescription" class="mt-4 text-white/80 leading-relaxed">
                {{ activity.longDescription }}
              </div>
            </div>
            
            <!-- 活动任务 -->
            <div class="tasks-section">
              <h2 class="section-title text-gradient-subtle">{{ $t('activities.tasks') }}</h2>
              <div class="h-0.5 w-16 bg-gradient-primary rounded-full mb-4"></div>
              <div class="tasks-grid">
                <div 
                  v-for="(task, index) in activity.tasks" 
                  :key="index"
                  class="task-item"
                  :class="{ 'completed': task.completed, 'locked': task.locked }"
                >
                  <div class="task-icon">
                    <i v-if="task.completed" class="i-heroicons-check-circle text-green-400"></i>
                    <i v-else-if="task.locked" class="i-heroicons-lock-closed text-gray-500"></i>
                    <i v-else class="i-heroicons-circle text-gray-400"></i>
                  </div>
                  <div class="task-content">
                    <div class="task-name">{{ task.name }}</div>
                    <div class="task-reward">{{ $t('activities.reward') }}</div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 活动规则 -->
            <div class="rules-section mt-8">
              <h2 class="section-title text-gradient-subtle">{{ $t('activities.rules') }}</h2>
              <div class="h-0.5 w-16 bg-gradient-primary rounded-full mb-4"></div>
              <ul class="list-disc pl-5 mt-4 space-y-2 text-white/80">
                <li v-for="(rule, index) in activity.rules" :key="index">
                  {{ rule }}
                </li>
              </ul>
            </div>
          </div>
        </div>
        
        <!-- 右侧: 参与信息和奖励 -->
        <div class="lg:col-span-1">
          <!-- 活动数据卡片 -->
          <div class="backdrop-card mb-6">
            <h3 class="card-title text-gradient-subtle">{{ $t('activities.stats') }}</h3>
            <div class="h-0.5 w-12 bg-gradient-primary rounded-full mb-4"></div>
            <div class="stats-grid">
              <div class="stat-item group hover:border-primary/30 transition-all duration-300">
                <div class="stat-value group-hover:text-gradient transition-all duration-300">{{ formatDate(activity.startTime) }}</div>
                <div class="stat-label">{{ $t('activities.start_time') }}</div>
              </div>
              <div class="stat-item group hover:border-primary/30 transition-all duration-300">
                <div class="stat-value group-hover:text-gradient transition-all duration-300">{{ formatDate(activity.endTime) }}</div>
                <div class="stat-label">{{ $t('activities.end_time') }}</div>
              </div>
              <div class="stat-item group hover:border-primary/30 transition-all duration-300">
                <div class="stat-value group-hover:text-gradient transition-all duration-300">{{ activity.participants }}</div>
                <div class="stat-label">{{ $t('activities.participants') }}</div>
              </div>
              <div class="stat-item group hover:border-primary/30 transition-all duration-300">
                <div class="stat-value group-hover:text-gradient transition-all duration-300">{{ completedTasksCount }}</div>
                <div class="stat-label">{{ $t('activities.completed_tasks') }}</div>
              </div>
            </div>
          </div>
          
          <!-- 活动奖励 -->
          <div class="backdrop-card mb-6">
            <h3 class="card-title text-gradient-subtle">{{ $t('activities.rewards') }}</h3>
            <div class="h-0.5 w-12 bg-gradient-primary rounded-full mb-4"></div>
            <div class="rewards-list">
              <div 
                v-for="(reward, index) in activity.rewards" 
                :key="index"
                class="reward-item hover:border-primary/30 hover:bg-gray-800/50 transition-all duration-300"
              >
                <div class="reward-icon">
                  <i class="i-heroicons-gift text-2xl text-primary/80"></i>
                </div>
                <div class="reward-text">{{ reward }}</div>
              </div>
            </div>
          </div>
          
          <!-- 参与者列表 -->
          <div class="backdrop-card">
            <h3 class="card-title text-gradient-subtle">{{ $t('activities.top_participants') }}</h3>
            <div class="h-0.5 w-12 bg-gradient-primary rounded-full mb-4"></div>
            <div class="participants-list">
              <div 
                v-for="(participant, index) in topParticipants" 
                :key="index"
                class="participant-item"
              >
                <div class="participant-rank">{{ index + 1 }}</div>
                <div class="participant-avatar">
                  <img :src="participant.avatar" :alt="participant.name" />
                </div>
                <div class="participant-info">
                  <div class="participant-name">{{ participant.name }}</div>
                  <div class="participant-score">{{ participant.score }} {{ $t('activities.points') }}</div>
                </div>
              </div>
            </div>
            <button class="view-more-btn">
              {{ $t('activities.view_all_participants') }}
            </button>
          </div>
        </div>
      </div>
      
      <!-- 分享区域 - 仅在活动已结束时显示 -->
      <div v-if="activity.status === 'ended'" class="share-section backdrop-card mt-8">
        <h2 class="section-title text-gradient-subtle mb-4">{{ $t('activities.share_results') }}</h2>
        <div class="h-0.5 w-16 bg-gradient-primary rounded-full mb-4"></div>
        <div class="share-buttons">
          <button class="share-btn facebook">
            <i class="i-simple-icons-facebook text-lg mr-1"></i>
            Facebook
          </button>
          <button class="share-btn twitter">
            <i class="i-simple-icons-twitter text-lg mr-1"></i>
            Twitter
          </button>
          <button class="share-btn discord">
            <i class="i-simple-icons-discord text-lg mr-1"></i>
            Discord
          </button>
          <button class="share-btn copy">
            <i class="i-heroicons-link text-lg mr-1"></i>
            {{ $t('activities.copy_link') }}
          </button>
        </div>
      </div>
    </div>

    <!-- 移动端内容 -->
    <div class="container mx-auto px-4 py-4 relative z-10" v-else>
      <!-- 导航条 -->
      <div class="navigation-bar backdrop-blur-md mb-4">
        <div class="flex items-center">
          <NuxtLink to="/" class="text-white/70 hover:text-white text-sm flex items-center transition-all duration-300">
            <i class="i-heroicons-arrow-left w-4 h-4 mr-1"></i>
            {{ $t('common.home') }}
          </NuxtLink>
          <span class="mx-2 text-white/30">/</span>
          <NuxtLink to="/activities" class="text-white/70 hover:text-white text-sm transition-all duration-300">
            {{ $t('activities.title') }}
          </NuxtLink>
        </div>
      </div>
      
      <!-- 活动横幅 -->
      <div class="activity-banner relative mb-6 rounded-xl overflow-hidden" :style="{ backgroundImage: `url(${activity.image})` }">
        <div class="banner-overlay backdrop-blur-sm">
          <div class="p-6">
            <h1 class="text-2xl font-bold mb-3 text-gradient">{{ activity.title }}</h1>
            
            <!-- 活动状态标签 -->
            <div class="status-badge inline-block" :class="getStatusClass(activity.status)">
              {{ getStatusText(activity.status) }}
            </div>
          </div>
        </div>
      </div>
      
      <!-- 活动信息 -->
      <div class="backdrop-card mb-6">
        <div class="mb-4">
          <div class="text-white/60 text-sm mb-1">{{ getTimeLabel }}</div>
          <div v-if="activity.status !== 'ended'" class="text-xl font-bold text-primary">
            {{ formatCountdown }}
          </div>
          <div v-else class="text-white/40 font-medium">
            {{ $t('activities.ended_at', { date: formatDate(activity.endTime) }) }}
          </div>
        </div>
        
        <!-- 操作按钮 -->
        <div class="flex space-x-3">
          <button v-if="activity.status === 'upcoming'" class="btn-reminder-mobile">
            <i class="i-heroicons-bell mr-1"></i>
            {{ $t('activities.set_reminder') }}
          </button>
          <button v-else-if="activity.status === 'active'" class="btn-participate-mobile">
            <i class="i-heroicons-play mr-1"></i>
            {{ $t('activities.participate_now') }}
          </button>
          <button class="btn-share-mobile">
            <i class="i-heroicons-share mr-1"></i>
            {{ $t('activities.share') }}
          </button>
        </div>
      </div>
      
      <!-- 活动描述 -->
      <div class="backdrop-card mb-6">
        <h2 class="section-title text-gradient-subtle mb-3">{{ $t('activities.details') }}</h2>
        <div class="h-0.5 w-12 bg-gradient-primary rounded-full mb-3"></div>
        <p class="text-white/80 leading-relaxed text-sm">
          {{ activity.description }}
        </p>
      </div>
      
      <!-- 活动任务 -->
      <div class="backdrop-card mb-6">
        <h2 class="section-title text-gradient-subtle mb-3">{{ $t('activities.tasks') }}</h2>
        <div class="h-0.5 w-12 bg-gradient-primary rounded-full mb-3"></div>
        <div class="tasks-list">
          <div 
            v-for="(task, index) in activity.tasks" 
            :key="index"
            class="task-item-mobile"
            :class="{ 'completed': task.completed, 'locked': task.locked }"
          >
            <div class="task-icon">
              <i v-if="task.completed" class="i-heroicons-check-circle text-green-400"></i>
              <i v-else-if="task.locked" class="i-heroicons-lock-closed text-gray-500"></i>
              <i v-else class="i-heroicons-circle text-gray-400"></i>
            </div>
            <div class="task-content">
              <div class="task-name">{{ task.name }}</div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 活动奖励 -->
      <div class="backdrop-card mb-6">
        <h2 class="section-title text-gradient-subtle mb-3">{{ $t('activities.rewards') }}</h2>
        <div class="h-0.5 w-12 bg-gradient-primary rounded-full mb-3"></div>
        <div class="rewards-list-mobile">
          <div 
            v-for="(reward, index) in activity.rewards" 
            :key="index"
            class="reward-item-mobile"
          >
            <i class="i-heroicons-gift text-primary mr-2"></i>
            <span>{{ reward }}</span>
          </div>
        </div>
      </div>
      
      <!-- 活动统计 -->
      <div class="backdrop-card">
        <h2 class="section-title text-gradient-subtle mb-3">{{ $t('activities.stats') }}</h2>
        <div class="h-0.5 w-12 bg-gradient-primary rounded-full mb-3"></div>
        <div class="stats-grid-mobile">
          <div class="stat-item-mobile">
            <div class="stat-value">{{ activity.participants }}</div>
            <div class="stat-label">{{ $t('activities.participants') }}</div>
          </div>
          <div class="stat-item-mobile">
            <div class="stat-value">{{ completedTasksCount }}</div>
            <div class="stat-label">{{ $t('activities.completed_tasks') }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 国际化
const { t } = useI18n()

// 设备检测 - 使用响应式状态
const isMobile = ref(false)

// 在客户端检测设备类型
onMounted(() => {
  const checkMobile = () => {
    isMobile.value = window.innerWidth < 768
  }
  
  checkMobile()
  window.addEventListener('resize', checkMobile)
  
  onUnmounted(() => {
    window.removeEventListener('resize', checkMobile)
  })
})

// 获取路由参数
const route = useRoute()
const activityId = route.params.id

// SEO配置
useSeoMeta({
  title: '活动详情 - CSGO开箱网站',
  description: '查看活动详情，参与活动赢取奖励',
  keywords: '活动详情,CSGO开箱,游戏活动,奖励活动',
  ogTitle: '活动详情 - CSGO开箱网站',
  ogDescription: '查看活动详情，参与活动赢取奖励',
  twitterTitle: '活动详情 - CSGO开箱网站',
  twitterDescription: '查看活动详情，参与活动赢取奖励'
})

// 模拟的活动数据
const activity = ref({
  id: 1,
  title: '暑期开箱狂欢节',
  description: '参与暑期开箱活动，赢取稀有皮肤和实物奖励',
  longDescription: '这是一个为期7天的暑期特别活动，玩家可以通过完成各种任务获得稀有皮肤、实物周边和平台代币。活动期间，所有开箱概率都会有所提升，让您更容易获得心仪的物品。',
  image: '/demo/banner1.png',
  startTime: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
  endTime: new Date(Date.now() + 4 * 24 * 60 * 60 * 1000).toISOString(),
  status: 'active',
  participants: 1245,
  rewards: ['限定武器皮肤', '实物周边', '平台代币'],
  rules: [
    '活动期间每人最多可获得3个限定皮肤',
    '实物奖励将在活动结束后7个工作日内发放',
    '平台代币可直接用于购买箱子或皮肤',
    '如有作弊行为，将取消参与资格'
  ],
  tasks: [
    { name: '每日登录', completed: true },
    { name: '开启5个武器箱', completed: true },
    { name: '参与3场对战', completed: false },
    { name: '邀请好友', completed: false }
  ]
})

// 参与者数据
const topParticipants = ref([
  { name: '玩家1', avatar: '/demo/avatar1.png', score: 1250 },
  { name: '玩家2', avatar: '/demo/avatar2.png', score: 1180 },
  { name: '玩家3', avatar: '/demo/avatar3.png', score: 1120 },
  { name: '玩家4', avatar: '/demo/avatar4.png', score: 1050 },
  { name: '玩家5', avatar: '/demo/avatar5.png', score: 980 }
])

// 计算属性
const getTimeLabel = computed(() => {
  if (activity.value.status === 'upcoming') return t('activities.starts_in')
  if (activity.value.status === 'active') return t('activities.ends_in')
  return t('activities.ended')
})

const formatCountdown = computed(() => {
  const now = new Date()
  const endTime = new Date(activity.value.endTime)
  const diff = endTime.getTime() - now.getTime()
  
  if (diff <= 0) return t('activities.ended')
  
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))
  
  if (days > 0) return `${days}${t('activities.days')} ${hours}${t('activities.hours')}`
  if (hours > 0) return `${hours}${t('activities.hours')} ${minutes}${t('activities.minutes')}`
  return `${minutes}${t('activities.minutes')}`
})

const completedTasksCount = computed(() => {
  return activity.value.tasks.filter(task => task.completed).length
})

// 方法
const getStatusClass = (status: string) => {
  switch (status) {
    case 'upcoming': return 'status-upcoming'
    case 'active': return 'status-active'
    case 'ended': return 'status-ended'
    default: return 'status-default'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'upcoming': return t('activities.status.upcoming')
    case 'active': return t('activities.status.active')
    case 'ended': return t('activities.status.ended')
    default: return t('activities.status.unknown')
  }
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString()
}
</script>

<style scoped>
/* 导航条样式 */
.navigation-bar {
  @apply bg-white/5 backdrop-blur-md border border-white/10 rounded-xl p-4;
}

/* 活动横幅样式 */
.activity-banner {
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  min-height: 200px;
}

.banner-overlay {
  @apply bg-black/40;
  min-height: 200px;
}

/* 卡片样式 */
.backdrop-card {
  @apply bg-white/5 backdrop-blur-md border border-white/10 rounded-2xl p-6;
  @apply hover:border-white/20 transition-all duration-300;
}

/* 状态标签样式 */
.status-badge {
  @apply px-3 py-1 rounded-full text-xs font-medium;
}

.status-upcoming {
  @apply bg-blue-500/20 text-blue-400 border border-blue-500/30;
}

.status-active {
  @apply bg-green-500/20 text-green-400 border border-green-500/30;
}

.status-ended {
  @apply bg-gray-500/20 text-gray-400 border border-gray-500/30;
}

/* 按钮样式 */
.btn-reminder, .btn-participate, .btn-share {
  @apply px-4 py-2 rounded-xl text-sm font-medium transition-all duration-300;
  @apply bg-white/5 border border-white/10 text-white/70;
  @apply hover:bg-white/10 hover:border-white/20 hover:text-white;
}

.btn-reminder.active, .btn-participate.active {
  @apply bg-primary/20 border-primary text-primary;
}

.btn-reminder-mobile, .btn-participate-mobile, .btn-share-mobile {
  @apply px-3 py-2 rounded-lg text-sm font-medium transition-all duration-300;
  @apply bg-white/5 border border-white/10 text-white/70;
  @apply hover:bg-white/10 hover:border-white/20 hover:text-white;
}

/* 任务样式 */
.tasks-grid {
  @apply space-y-3;
}

.task-item {
  @apply flex items-center p-3 rounded-xl border border-white/10;
  @apply hover:border-white/20 transition-all duration-300;
}

.task-item.completed {
  @apply bg-green-500/10 border-green-500/30;
}

.task-item.locked {
  @apply bg-gray-500/10 border-gray-500/30 opacity-50;
}

.task-icon {
  @apply mr-3;
}

.task-content {
  @apply flex-1;
}

.task-name {
  @apply text-white font-medium;
}

.task-reward {
  @apply text-white/60 text-sm;
}

.tasks-list {
  @apply space-y-2;
}

.task-item-mobile {
  @apply flex items-center p-2 rounded-lg border border-white/10;
  @apply hover:border-white/20 transition-all duration-300;
}

.task-item-mobile.completed {
  @apply bg-green-500/10 border-green-500/30;
}

.task-item-mobile.locked {
  @apply bg-gray-500/10 border-gray-500/30 opacity-50;
}

/* 统计样式 */
.stats-grid {
  @apply grid grid-cols-2 gap-4;
}

.stat-item {
  @apply p-4 rounded-xl border border-white/10 text-center;
  @apply hover:border-primary/30 transition-all duration-300;
}

.stat-value {
  @apply text-2xl font-bold text-white mb-1;
}

.stat-label {
  @apply text-white/60 text-sm;
}

.stats-grid-mobile {
  @apply grid grid-cols-2 gap-3;
}

.stat-item-mobile {
  @apply p-3 rounded-lg border border-white/10 text-center;
}

.stat-item-mobile .stat-value {
  @apply text-lg font-bold text-white mb-1;
}

.stat-item-mobile .stat-label {
  @apply text-white/60 text-xs;
}

/* 奖励样式 */
.rewards-list {
  @apply space-y-3;
}

.reward-item {
  @apply flex items-center p-3 rounded-xl border border-white/10;
  @apply hover:border-primary/30 hover:bg-gray-800/50 transition-all duration-300;
}

.reward-icon {
  @apply mr-3;
}

.reward-text {
  @apply text-white font-medium;
}

.rewards-list-mobile {
  @apply space-y-2;
}

.reward-item-mobile {
  @apply flex items-center p-2 rounded-lg border border-white/10;
  @apply text-white/80 text-sm;
}

/* 参与者样式 */
.participants-list {
  @apply space-y-3 mb-4;
}

.participant-item {
  @apply flex items-center p-3 rounded-xl border border-white/10;
  @apply hover:border-white/20 transition-all duration-300;
}

.participant-rank {
  @apply w-6 h-6 rounded-full bg-primary/20 text-primary text-xs font-bold;
  @apply flex items-center justify-center mr-3;
}

.participant-avatar {
  @apply w-8 h-8 rounded-full overflow-hidden mr-3;
}

.participant-avatar img {
  @apply w-full h-full object-cover;
}

.participant-info {
  @apply flex-1;
}

.participant-name {
  @apply text-white font-medium text-sm;
}

.participant-score {
  @apply text-white/60 text-xs;
}

.view-more-btn {
  @apply w-full py-2 text-center text-primary text-sm font-medium;
  @apply hover:text-primary-light transition-colors;
}

/* 分享按钮样式 */
.share-buttons {
  @apply flex flex-wrap gap-3;
}

.share-btn {
  @apply px-4 py-2 rounded-xl text-sm font-medium transition-all duration-300;
  @apply bg-white/5 border border-white/10 text-white/70;
  @apply hover:bg-white/10 hover:border-white/20 hover:text-white;
}

.share-btn.facebook:hover {
  @apply bg-blue-500/20 border-blue-500/30 text-blue-400;
}

.share-btn.twitter:hover {
  @apply bg-blue-400/20 border-blue-400/30 text-blue-300;
}

.share-btn.discord:hover {
  @apply bg-indigo-500/20 border-indigo-500/30 text-indigo-400;
}

.share-btn.copy:hover {
  @apply bg-gray-500/20 border-gray-500/30 text-gray-400;
}

/* 标题样式 */
.section-title {
  @apply text-xl font-semibold mb-4;
}

.card-title {
  @apply text-lg font-semibold mb-4;
}

/* 渐变文本 */
.text-gradient {
  @apply text-transparent bg-clip-text bg-gradient-to-r from-primary to-secondary;
}

.text-gradient-subtle {
  @apply text-transparent bg-clip-text bg-gradient-to-r from-white/80 to-white/60;
}

/* 网格背景 */
.bg-grid-pattern {
  background-image: linear-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px),
                    linear-gradient(90deg, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
  background-size: 50px 50px;
}

/* 噪声背景 */
.bg-noise {
  background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 200 200' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.65' numOctaves='3' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E");
}

/* 动画 */
.animate-pulse-slow {
  animation: pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}
</style> 