<template>
  <div class="battle-detail-page px-4">
    <!-- 实时模式连接状态 -->
    <BattleConnectionStatus
      v-show="unifiedIsRealtimeMode"
      :is-connected="connectionState.isConnected"
      :is-reconnecting="connectionState.isReconnecting"
      :reconnect-attempts="connectionState.reconnectAttempts"
      @retry-connection="handleRetryConnection"
    />

    <!-- 加载状态：骨架屏 -->
    <BattleDetailSkeleton v-if="pageController.pageState.value.isLoading" />

    <!-- 错误状态 -->
    <div v-if="pageController.pageState.value.error" class="error-container">
      <div class="error-content">
        <Icon name="heroicons:exclamation-triangle" class="w-8 h-8 text-red-400" />
        <p class="text-red-400 mt-2">{{ pageController.pageState.value.error }}</p>
        <button 
          @click="pageController.handleBattleAction('refresh-data')"
          class="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
        >
          {{ t('common.retry') }}
        </button>
      </div>
    </div>

    <!-- 静态模式对战内容（已初始化后显示） -->
    <div v-if="pageController.pageState.value.isInitialized && !unifiedIsRealtimeMode" class="battle-content">
      <div class="content-container">
        <BattleStaticModeIndicator
          :battle-state="unifiedBattleData.state"
          :finished-time="unifiedBattleData.update_time"
        />
        <!-- 对战头部信息 -->
        <BattleHeader
          :battle-data="unifiedBattleData"
          :is-user-creator="isUserCreator"
          :is-user-joined="isUserJoined"
          :is-battle-started="isBattleStarted"
        />
        <!-- 对战状态显示 -->
        <BattleStateDisplay
          :battle-state="battleStateString"
          :current-round="unifiedCurrentRound"
          :total-rounds="unifiedTotalRounds"
          :opening-case-id="openingCaseId"
          :is-battle-started="isBattleStarted"
          :is-battle-finished="isBattleFinished"
        />
        <!-- 箱子展示 -->
        <BattleCaseDisplay
          :cases="[...displayCases]"
          :current-round="unifiedCurrentRound"
          :total-rounds="unifiedTotalRounds"
          :opening-case-id="openingCaseId"
          :is-battle-started="isBattleStarted"
          :is-battle-finished="isBattleFinished"
        />
        <!-- 玩家展示 -->
        <BattlePlayerDisplay
          :players="unifiedBattleData.bets || []"
          :current-round="unifiedCurrentRound"
          :total-rounds="unifiedTotalRounds"
          :max-players="unifiedBattleData.max_joiner || unifiedBattleData.bets.length"
          :is-battle-started="isBattleStarted"
          :is-battle-finished="isBattleFinished"
          :host-uid="unifiedBattleData.user?.uid"
          :is-user-creator="isUserCreator"
          :is-user-joined="isUserJoined"
          :current-user-id="currentUserId"
          :opening-player-name="openingPlayerName"
          :opening-player-index="openingPlayerIndex"
          :current-case-items="[...currentCaseItems]"
          :steps="calculationSteps"
          :can-join="canJoinBattle"
          @join-battle="handleJoinBattle"
          @leave-battle="handleQuitBattle"
          @dismiss-battle="handleDismissBattle"
        />
      </div>
      <!-- 胜利者弹窗 -->
      <BattleWinnerModal
        v-if="canShowWinnerModal"
        :winner-data="winnerData"
        @close="handleCloseWinnerModal"
      />
    </div>

    <!-- 实时模式对战内容（已初始化后显示） -->
    <div v-if="pageController.pageState.value.isInitialized && unifiedIsRealtimeMode" class="battle-content">
      <div class="content-container">
        <!-- 对战头部信息 -->
        <BattleHeader
          :battle-data="unifiedBattleData"
          :is-user-creator="isUserCreator"
          :is-user-joined="isUserJoined"
          :is-battle-started="isBattleStarted"
          @start-battle="handleStartBattle"
          @leave="handleQuitBattle"
          @dismiss="handleDismissBattle"
        />

        <!-- 对战状态显示 -->
        <BattleStateDisplay
          :battle-state="battleStateString"
          :current-round="battleStateSync.currentRound"
          :total-rounds="battleStateSync.totalRounds"
          :opening-case-id="battleStateSync.openingCaseId || undefined"
          :is-battle-started="battleStateSync.isBattleStarted"
          :is-battle-finished="battleStateSync.isBattleFinished"
          :is-round-changing="battleStateSync.isRoundChanging"
          :completed-rounds="Array.from(battleStateSync.completedRounds)"
        />
        <!-- 箱子展示（实时模式，等待状态使用静态网格） -->
        <BattleCaseDisplay
          :cases="casesToShow"
          :current-round="battleStateSync.currentRound"
          :total-rounds="battleStateSync.totalRounds"
          :opening-case-id="battleStateSync.openingCaseId || undefined"
          :is-battle-started="battleStateSync.isBattleStarted"
          :is-battle-finished="battleStateSync.isBattleFinished"
          :is-round-changing="battleStateSync.isRoundChanging"
          :completed-cases="Array.from(battleStateSync.completedCases)"
        />

        <!-- 如果正在计算，显示计算进度（仅在对战开始后） -->
        <CalculationProgress
          v-if="isBattleStarted && showCalculationProgress"
          :progress="calculationProgress"
          :custom-steps="calculationSteps"
        />
        <!-- 玩家展示 - 根据模式传递不同数据 -->
        <BattlePlayerDisplay
          :players="unifiedBattleData.bets || []"
          :current-round="battleStateSync.currentRound"
          :total-rounds="battleStateSync.totalRounds"
          :max-players="unifiedBattleData.max_joiner || unifiedBattleData.bets.length"
          :is-battle-started="battleStateSync.isBattleStarted"
          :is-battle-finished="battleStateSync.isBattleFinished"
          :host-uid="unifiedBattleData.user?.uid"
          :is-user-creator="isUserCreator"
          :is-user-joined="isUserJoined"
          :current-user-id="currentUserId"
          :opening-player-name="battleStateSync.openingPlayerName"
          :opening-player-index="battleStateSync.openingPlayerIndex ?? undefined"
          :current-case-items="[...currentCaseItems]"
          :steps="calculationSteps"
          :can-join="canJoinBattle"
          :is-round-changing="battleStateSync.isRoundChanging"
          :player-records="battleStateSync.playerRecords"
          :completed-rounds="Array.from(battleStateSync.completedRounds)"
          @join-battle="handleJoinBattle"
          @leave-battle="handleQuitBattle"
          @dismiss-battle="handleDismissBattle"
        />
      </div>

      <!-- 胜利者弹窗 -->
      <BattleWinnerModal
        v-if="canShowWinnerModal"
        :winner-data="winnerData"
        @close="handleCloseWinnerModal"
      />

      <!-- 测试动画组件（不依赖初始化状态） -->
      <div v-if="isDev" class="fixed top-4 left-4 z-50 bg-red-900/80 text-white p-2 rounded text-xs">
        <div>测试区域 - 强制渲染BattleCaseAnimation</div>
        <BattleCaseAnimation
          v-if="unifiedBattleData?.bets?.[0]"
          :case-detail="{}"
          :case-items="[]"
          :battle-player-index="0"
          :is-battle-mode="true"
          :battle-round="1"
          :total-rounds="1"
          :is-player-turn="false"
          :player-id="unifiedBattleData.bets[0].user?.uid"
          :player-name="unifiedBattleData.bets[0].user?.username || 'test'"
        />
      </div>

      <!-- 开发模式调试区域 -->
      <div v-if="isDev" class="fixed bottom-4 right-4 z-50 flex flex-col gap-2">
        <div class="bg-black/80 text-white p-3 rounded-lg text-xs max-w-xs">
          <div>页面初始化: {{ pageController.pageState.value.isInitialized ? '是' : '否' }}</div>
          <div>实时模式: {{ unifiedIsRealtimeMode ? '是' : '否' }}</div>
          <div>原始状态: {{ unifiedBattleData?.state }}</div>
          <div>对战状态: {{ isBattleStarted ? '已开始' : '未开始' }}</div>
          <div>对战完成: {{ isBattleFinished ? '是' : '否' }}</div>
          <div>玩家数量: {{ unifiedBattleData?.bets?.length || 0 }}</div>
          <div>当前轮次: {{ unifiedCurrentRound }}</div>
          <div>总轮次: {{ unifiedTotalRounds }}</div>
          <div>Case Items: {{ currentCaseItems.length }}</div>
        </div>
        <button
          @click="debugAnimationElements"
          class="px-4 py-2 bg-purple-600 text-white rounded-lg shadow-lg hover:bg-purple-700 transition-colors text-sm"
        >
          调试动画元素
        </button>
        <button
          @click="testAnimationTrigger"
          class="px-4 py-2 bg-green-600 text-white rounded-lg shadow-lg hover:bg-green-700 transition-colors text-sm"
        >
          测试动画触发
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 🎯 Vue核心导入
import { watch, onMounted, computed, ref } from "vue"
// 引入智能连接策略及相关composables和store
import { useBattlePageController } from "~/composables/useBattlePageController"
import { useBattleDetailPage } from "~/composables/useBattleDetailPage"
import { useBattleCore } from "~/composables/useBattleCore"
import { useUserStore } from "~/stores/user"

// 🎯 国际化设置
const { t } = useI18n()

// 🎯 路由参数
const route = useRoute()
const battleId = computed(() => route.params.id as string)

// 🎯 导入BattleStatus枚举
import { BattleStatus } from "~/services/battle-api"

// 🎯 导入组件
import CalculationProgress from "~/components/ui/CalculationProgress.vue"
import BattleWinnerModal from "~/components/battle/BattleWinnerModal.vue"
import BattleDetailSkeleton from "~/components/battle/BattleDetailSkeleton.vue"
// Battle 组件
import BattleConnectionStatus from "~/components/battle/BattleConnectionStatus.vue"
import BattleStaticModeIndicator from "~/components/battle/BattleStaticModeIndicator.vue"
import BattleHeader from "~/components/battle/BattleHeader.vue"
import BattleStateDisplay from "~/components/battle/BattleStateDisplay.vue"
import BattleCaseDisplay from "~/components/battle/BattleCaseDisplay.vue"
import BattlePlayerDisplay from "~/components/battle/BattlePlayerDisplay.vue"
import BattleCaseAnimation from "~/components/battle/BattleCaseAnimation.vue"

// 🎯 导入状态同步管理器
import { useBattleStateSync } from "~/composables/useBattleStateSync"

// 🎯 主要使用智能控制器
const pageController = useBattlePageController()
// 🎯 顶级调用所有 composables，避免在computed中调用
// 顶级使用 battleCore 和 pageData，可根据 unifiedIsRealtimeMode 使用对应数据
const battleCore = useBattleCore()
const pageData = useBattleDetailPage()

// 🎯 状态同步管理器
const {
  state: battleStateSync,
  updateBattleState,
  setOpeningState,
  addPlayerRecord,
  completeRound,
  switchToNextRound,
  finishBattle,
  resetBattleState,
  subscribeStateUpdate
} = useBattleStateSync()

// 🎯 动画调试
console.log('[🎰BATTLE-PAGE] 页面调用 useBattleAnimations')
const { debugRegisteredElements, startOpeningAnimation } = useBattleAnimations()
console.log('[🎰BATTLE-PAGE] 页面获得 useBattleAnimations 实例')

// 🎯 调试函数
const debugAnimationElements = () => {
  console.log('[🎰BATTLE-PAGE] 调试动画元素注册情况')
  const elements = debugRegisteredElements()
  console.log('注册的元素:', elements)

  // 检查页面中的BattleCaseAnimation组件
  const animationComponents = document.querySelectorAll('[data-battle-animation], .battle-case-animation')
  console.log('页面中的动画组件数量:', animationComponents.length)
  animationComponents.forEach((el, index) => {
    console.log(`动画组件 ${index}:`, el, {
      player: el.getAttribute('data-player'),
      playerId: el.getAttribute('data-player-id'),
      playerIndex: el.getAttribute('data-player-index')
    })
  })

  // 检查BattlePlayerCard组件
  const playerCards = document.querySelectorAll('.player-card, [class*="player-card"]')
  console.log('页面中的玩家卡片数量:', playerCards.length)

  // 检查动画区域
  const animationSections = document.querySelectorAll('.animation-section')
  console.log('页面中的动画区域数量:', animationSections.length)
  animationSections.forEach((el, index) => {
    console.log(`动画区域 ${index}:`, el, '是否可见:', (el as HTMLElement).offsetParent !== null)
  })

  return elements
}

// 🎯 测试动画触发
const testAnimationTrigger = () => {
  console.log('[🎰BATTLE-PAGE] 测试动画触发')

  // 首先检查注册的元素
  const elements = debugRegisteredElements()
  if (elements.caseElements.length === 0) {
    console.warn('[🎰BATTLE-PAGE] 没有注册的case元素，无法测试动画')
    alert('没有注册的动画元素，请确保对战页面已正确加载')
    return
  }

  // 模拟WebSocket消息
  const mockData = {
    animation_id: 'test-' + Date.now(),
    animation_start_timestamp: Date.now(),
    animation_duration: 8000,
    server_timestamp: Date.now(),
    participants: unifiedBattleData.value?.bets?.map((bet: any, index: number) => ({
      user: {
        username: bet.user?.username || bet.user?.profile?.nickname || `player-${index}`,
        uid: bet.user?.uid,
        nickname: bet.user?.profile?.nickname
      },
      animation_duration: 8000
    })) || []
  }

  console.log('[🎰BATTLE-PAGE] 模拟动画数据:', mockData)
  console.log('[🎰BATTLE-PAGE] 注册的元素:', elements)

  // 触发动画
  try {
    startOpeningAnimation(mockData)
    console.log('[🎰BATTLE-PAGE] 动画触发成功')
  } catch (error) {
    console.error('[🎰BATTLE-PAGE] 动画触发失败:', error)
  }
}

// 🎯 监听状态更新事件
subscribeStateUpdate((event) => {
  console.log('[🎰BATTLE-PAGE] 收到状态更新事件:', event)

  switch (event.type) {
    case 'opening_start':
      console.log('[🎰BATTLE-PAGE] 开箱开始:', event)
      break
    case 'opening_complete':
      console.log('[🎰BATTLE-PAGE] 开箱完成:', event)
      // 可以在这里触发动画或其他UI更新
      break
    case 'round_complete':
      console.log('[🎰BATTLE-PAGE] 轮次完成:', event)
      // 可以在这里处理轮次完成后的逻辑
      break
    case 'battle_end':
      console.log('[🎰BATTLE-PAGE] 对战结束:', event)
      // 可以在这里处理对战结束后的逻辑
      break
  }
})

// 🎯 创建统一的计算属性，避免双重数据源
const unifiedBattleData = computed(() => pageController.battleData.value)
const unifiedCurrentRound = computed(() => pageController.currentRound.value)
const unifiedTotalRounds = computed(() => pageController.totalRounds.value)
const unifiedIsRealtimeMode = computed(() => pageController.isRealtimeMode.value)


// 连接状态封装，用于 BattleConnectionStatus
const connectionState = computed(() => {
  return unifiedIsRealtimeMode.value
    ? battleCore.battleWebSocket.connectionState.value
    : { isConnected: false, isReconnecting: false, reconnectAttempts: 0 }
})
// 重试连接处理
const handleRetryConnection = () => {
  if (unifiedIsRealtimeMode.value) {
    battleCore.initialize()
  }
}


// 保留获取状态字符串函数
const getBattleStateString = (state: number): string => {
  // 未开始/准备阶段
  if ([2,3,4].includes(state)) return 'waiting'
  switch (state) {
    case 1: return 'loading'
    case 5: return 'battle'
    case 11: return 'completed'
    case 20: return 'cancelled'
    default: return 'unknown'
  }
}


// 🎯 新增统一计算属性和事件处理
// 用户身份和状态
const isUserCreator = computed(() => {
  const user = useUserStore()
  return unifiedBattleData.value?.user?.uid === user.userId
})
const isUserJoined = computed(() => {
  const user = useUserStore()
  const bets = unifiedBattleData.value?.bets || []
  return bets.some((bet: any) => bet.user?.uid === user.userId)
})
// 当前用户ID
const currentUserId = computed(() => useUserStore().userId)
const isBattleStarted = computed(() => {
  const state = unifiedBattleData.value?.state
  return state !== undefined && state >= 5
})
const isBattleFinished = computed(() => {
  const state = unifiedBattleData.value?.state
  return state === 11 || state === 20
})
const isBattleInProgress = computed(() => {
  const state = unifiedBattleData.value?.state
  return state !== undefined && [5,6,7,8,9,10].includes(state!)
})
const battleStateString = computed(() => getBattleStateString(unifiedBattleData.value?.state))

// 数据流相关
const roundResults = computed(() => {
  if (unifiedIsRealtimeMode.value) {
    // 实时模式：从 pageData 获取动态结果并打印调试信息
    const results = pageData.state.roundResults.value || []
    console.log('[🎯轮次结果] 实时模式结果数据:', {
      resultsCount: results.length,
      results: results.map(r => ({
        round: r.round,
        playerIndex: r.playerIndex,
        itemName: r.item?.name,
        itemPrice: r.item?.price
      }))
    })
    return results
  } else {
    // 静态模式：重构 API 数据
    const processStaticRoundResults = (battleData: any) => {
      if (!battleData || !battleData.bets) {
        console.warn('[🎯轮次结果] 静态模式：对战数据不完整')
        return []
      }
      const allResults: any[] = []
      battleData.bets.forEach((bet: any, playerIndex: number) => {
        if (!bet.open_items || !Array.isArray(bet.open_items)) {
          console.warn(`[🎯轮次结果] 玩家${playerIndex}没有开箱记录`)
          return
        }
        bet.open_items.forEach((item: any, roundIndex: number) => {
          const round = roundIndex + 1
          const processedItem = {
            ...item,
            round,
            item_id: item.item_id || item.id || `static_${playerIndex}_${round}_${Date.now()}`,
            name: item.name || '未知物品',
            price: item.price || item.item_price?.price || 0,
            rarity_name: item.rarity_name || item.rarity || 'common',
            image: item.image || item.icon_url || '',
            isStatic: true,
            timestamp: item.timestamp || battleData.update_time
          }
          allResults.push({
            round,
            playerIndex,
            item: processedItem,
            user: bet.user,
            resultId: `static_${playerIndex}_${round}`,
            battleId: battleData.id,
            isComplete: true,
            displayOrder: round * 100 + playerIndex
          })
        })
      })
      allResults.sort((a, b) => a.displayOrder - b.displayOrder)
      console.log('[🎯轮次结果] 静态结果处理完成:', {
        totalResults: allResults.length,
        roundsWithResults: [...new Set(allResults.map(r => r.round))],
        playersWithResults: [...new Set(allResults.map(r => r.playerIndex))]
      })
      return allResults
    }
    const staticResults = processStaticRoundResults(unifiedBattleData.value)
    console.log('[🎯轮次结果] 静态模式结果数据:', {
      resultsCount: staticResults.length,
      totalRounds: unifiedTotalRounds.value,
      playersCount: unifiedBattleData.value?.bets?.length || 0
    })
    return staticResults
  }
})

// 🎯 修复3：添加轮次结果显示状态管理
const roundResultsDisplay = computed(() => {
  const results = roundResults.value
  const totalRounds = unifiedTotalRounds.value
  const currentRound = unifiedCurrentRound.value
  const resultsByRound: Record<number, any[]> = {}
  results.forEach(r => {
    resultsByRound[r.round] = resultsByRound[r.round] || []
    resultsByRound[r.round].push(r)
  })
  const roundsDisplay: any[] = []
  for (let i = 1; i <= totalRounds; i++) {
    const list = resultsByRound[i] || []
    roundsDisplay.push({
      round: i,
      results: list,
      isCurrentRound: i === currentRound,
      isCompleted: list.length > 0,
      isVisible: unifiedIsRealtimeMode.value ? i <= currentRound : true,
      playerCount: list.length,
      totalValue: list.reduce((sum, r) => sum + (r.item?.price || 0), 0)
    })
  }
  return {
    byRound: resultsByRound,
    display: roundsDisplay,
    totalResults: results.length,
    completedRounds: roundsDisplay.filter(r => r.isCompleted).length
  }
})

// 🎯 修复4：添加轮次完成检测和通知机制
const lastCompletedRound = ref(0)
watch(
  () => roundResultsDisplay.value.completedRounds,
  (newCount, oldCount) => {
    if (newCount > oldCount && unifiedIsRealtimeMode.value) {
      console.log('[🎯轮次结果] 检测到新的轮次完成:', { newCount, oldCount, current: unifiedCurrentRound.value })
      handleRoundCompleted(newCount)
    }
  }
)

// 🎯 修复5：轮次完成处理函数
function handleRoundCompleted(round: number) {
  console.log(`[🎯轮次结果] 第${round}轮完成，显示结果`)
  const roundData = roundResultsDisplay.value.byRound[round] || []
  if (!roundData.length) {
    console.warn(`[🎯轮次结果] 第${round}轮没有结果数据`)
    return
  }
  window.dispatchEvent(new CustomEvent('battle:round_completed', {
    detail: {
      round,
      results: roundData,
      totalValue: roundData.reduce((sum, r) => sum + (r.item?.price || 0), 0),
      playerResults: roundData.map(r => ({ playerIndex: r.playerIndex, playerName: r.user?.nickname || `玩家${r.playerIndex+1}`, item: r.item, value: r.item?.price || 0 })),
      timestamp: Date.now(),
      battleId: unifiedBattleData.value?.id,
      isAllRoundsComplete: round === unifiedTotalRounds.value
    }
  }))
  lastCompletedRound.value = Math.max(lastCompletedRound.value, round)
  if (round === unifiedTotalRounds.value) {
    handleBattleCompleted()
  }
}

// 🎯 修复6：对战完成处理
function handleBattleCompleted() {
  console.log('[🎯轮次结果] 对战完成，计算最终胜利者')
  setTimeout(() => {
    pageController.handleBattleAction('calculate-winner')
  }, 1000)
}

// 🎯 修复7：为调试面板添加轮次结果信息
const debugRoundResults = computed(() => {
  const d = roundResultsDisplay.value
  return {
    总结果数: d.totalResults,
    已完成轮次数: d.completedRounds,
    各轮次结果数: d.display.map(r => `R${r.round}:${r.playerCount}`).join(' ')
  }
})
// 🎯 原始数据流与交互计算属性和事件处理（恢复模板绑定）
// 案例条目数据
const currentCaseItems = computed(() =>
  unifiedIsRealtimeMode.value
    ? pageData.caseItems.currentCaseItems.value
    : pageController.currentCaseItems?.value || []
)
// 显示的箱子列表，根据模式区分
const displayCases = computed(() =>
  unifiedIsRealtimeMode.value
    ? battleCore.battleState.displayCases.value || []
    : pageController.displayCases?.value || []
)
// 静态模式下的网格展示案例
const staticDisplayCases = computed(() => {
  const rounds = unifiedBattleData.value?.rounds || []
  return rounds.map((r: any) => ({
    id: r.case.case_key,
    key: r.case.case_key,
    case_key: r.case.case_key,
    name: r.case.name,
    name_en: r.case.name_en,
    name_zh_hans: r.case.name_zh_hans,
    cover: r.case.cover,
    price: r.case.price,
    count: 1
  }))
})

// 🎯 等待状态下实时模式使用静态箱子网格
const casesToShow = computed(() => {
  if (unifiedIsRealtimeMode.value && !isBattleStarted.value) {
    return staticDisplayCases.value
  }
  return displayCases.value
})

// 当前开箱案例 ID
const openingCaseId = computed(() =>
  unifiedIsRealtimeMode.value
    ? battleCore.battleState.openingCaseId.value || undefined
    : undefined
)
// 当前开箱玩家名称
const openingPlayerName = computed(() =>
  unifiedIsRealtimeMode.value
    ? battleCore.battleState.openingPlayerName.value || ''
    : ''
)
// 当前开箱玩家索引
const openingPlayerIndex = computed(() =>
  unifiedIsRealtimeMode.value
    ? battleCore.battleState.openingPlayerIndex.value ?? undefined
    : undefined
)
// 是否显示计算进度
const showCalculationProgress = computed(() =>
  unifiedIsRealtimeMode.value
    ? pageData.state.showCalculationProgress.value
    : false
)
// 计算进度值
const calculationProgress = computed(() =>
  unifiedIsRealtimeMode.value
    ? pageData.state.calculationProgress.value
    : 0
)
// 计算步骤列表
const calculationSteps = computed(() =>
  unifiedIsRealtimeMode.value
    ? pageData.state.calculationSteps.value
    : []
)
// 弹窗与结果
const canShowWinnerModal = computed(() => pageController.canShowWinnerModal?.value || false)
const winnerData = computed(() => pageController.winnerData?.value)

// 🎯 计算是否允许加入对战
const canJoinBattle = computed(() => {
  // 如果用户已加入或是对战创建者，不允许加入
  if (isUserJoined.value || isUserCreator.value) {
    return false
  }
  
  // 如果对战已开始或已结束，不允许加入
  if (isBattleStarted.value || isBattleFinished.value) {
    return false
  }
  
  // 如果房间已满，不允许加入
  const currentPlayerCount = unifiedBattleData.value?.bets?.length || 0
  const maxPlayers = unifiedBattleData.value?.max_joiner || 4
  if (currentPlayerCount >= maxPlayers) {
    return false
  }
  
  // 如果对战状态不是等待状态，不允许加入
  const battleState = unifiedBattleData.value?.state
  if (battleState !== 2) { // 2 = WAITING
    return false
  }
  
  return true
})

// 统一事件处理
const handleJoinBattle = () => pageController.handleBattleAction('join-battle')
const handleStartBattle = () => pageController.handleBattleAction('start-battle')
const handleCloseWinnerModal = () => pageController.handleBattleAction('close-winner-modal')
const handleQuitBattle = () => {
  console.log('[🎰BATTLE-PAGE] 收到退出对战事件，调用pageController')
  pageController.handleBattleAction('quit-battle')
}
const handleDismissBattle = () => {
  console.log('[🎰BATTLE-PAGE] 收到解散房间事件，调用pageController')
  pageController.handleBattleAction('dismiss-battle')
}
// 开发模式检测
const isDev = import.meta.dev

// 🎯 状态同步初始化
const initializeBattleStateSync = () => {
  console.log('[🎰BATTLE-PAGE] 初始化状态同步管理器')

  // 初始化状态
  updateBattleState({
    currentRound: unifiedCurrentRound.value,
    totalRounds: unifiedTotalRounds.value,
    isBattleStarted: isBattleStarted.value,
    isBattleFinished: isBattleFinished.value
  })

  // 设置开箱状态
  if (openingCaseId.value) {
    setOpeningState(
      openingCaseId.value,
      openingPlayerIndex.value ?? null,
      openingPlayerName.value
    )
  }

  console.log('[🎰BATTLE-PAGE] 状态同步管理器初始化完成')
}

// 🎯 监听状态变化并同步到状态管理器
watch([unifiedCurrentRound, unifiedTotalRounds, isBattleStarted, isBattleFinished],
  ([currentRound, totalRounds, battleStarted, battleFinished]) => {
    console.log('[🎰BATTLE-PAGE] 检测到状态变化，同步到状态管理器:', {
      currentRound, totalRounds, battleStarted, battleFinished
    })

    updateBattleState({
      currentRound,
      totalRounds,
      isBattleStarted: battleStarted,
      isBattleFinished: battleFinished
    })
  }
)

// 🎯 监听开箱状态变化
watch([openingCaseId, openingPlayerIndex, openingPlayerName],
  ([caseId, playerIndex, playerName]) => {
    if (caseId) {
      console.log('[🎰BATTLE-PAGE] 检测到开箱状态变化:', { caseId, playerIndex, playerName })
      setOpeningState(
        caseId,
        playerIndex ?? null,
        playerName
      )
    }
  }
)

// 🎯 监听当前轮次物品变化，用于记录玩家开箱结果
watch(currentCaseItems, (newItems, oldItems) => {
  if (!newItems || newItems.length === 0) return

  // 检查是否有新的物品添加
  const newItemsCount = newItems.length
  const oldItemsCount = oldItems?.length || 0

  if (newItemsCount > oldItemsCount) {
    // 有新物品，记录到对应玩家
    const newItem = newItems[newItemsCount - 1]
    const currentPlayerIndex = openingPlayerIndex.value

    if (newItem && currentPlayerIndex !== null && currentPlayerIndex !== undefined) {
      const players = unifiedBattleData.value?.bets || []
      const currentPlayer = players[currentPlayerIndex]

      if (currentPlayer) {
        console.log('[🎰BATTLE-PAGE] 记录玩家开箱结果:', {
          playerId: currentPlayer.user.uid,
          playerName: currentPlayer.user.name,
          item: newItem,
          round: battleStateSync.currentRound
        })

        // 添加到状态管理器
        addPlayerRecord(currentPlayer.user.uid, newItem, battleStateSync.currentRound - 1)
      }
    }
  }
}, { deep: true })

// 🎯 监听轮次变化，自动推进状态
watch([unifiedCurrentRound, unifiedTotalRounds], ([currentRound, totalRounds]) => {
  console.log('[🎰BATTLE-PAGE] 轮次变化检测:', { currentRound, totalRounds })

  // 如果轮次发生变化，检查是否需要完成上一轮
  if (currentRound > battleStateSync.currentRound) {
    const previousRound = battleStateSync.currentRound
    console.log('[🎰BATTLE-PAGE] 检测到轮次推进，完成轮次:', previousRound)

    // 完成上一轮
    if (previousRound > 0) {
      completeRound(previousRound, battleStateSync.openingCaseId || undefined)
    }

    // 如果对战已结束
    if (currentRound > totalRounds && isBattleFinished.value) {
      console.log('[🎰BATTLE-PAGE] 对战已结束，触发结束状态')
      finishBattle()
    }
  }
})

// 🎯 监听对战结束状态
watch(isBattleFinished, (isFinished) => {
  if (isFinished && !battleStateSync.isBattleFinished) {
    console.log('[🎰BATTLE-PAGE] 对战结束，更新状态管理器')
    finishBattle()
  }
})

// 🎯 页面初始化
onMounted(async () => {
  console.log('[🎰BATTLE-PAGE] 页面挂载，初始化智能连接策略')

  if (battleId.value) {
    try {
      await pageController.initializeBattleMode(battleId.value)
      console.log('[🎰BATTLE-PAGE] 智能连接策略初始化完成:', {
        mode: pageController.isRealtimeMode.value ? 'Realtime' : 'Static',
        battleState: pageController.battleData.value?.state,
        totalRounds: pageController.totalRounds.value,
        apiRoundCount: pageController.battleData.value?.round_count
      })

      // 初始化状态同步
      await nextTick()
      initializeBattleStateSync()

    } catch (error) {
      console.error('[🎰BATTLE-PAGE] 智能连接策略初始化失败:', error)
    }
  }
})

// 🎯 调试：监听showWinnerModal变化
watch(() => pageController.canShowWinnerModal.value, (newValue) => {
  console.log('[🎰BATTLE-PAGE] canShowWinnerModal变化:', newValue)
})

// 🎯 页面元数据
useHead({
  title: computed(() => {
    const battleData = unifiedBattleData.value
    return battleData ? `${t('battle.detail')} - ${battleData.name || battleData.id}` : t('battle.detail')
  })
})
</script>

<style scoped>
.battle-detail-page {
  min-height: 100vh;
  /* background: linear-gradient(135deg, #0f1419 0%, #1a1f2e 50%, #0f1419 100%); */
  display: flex;
  flex-direction: column;
  color: white;
  position: relative;
}

/* 内容区域样式 */
.battle-content {
  flex: 1;
  overflow-y: auto;
  padding: 1rem 0;
}

.content-container {
  /* max-width: 1200px; */
  margin: 0 auto;
  /* padding: 0 1rem; */
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* 加载和错误状态样式 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 50vh;
}

.loading-content {
  text-align: center;
}

.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 50vh;
  padding: 2rem;
}

.error-content {
  text-align: center;
  max-width: 400px;
  padding: 2rem;
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.2);
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .battle-content {
    padding: 0.5rem 0;
  }
  
  .content-container {
    padding: 0 0.75rem;
    gap: 1rem;
  }
}


/* 返回按钮样式 */
.fixed .bg-gray-800\/90 {
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.fixed .bg-gray-800\/90 a {
  transition: all 0.3s ease;
  border-radius: 0.5rem;
}

.fixed .bg-gray-800\/90 a:hover {
  background: linear-gradient(
    135deg,
    rgba(0, 168, 255, 0.1) 0%,
    rgba(255, 105, 180, 0.1) 100%
  );
  transform: translateY(-2px);
  box-shadow: 
    0 0.5rem 1rem rgba(0, 0, 0, 0.3),
    0 0 0.5rem rgba(0, 168, 255, 0.2);
}

.fixed .bg-gray-800\/90 a:active {
  transform: translateY(0);
}
</style>
