<template>
  <div class="min-h-screen bg-gray-900 text-white">
    <div class="container mx-auto px-4 py-8">
      <div class="flex justify-between items-center mb-8">
        <h1 class="text-3xl font-bold">{{ $t('nav.profile') }}</h1>
        <button 
          @click="handleLogout"
          class="bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-lg transition-colors flex items-center gap-2"
        >
          <div class="i-ph-sign-out w-4 h-4"></div>
          {{ $t('auth.logout') }}
        </button>
      </div>
      
      <!-- 用户信息卡片 -->
      <div class="bg-gray-800 rounded-lg p-6 mb-8">
        <div class="flex items-center space-x-4">
          <div class="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
            <img 
              v-if="userStore.userAvatar" 
              :src="userStore.userAvatar" 
              :alt="userStore.userNickname"
              class="w-full h-full rounded-full object-cover"
            />
            <span v-else class="text-xl font-bold">{{ userStore.userNickname.charAt(0).toUpperCase() }}</span>
          </div>
          <div>
            <h2 class="text-xl font-semibold">{{ userStore.userNickname }}</h2>
            <p class="text-gray-400">{{ userStore.user?.email }}</p>
            <div class="flex items-center gap-2 mt-1">
              <span v-if="userStore.isVip" class="bg-yellow-500 text-black text-xs px-2 py-1 rounded">VIP</span>
              <span v-if="userStore.isAgent" class="bg-green-500 text-white text-xs px-2 py-1 rounded">代理</span>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 统计信息 -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="bg-gray-800 rounded-lg p-6">
          <h3 class="text-lg font-semibold mb-2">余额</h3>
          <p class="text-2xl font-bold text-green-400">${{ Number(userStore.userBalance).toFixed(2) }}</p>
        </div>
        <div class="bg-gray-800 rounded-lg p-6">
          <h3 class="text-lg font-semibold mb-2">等级</h3>
          <p class="text-2xl font-bold text-blue-400">Level {{ userStore.userLevel }}</p>
        </div>
        <div class="bg-gray-800 rounded-lg p-6">
          <h3 class="text-lg font-semibold mb-2">积分</h3>
          <p class="text-2xl font-bold text-purple-400">{{ userStore.userPoints }}</p>
        </div>
        <div class="bg-gray-800 rounded-lg p-6">
          <h3 class="text-lg font-semibold mb-2">活跃积分</h3>
          <p class="text-2xl font-bold text-orange-400">{{ userStore.userActivePoints }}</p>
        </div>
      </div>

      <!-- 账户信息 -->
      <div class="bg-gray-800 rounded-lg p-6 mb-8">
        <h3 class="text-lg font-semibold mb-4">账户信息</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div>
            <span class="text-gray-400">注册时间:</span>
            <span class="ml-2">{{ formatDate(userStore.user?.date_joined) }}</span>
          </div>
          <div>
            <span class="text-gray-400">最近登录:</span>
            <span class="ml-2">{{ formatDate(userStore.user?.login_time) }}</span>
          </div>
          <div v-if="userStore.user?.login_ip">
            <span class="text-gray-400">登录IP:</span>
            <span class="ml-2">{{ userStore.user?.login_ip }}</span>
          </div>
          <div v-if="userStore.steamTradeUrl">
            <span class="text-gray-400">Steam交易链接:</span>
            <span class="ml-2 text-blue-400">已设置</span>
          </div>
        </div>
      </div>
      
      <!-- 功能按钮 -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <button class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded-lg transition-colors">
          充值余额
        </button>
        <button class="bg-green-600 hover:bg-green-700 text-white font-bold py-3 px-6 rounded-lg transition-colors">
          设置交易链接
        </button>
        <button 
          @click="showChangePassword = true"
          class="bg-purple-600 hover:bg-purple-700 text-white font-bold py-3 px-6 rounded-lg transition-colors"
        >
          修改密码
        </button>
      </div>
    </div>

    <!-- 修改密码模态框 -->
    <div v-if="showChangePassword" class="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
      <div class="bg-gray-800 rounded-lg p-6 w-full max-w-md">
        <h3 class="text-lg font-semibold mb-4">{{ $t('auth.change_password') }}</h3>
        
        <form @submit.prevent="handleChangePassword" class="space-y-4">
          <!-- 当前密码 -->
          <div>
            <label class="block text-sm font-medium text-white/80 mb-1">
              {{ $t('auth.old_password') }}
            </label>
            <input 
              type="password" 
              v-model="passwordForm.oldPassword"
              class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:outline-none focus:ring-1 focus:ring-blue-500"
              :placeholder="$t('auth.old_password_placeholder')"
            />
          </div>

          <!-- 新密码 -->
          <div>
            <label class="block text-sm font-medium text-white/80 mb-1">
              {{ $t('auth.new_password') }}
            </label>
            <input 
              type="password" 
              v-model="passwordForm.newPassword"
              class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:outline-none focus:ring-1 focus:ring-blue-500"
              :placeholder="$t('auth.new_password_placeholder')"
            />
          </div>

          <!-- 确认新密码 -->
          <div>
            <label class="block text-sm font-medium text-white/80 mb-1">
              {{ $t('auth.confirm_password') }}
            </label>
            <input 
              type="password" 
              v-model="passwordForm.confirmPassword"
              class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:outline-none focus:ring-1 focus:ring-blue-500"
              :placeholder="$t('auth.confirm_password_placeholder')"
            />
          </div>

          <!-- 错误信息 -->
          <div v-if="passwordError" class="p-3 bg-red-500/10 border border-red-500/20 rounded-lg">
            <p class="text-sm text-red-400">{{ passwordError }}</p>
          </div>

          <!-- 按钮 -->
          <div class="flex gap-3">
            <button 
              type="button" 
              @click="showChangePassword = false"
              class="flex-1 bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded-lg transition-colors"
            >
              取消
            </button>
            <button 
              type="submit" 
              :disabled="isChangingPassword"
              class="flex-1 bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors disabled:opacity-50 flex items-center justify-center"
            >
              <div v-if="isChangingPassword" class="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full mr-2"></div>
              {{ isChangingPassword ? $t('auth.changing') : $t('auth.change_password') }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useUserStore } from '~/stores/user'
import { useAuth } from '~/composables/useAuth'
import { validateResetPasswordForm } from '~/utils/validation'

// 设置页面元信息
definePageMeta({
  middleware: 'auth'
})

// 设置页面标题
useHead({
  title: '个人中心 - CSGOSKINS',
  meta: [
    { name: 'description', content: '管理您的CSGOSKINS账户，查看余额、等级和个人信息' },
    { name: 'keywords', content: 'CSGOSKINS个人中心,用户资料,账户管理,余额查询' }
  ]
})

const userStore = useUserStore()
const { logout, changePassword } = useAuth()
const router = useRouter()

// 修改密码相关状态
const showChangePassword = ref(false)
const isChangingPassword = ref(false)
const passwordError = ref('')

const passwordForm = reactive({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// 格式化日期
const formatDate = (dateString: string) => {
  if (!dateString) return '未知'
  
  try {
    const date = new Date(dateString)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  } catch {
    return '未知'
  }
}

// 处理登出
const handleLogout = async () => {
  await logout()
  await router.push('/')
}

// 处理修改密码
const handleChangePassword = async () => {
  passwordError.value = ''
  
  // 验证表单
  const validation = validateResetPasswordForm(
    '', // 邮箱不需要验证
    '', // 验证码不需要验证
    passwordForm.newPassword,
    passwordForm.confirmPassword
  )
  
  if (!passwordForm.oldPassword) {
    passwordError.value = '请输入当前密码'
    return
  }
  
  if (!validation.password.valid) {
    passwordError.value = validation.password.message || '新密码格式不正确'
    return
  }
  
  if (!validation.confirmPassword.valid) {
    passwordError.value = validation.confirmPassword.message || '两次密码不一致'
    return
  }
  
  isChangingPassword.value = true
  
  try {
    const result = await changePassword({
      oldPassword: passwordForm.oldPassword,
      newPassword: passwordForm.newPassword,
      confirmPassword: passwordForm.confirmPassword
    })
    
    if (result.success) {
      showChangePassword.value = false
      // 重置表单
      passwordForm.oldPassword = ''
      passwordForm.newPassword = ''
      passwordForm.confirmPassword = ''
      
      // 可以显示成功消息
      alert('密码修改成功')
    } else {
      passwordError.value = result.error || '密码修改失败'
    }
  } catch (error: any) {
    passwordError.value = error.message || '密码修改失败'
  } finally {
    isChangingPassword.value = false
  }
}

// 页面加载时初始化用户信息
onMounted(() => {
  userStore.initUserState()
})
</script>

<style scoped>
section {
  margin-bottom: 2rem;
}

.space-y-2 > * + * {
  margin-top: 0.5rem;
}
</style>
