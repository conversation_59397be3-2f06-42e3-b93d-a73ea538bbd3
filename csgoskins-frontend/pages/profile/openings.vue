<template>
  <div class="min-h-screen relative">
    <!-- 背景装饰 -->
    <div class="fixed inset-0 -z-10">
      <div class="absolute top-0 -left-32 w-64 h-64 rounded-full blur-3xl bg-primary/10 animate-pulse-slow"></div>
      <div class="absolute bottom-0 -right-32 w-64 h-64 rounded-full blur-3xl bg-secondary/10 animate-pulse-slow"></div>
      <div class="absolute inset-0 bg-grid-pattern opacity-20"></div>
      <div class="absolute inset-0 bg-noise opacity-40 mix-blend-soft-light"></div>
    </div>

    <!-- PC端内容 -->
    <div class="container mx-auto px-4 py-8 relative z-10" v-if="!isMobile">
      <!-- 页面头部 -->
      <div class="page-header text-center mb-12">
        <h1 class="text-4xl font-bold mb-4">
          <span class="text-gradient">{{ $t('openings.title') }}</span>
        </h1>
        <p class="text-gray-300 max-w-2xl mx-auto">{{ $t('openings.description') }}</p>
        
        <!-- 筛选选项 -->
        <div class="filter-options mt-8 flex justify-center space-x-4">
          <button 
            v-for="filter in filterOptions" 
            :key="filter.value"
            @click="currentFilter = filter.value"
            class="filter-btn"
            :class="{ 'active': currentFilter === filter.value }"
          >
            {{ $t(filter.label) }}
          </button>
        </div>
      </div>

      <!-- 统计信息 -->
      <div class="stats-section mb-8">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div class="stat-card">
            <div class="stat-icon">
              <i class="i-heroicons-gift text-2xl text-primary"></i>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ totalOpenings }}</div>
              <div class="stat-label">{{ $t('openings.total_openings') }}</div>
            </div>
          </div>
          <div class="stat-card">
            <div class="stat-icon">
              <i class="i-heroicons-star text-2xl text-yellow-400"></i>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ rareItems }}</div>
              <div class="stat-label">{{ $t('openings.rare_items') }}</div>
            </div>
          </div>
          <div class="stat-card">
            <div class="stat-icon">
              <i class="i-heroicons-trophy text-2xl text-orange-400"></i>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ legendaryItems }}</div>
              <div class="stat-label">{{ $t('openings.legendary_items') }}</div>
            </div>
          </div>
          <div class="stat-card">
            <div class="stat-icon">
              <i class="i-heroicons-currency-dollar text-2xl text-green-400"></i>
            </div>
            <div class="stat-content">
              <div class="stat-value">${{ totalValue.toFixed(2) }}</div>
              <div class="stat-label">{{ $t('openings.total_value') }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 开箱记录列表 -->
      <div class="openings-content">
        <div class="openings-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div
            v-for="opening in filteredOpenings"
            :key="opening.id"
            class="opening-card"
            :class="opening.rarityClass"
          >
            <div class="opening-image">
              <img :src="opening.item.image" :alt="opening.item.name" />
              <div class="opening-rarity-badge" :class="opening.rarityClass">
                {{ opening.rarity }}
              </div>
            </div>
            <div class="opening-info">
              <div class="opening-item-name">{{ opening.item.name }}</div>
              <div class="opening-details">
                <div class="opening-case">{{ opening.caseName }}</div>
                <div class="opening-time">{{ formatTime(opening.time) }}</div>
              </div>
              <div class="opening-value" v-if="opening.value">
                ${{ opening.value.toFixed(2) }}
              </div>
            </div>
          </div>
        </div>
        
        <!-- 分页 -->
        <div class="pagination-section mt-8">
          <div class="flex justify-center space-x-2">
            <button 
              @click="currentPage--"
              :disabled="currentPage === 1"
              class="pagination-btn"
              :class="{ 'disabled': currentPage === 1 }"
            >
              <i class="i-heroicons-chevron-left w-4 h-4"></i>
            </button>
            <button 
              v-for="page in visiblePages"
              :key="page"
              @click="currentPage = page"
              class="pagination-btn"
              :class="{ 'active': currentPage === page }"
            >
              {{ page }}
            </button>
            <button 
              @click="currentPage++"
              :disabled="currentPage === totalPages"
              class="pagination-btn"
              :class="{ 'disabled': currentPage === totalPages }"
            >
              <i class="i-heroicons-chevron-right w-4 h-4"></i>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 移动端内容 -->
    <div class="container mx-auto px-4 py-4 relative z-10" v-else>
      <!-- 页面头部 -->
      <div class="page-header mb-6">
        <h1 class="text-2xl font-bold mb-2">{{ $t('openings.title') }}</h1>
        <p class="text-gray-300 text-sm">{{ $t('openings.description') }}</p>
        
        <!-- 筛选选项 -->
        <div class="filter-options mt-4 flex space-x-2 overflow-x-auto pb-2">
          <button 
            v-for="filter in filterOptions" 
            :key="filter.value"
            @click="currentFilter = filter.value"
            class="filter-btn-mobile whitespace-nowrap"
            :class="{ 'active': currentFilter === filter.value }"
          >
            {{ $t(filter.label) }}
          </button>
        </div>
      </div>

      <!-- 统计信息 -->
      <div class="stats-section mb-6">
        <div class="grid grid-cols-2 gap-3">
          <div class="stat-card-mobile">
            <div class="stat-icon">
              <i class="i-heroicons-gift text-lg text-primary"></i>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ totalOpenings }}</div>
              <div class="stat-label">{{ $t('openings.total_openings') }}</div>
            </div>
          </div>
          <div class="stat-card-mobile">
            <div class="stat-icon">
              <i class="i-heroicons-star text-lg text-yellow-400"></i>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ rareItems }}</div>
              <div class="stat-label">{{ $t('openings.rare_items') }}</div>
            </div>
          </div>
          <div class="stat-card-mobile">
            <div class="stat-icon">
              <i class="i-heroicons-trophy text-lg text-orange-400"></i>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ legendaryItems }}</div>
              <div class="stat-label">{{ $t('openings.legendary_items') }}</div>
            </div>
          </div>
          <div class="stat-card-mobile">
            <div class="stat-icon">
              <i class="i-heroicons-currency-dollar text-lg text-green-400"></i>
            </div>
            <div class="stat-content">
              <div class="stat-value">${{ totalValue.toFixed(2) }}</div>
              <div class="stat-label">{{ $t('openings.total_value') }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 开箱记录列表 -->
      <div class="openings-content">
        <div class="openings-list space-y-3">
          <div
            v-for="opening in filteredOpenings"
            :key="opening.id"
            class="opening-item-mobile"
            :class="opening.rarityClass"
          >
            <div class="opening-image">
              <img :src="opening.item.image" :alt="opening.item.name" />
              <div class="opening-rarity-badge" :class="opening.rarityClass">
                {{ opening.rarity }}
              </div>
            </div>
            <div class="opening-info">
              <div class="opening-item-name">{{ opening.item.name }}</div>
              <div class="opening-details">
                <div class="opening-case">{{ opening.caseName }}</div>
                <div class="opening-time">{{ formatTime(opening.time) }}</div>
              </div>
              <div class="opening-value" v-if="opening.value">
                ${{ opening.value.toFixed(2) }}
              </div>
            </div>
          </div>
        </div>
        
        <!-- 分页 -->
        <div class="pagination-section mt-6">
          <div class="flex justify-center space-x-2">
            <button 
              @click="currentPage--"
              :disabled="currentPage === 1"
              class="pagination-btn-mobile"
              :class="{ 'disabled': currentPage === 1 }"
            >
              <i class="i-heroicons-chevron-left w-4 h-4"></i>
            </button>
            <span class="pagination-info">
              {{ currentPage }} / {{ totalPages }}
            </span>
            <button 
              @click="currentPage++"
              :disabled="currentPage === totalPages"
              class="pagination-btn-mobile"
              :class="{ 'disabled': currentPage === totalPages }"
            >
              <i class="i-heroicons-chevron-right w-4 h-4"></i>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 国际化
const { t } = useI18n()

// 设备检测 - 使用响应式状态
const isMobile = ref(false)

// 在客户端检测设备类型
onMounted(() => {
  const checkMobile = () => {
    isMobile.value = window.innerWidth < 768
  }
  
  checkMobile()
  window.addEventListener('resize', checkMobile)
  
  onUnmounted(() => {
    window.removeEventListener('resize', checkMobile)
  })
})

// SEO配置
useSeoMeta({
  title: '开箱记录 - CSGO开箱网站',
  description: '查看您的开箱历史记录、统计数据和获得的物品',
  keywords: '开箱记录,开箱历史,物品统计,CSGO开箱',
  ogTitle: '开箱记录 - CSGO开箱网站',
  ogDescription: '查看您的开箱历史记录、统计数据和获得的物品',
  twitterTitle: '开箱记录 - CSGO开箱网站',
  twitterDescription: '查看您的开箱历史记录、统计数据和获得的物品'
})

// 筛选选项
const filterOptions = [
  { label: 'openings.filters.all', value: 'all' },
  { label: 'openings.filters.rare', value: 'rare' },
  { label: 'openings.filters.epic', value: 'epic' },
  { label: 'openings.filters.legendary', value: 'legendary' }
]

// 当前筛选
const currentFilter = ref('all')

// 分页
const currentPage = ref(1)
const itemsPerPage = 12

// 模拟开箱数据
const openings = ref([
  {
    id: 1,
    item: { name: 'AK-47 | 血腥运动', image: '/demo/item1.png' },
    caseName: '武器箱 #1',
    time: '2024-01-20T14:30:00Z',
    rarity: '稀有',
    rarityClass: 'rarity-rare',
    value: 25.50
  },
  {
    id: 2,
    item: { name: 'M4A4 | 龙王', image: '/demo/item2.png' },
    caseName: '武器箱 #2',
    time: '2024-01-20T13:15:00Z',
    rarity: '史诗',
    rarityClass: 'rarity-epic',
    value: 45.80
  },
  {
    id: 3,
    item: { name: 'AWP | 龙王', image: '/demo/item3.png' },
    caseName: '武器箱 #3',
    time: '2024-01-20T12:00:00Z',
    rarity: '传说',
    rarityClass: 'rarity-legendary',
    value: 125.00
  },
  {
    id: 4,
    item: { name: 'Desert Eagle | 龙王', image: '/demo/item4.png' },
    caseName: '武器箱 #4',
    time: '2024-01-20T11:45:00Z',
    rarity: '稀有',
    rarityClass: 'rarity-rare',
    value: 18.90
  },
  {
    id: 5,
    item: { name: 'USP-S | 龙王', image: '/demo/item5.png' },
    caseName: '武器箱 #5',
    time: '2024-01-20T11:30:00Z',
    rarity: '普通',
    rarityClass: 'rarity-common',
    value: 5.20
  },
  {
    id: 6,
    item: { name: 'Glock-18 | 龙王', image: '/demo/item6.png' },
    caseName: '武器箱 #6',
    time: '2024-01-20T11:15:00Z',
    rarity: '普通',
    rarityClass: 'rarity-common',
    value: 3.80
  }
])

// 计算属性
const filteredOpenings = computed(() => {
  let filtered = openings.value
  
  if (currentFilter.value !== 'all') {
    filtered = filtered.filter(opening => {
      switch (currentFilter.value) {
        case 'rare': return opening.rarity === '稀有'
        case 'epic': return opening.rarity === '史诗'
        case 'legendary': return opening.rarity === '传说'
        default: return true
      }
    })
  }
  
  const startIndex = (currentPage.value - 1) * itemsPerPage
  const endIndex = startIndex + itemsPerPage
  return filtered.slice(startIndex, endIndex)
})

const totalOpenings = computed(() => openings.value.length)

const rareItems = computed(() => 
  openings.value.filter(opening => opening.rarity === '稀有').length
)

const legendaryItems = computed(() => 
  openings.value.filter(opening => opening.rarity === '传说').length
)

const totalValue = computed(() => 
  openings.value.reduce((sum, opening) => sum + (opening.value || 0), 0)
)

const totalPages = computed(() => {
  const filtered = currentFilter.value === 'all' 
    ? openings.value 
    : openings.value.filter(opening => {
        switch (currentFilter.value) {
          case 'rare': return opening.rarity === '稀有'
          case 'epic': return opening.rarity === '史诗'
          case 'legendary': return opening.rarity === '传说'
          default: return true
        }
      })
  return Math.ceil(filtered.length / itemsPerPage)
})

const visiblePages = computed(() => {
  const pages = []
  const maxVisible = 5
  const start = Math.max(1, currentPage.value - Math.floor(maxVisible / 2))
  const end = Math.min(totalPages.value, start + maxVisible - 1)
  
  for (let i = start; i <= end; i++) {
    pages.push(i)
  }
  
  return pages
})

// 方法
const formatTime = (timeString: string) => {
  return new Date(timeString).toLocaleDateString()
}
</script>

<style scoped>
/* 页面头部样式 */
.page-header {
  text-align: center;
  position: relative;
}

.page-header::after {
  content: '';
  position: absolute;
  bottom: -1rem;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, var(--color-primary), var(--color-secondary));
  border-radius: 2px;
}

/* 筛选按钮样式 */
.filter-btn {
  @apply px-6 py-2 rounded-xl text-sm font-medium transition-all duration-300;
  @apply bg-white/5 border border-white/10 text-white/70;
  @apply hover:bg-white/10 hover:border-white/20 hover:text-white;
}

.filter-btn.active {
  @apply bg-primary/20 border-primary text-primary;
  @apply shadow-lg shadow-primary/25;
}

.filter-btn-mobile {
  @apply px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300;
  @apply bg-white/5 border border-white/10 text-white/70;
  @apply hover:bg-white/10 hover:border-white/20 hover:text-white;
}

.filter-btn-mobile.active {
  @apply bg-primary/20 border-primary text-primary;
  @apply shadow-lg shadow-primary/25;
}

/* 统计卡片样式 */
.stat-card {
  @apply bg-white/5 backdrop-blur-md border border-white/10 rounded-2xl p-6;
  @apply hover:border-white/20 transition-all duration-300;
  @apply flex items-center space-x-4;
}

.stat-icon {
  @apply flex-shrink-0;
}

.stat-content {
  @apply flex-1;
}

.stat-value {
  @apply text-2xl font-bold text-white mb-1;
}

.stat-label {
  @apply text-white/60 text-sm;
}

.stat-card-mobile {
  @apply bg-white/5 backdrop-blur-md border border-white/10 rounded-xl p-4;
  @apply hover:border-white/20 transition-all duration-300;
  @apply flex items-center space-x-3;
}

.stat-card-mobile .stat-value {
  @apply text-lg font-bold text-white mb-1;
}

.stat-card-mobile .stat-label {
  @apply text-white/60 text-xs;
}

/* 开箱卡片样式 */
.opening-card {
  @apply bg-white/5 backdrop-blur-md border border-white/10 rounded-2xl overflow-hidden;
  @apply hover:border-white/20 transition-all duration-300;
  @apply hover:-translate-y-1 transform;
}

.opening-card.rarity-rare {
  @apply border-blue-500/30 hover:border-blue-500/50;
}

.opening-card.rarity-epic {
  @apply border-purple-500/30 hover:border-purple-500/50;
}

.opening-card.rarity-legendary {
  @apply border-orange-500/30 hover:border-orange-500/50;
}

.opening-image {
  @apply relative w-full h-48 overflow-hidden;
}

.opening-image img {
  @apply w-full h-full object-cover;
}

.opening-rarity-badge {
  @apply absolute top-2 right-2 px-2 py-1 rounded-full text-xs font-medium;
}

.opening-rarity-badge.rarity-rare {
  @apply bg-blue-500/20 text-blue-400 border border-blue-500/30;
}

.opening-rarity-badge.rarity-epic {
  @apply bg-purple-500/20 text-purple-400 border border-purple-500/30;
}

.opening-rarity-badge.rarity-legendary {
  @apply bg-orange-500/20 text-orange-400 border border-orange-500/30;
}

.opening-rarity-badge.rarity-common {
  @apply bg-gray-500/20 text-gray-400 border border-gray-500/30;
}

.opening-info {
  @apply p-4;
}

.opening-item-name {
  @apply text-white font-medium mb-2;
}

.opening-details {
  @apply space-y-1 mb-2;
}

.opening-case {
  @apply text-white/60 text-sm;
}

.opening-time {
  @apply text-white/40 text-xs;
}

.opening-value {
  @apply text-green-400 font-semibold text-sm;
}

/* 移动端开箱项样式 */
.opening-item-mobile {
  @apply bg-white/5 backdrop-blur-md border border-white/10 rounded-xl overflow-hidden;
  @apply hover:border-white/20 transition-all duration-300;
  @apply flex;
}

.opening-item-mobile.rarity-rare {
  @apply border-blue-500/30 hover:border-blue-500/50;
}

.opening-item-mobile.rarity-epic {
  @apply border-purple-500/30 hover:border-purple-500/50;
}

.opening-item-mobile.rarity-legendary {
  @apply border-orange-500/30 hover:border-orange-500/50;
}

.opening-item-mobile .opening-image {
  @apply w-16 h-16 flex-shrink-0;
}

.opening-item-mobile .opening-info {
  @apply p-3 flex-1;
}

.opening-item-mobile .opening-item-name {
  @apply text-white font-medium mb-1 text-sm;
}

.opening-item-mobile .opening-details {
  @apply space-y-0.5 mb-1;
}

.opening-item-mobile .opening-case {
  @apply text-white/60 text-xs;
}

.opening-item-mobile .opening-time {
  @apply text-white/40 text-xs;
}

.opening-item-mobile .opening-value {
  @apply text-green-400 font-semibold text-xs;
}

/* 分页样式 */
.pagination-section {
  @apply flex justify-center;
}

.pagination-btn {
  @apply px-3 py-2 rounded-lg text-sm font-medium transition-all duration-300;
  @apply bg-white/5 border border-white/10 text-white/70;
  @apply hover:bg-white/10 hover:border-white/20 hover:text-white;
}

.pagination-btn.active {
  @apply bg-primary/20 border-primary text-primary;
  @apply shadow-lg shadow-primary/25;
}

.pagination-btn.disabled {
  @apply opacity-50 cursor-not-allowed;
}

.pagination-btn-mobile {
  @apply px-2 py-2 rounded text-sm font-medium transition-all duration-300;
  @apply bg-white/5 border border-white/10 text-white/70;
  @apply hover:bg-white/10 hover:border-white/20 hover:text-white;
}

.pagination-btn-mobile.disabled {
  @apply opacity-50 cursor-not-allowed;
}

.pagination-info {
  @apply px-3 py-2 text-white/70 text-sm;
}

/* 渐变文本 */
.text-gradient {
  @apply text-transparent bg-clip-text bg-gradient-to-r from-primary to-secondary;
}

/* 网格背景 */
.bg-grid-pattern {
  background-image: linear-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px),
                    linear-gradient(90deg, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
  background-size: 50px 50px;
}

/* 噪声背景 */
.bg-noise {
  background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 200 200' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.65' numOctaves='3' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E");
}

/* 动画 */
.animate-pulse-slow {
  animation: pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}
</style> 