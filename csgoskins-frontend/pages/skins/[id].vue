<template>
  <div class="min-h-screen bg-gray-900/80">
    <!-- 背景装饰元素 -->
    <div class="fixed inset-0 -z-10">
      <div class="absolute top-0 -left-32 w-96 h-96 rounded-full blur-3xl bg-primary/5 animate-pulse-slow"></div>
      <div class="absolute bottom-0 -right-32 w-96 h-96 rounded-full blur-3xl bg-secondary/5 animate-pulse-slow"></div>
      <div class="absolute inset-0 bg-grid opacity-10"></div>
      <div class="absolute inset-0 bg-noise opacity-30 mix-blend-soft-light"></div>
    </div>

    <!-- 加载状态 -->
    <div v-if="isLoading" class="flex flex-col items-center justify-center py-20 min-h-screen">
      <UiCSGOSkeleton type="case" class="w-96 h-64" />
      <span class="ml-3 text-white/60 mt-4">{{ $t('common.loading') }}</span>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="hasError" class="flex flex-col items-center justify-center py-20 min-h-screen text-white/40">
      <div class="w-20 h-20 rounded-full bg-red-900/20 flex items-center justify-center mb-4 border border-red-800/30">
        <i class="i-ph-warning text-4xl text-red-500/60"></i>
      </div>
      <p class="text-lg text-white/70">{{ $t('skins.load_error') }}</p>
      <p class="text-sm mt-1 text-white/50">{{ errorMessage }}</p>
      <button
        @click="fetchSkinDetail"
        class="mt-6 bg-gradient-primary text-black py-2 px-6 rounded-lg transition-all duration-300 font-medium shadow-sm hover:shadow-lg hover:-translate-y-0.5 transform"
      >
        {{ $t('skins.retry') }}
      </button>
    </div>

    <template v-else>
      <!-- 页面头部导航 -->
      <div class="border-b border-gray-800/50 bg-gray-900/60 backdrop-blur-sm">
        <div class="container mx-auto px-4 py-4">
          <div class="flex items-center gap-2 text-white/60 text-sm">
            <NuxtLink to="/" class="hover:text-primary transition-colors">
              {{ $t('common.home') }}
            </NuxtLink>
            <i class="i-ph-caret-right text-xs"></i>
            <NuxtLink to="/skins" class="hover:text-primary transition-colors">
              {{ $t('common.skins') }}
            </NuxtLink>
            <i class="i-ph-caret-right text-xs"></i>
            <span class="text-white">{{ skinData.localizedName || skinData.name }}</span>
          </div>
        </div>
      </div>

      <!-- 饰品主要信息 -->
      <div class="container mx-auto px-4 py-8">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <!-- 左侧 - 饰品图片和稀有度 -->
          <div class="lg:col-span-1">
            <div
              class="bg-gradient-to-br rounded-xl p-6 overflow-hidden shadow-lg backdrop-blur-sm border border-gray-700/30 hover:border-primary/30 transition-all duration-500"
              :class="getGradientByRarity(skinData.rarityColor)"
            >
              <div class="relative aspect-square flex items-center justify-center group">
                <img
                  v-if="skinData.image"
                  :src="skinData.image"
                  :alt="`${skinData.weapon} | ${skinData.skin}`"
                  class="w-full h-auto object-contain transform hover:scale-105 transition-transform duration-500"
                  @error="handleImageError"
                />
                <div
                  v-else
                  class="flex items-center justify-center h-full flex-col text-gray-500"
                >
                  <i class="i-ph-image text-6xl text-gray-500"></i>
                  <span class="text-xl font-bold mt-2">{{ $t('skins.image_error') }}</span>
                </div>

                <!-- 悬停时的光效 -->
                <div class="absolute inset-0 bg-gradient-to-tr from-transparent to-white/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                <!-- 稀有度指示条 -->
                <div
                  class="absolute bottom-0 left-0 right-0 h-1"
                  :style="skinData.rarityColor ? { backgroundColor: skinData.rarityColor } : {}"
                ></div>

                <div
                  v-if="skinData.isStatTrak"
                  class="absolute top-4 right-4 bg-gradient-to-r from-orange-600 to-orange-500 text-white text-xs font-bold py-1 px-3 rounded-md shadow-lg"
                >
                  StatTrak™
                </div>
              </div>
            </div>

            <!-- 分享和收藏 -->
            <div class="flex items-center justify-between mt-4">
              <button class="btn-action" @click="showMessage('分享功能还在开发中')">
                <i class="i-ph-share-network text-lg"></i>
                <span>{{ $t('common.share') }}</span>
              </button>
              <button class="btn-action" @click="showMessage('收藏功能还在开发中')">
                <i class="i-ph-bookmark-simple text-lg"></i>
                <span>{{ $t('common.collect') }}</span>
              </button>
              <button class="btn-action" @click="showMessage('检视功能还在开发中')">
                <i class="i-ph-eye text-lg"></i>
                <span>{{ $t('common.inspect') }}</span>
              </button>
            </div>

            <!-- 可获取箱子 -->
            <div v-if="relatedCases && relatedCases.length > 0" class="mt-6">
              <div class="flex items-center mb-4">
                <i class="i-ph-package text-primary mr-2"></i>
                <h2 class="text-white text-xl font-bold">{{ $t('skins.available_cases') }}</h2>
              </div>
              <div class="space-y-3">
                <div
                  v-for="box in relatedCases"
                  :key="box.id"
                  class="bg-gray-800/40 hover:bg-gray-800/60 rounded-lg p-3 flex items-center gap-3 cursor-pointer transition-all duration-300 border border-gray-700/30 hover:border-primary/30 backdrop-blur-sm hover:-translate-y-0.5 transform shadow-md hover:shadow-lg group"
                >
                  <div class="relative w-16 h-16 overflow-hidden rounded-md">
                    <NuxtLink :to="`/cases/${box.case_key}`">
                      <img
                        :src="box.cover"
                        :alt="box.name"
                        class="w-full h-full object-contain group-hover:scale-110 transition-transform duration-300"
                        @error="handleImageError"
                      />
                    </NuxtLink>
                  </div>
                  <div class="flex-1">
                    <div class="text-white font-medium group-hover:text-primary transition-colors">
                      {{ box.name }}
                    </div>
                    <div class="text-primary/80 text-sm">¥ {{ formatPrice(box.price) }}</div>
                  </div>
                  <button
                    class="bg-gray-700/70 hover:bg-gradient-to-r hover:from-primary hover:to-secondary text-white py-1 px-3 rounded transition-all text-sm hover:text-black font-medium"
                    @click="navigateToCase(box.case_key)"
                  >
                    {{ $t('cases.open') }}
                  </button>
                </div>
              </div>
            </div>

            <!-- 上一个/下一个饰品 -->
            <div class="mt-6 space-y-2">
              <button
                v-if="prevItem"
                @click="navigateToDetail(prevItem.id)"
                class="btn-action flex w-full text-sm"
              >
                <i class="i-ph-arrow-left"></i>
                <span class="flex-1 truncate">{{ prevItem.name }}</span>
              </button>
              <button
                v-if="nextItem"
                @click="navigateToDetail(nextItem.id)"
                class="btn-action flex w-full text-sm"
              >
                <span class="flex-1 truncate">{{ nextItem.name }}</span>
                <i class="i-ph-arrow-right"></i>
              </button>
            </div>
          </div>

          <!-- 中间 - 饰品详细信息 -->
          <div class="lg:col-span-1">
            <h1
              :style="skinData.rarityColor ? { color: skinData.rarityColor } : {}"
              class="text-2xl lg:text-3xl font-bold mb-1"
            >
              {{ skinData.skin }}
            </h1>
            <div class="text-white/80 text-lg mb-6">{{ skinData.weapon }}</div>

            <!-- 价格信息 -->
            <div class="flex items-center bg-gray-800/40 backdrop-blur-sm rounded-lg p-4 mb-6 border border-gray-700/30 shadow-md hover:border-primary/20 transition-all duration-300">
              <img src="/images/dollar.svg" alt="price" class="w-6 h-6 mr-2" />
              <div>
                <div class="text-primary text-2xl font-bold">{{ formatPrice(skinData.price) }}</div>
                <div class="text-white/60 text-sm">{{ $t('skins.market_price') }}</div>
              </div>
              <div class="ml-auto flex items-center gap-2">
                <button class="bg-gradient-to-r from-primary to-primary-dark text-white py-2 px-6 rounded-lg shadow-lg hover:shadow-primary/20 hover:-translate-y-0.5 transform transition-all font-medium">
                  {{ $t('common.buy') }}
                </button>
                <button class="bg-gray-700/70 hover:bg-gradient-to-r hover:from-primary hover:to-secondary text-white py-2 px-4 rounded-lg shadow transition-all hover:-translate-y-0.5 transform backdrop-blur-sm border border-gray-600/30">
                  {{ $t('common.offer') }}
                </button>
              </div>
            </div>

            <!-- 饰品详情 -->
            <div class="mb-6">
              <div class="flex items-center mb-4">
                <i class="i-ph-info text-primary mr-2"></i>
                <h2 class="text-white text-xl font-bold">{{ $t('skins.details') }}</h2>
              </div>
              <div class="grid grid-cols-2 gap-4">
                <div class="detail-item">
                  <div class="detail-label">{{ $t('skins.category') }}</div>
                  <div class="detail-value">{{ skinData.category }}</div>
                </div>
                <div class="detail-item">
                  <div class="detail-label">{{ $t('skins.rarity') }}</div>
                  <div class="detail-value" :style="skinData.rarityColor ? { color: skinData.rarityColor } : {}">
                    {{ skinData.rarity }}
                  </div>
                </div>
                <div class="detail-item">
                  <div class="detail-label">{{ $t('skins.wear') }}</div>
                  <div class="detail-value">{{ (skinData.wear || 0).toFixed(8) }}</div>
                </div>
                <div class="detail-item">
                  <div class="detail-label">{{ $t('skins.exterior') }}</div>
                  <div class="detail-value" :style="skinData.exteriorColor ? { color: skinData.exteriorColor } : {}">
                    {{ skinData.exterior }}
                  </div>
                </div>
                <div class="detail-item">
                  <div class="detail-label">StatTrak™</div>
                  <div class="detail-value">
                    {{ skinData.isStatTrak ? $t('common.yes') : $t('common.no') }}
                  </div>
                </div>
                <div class="detail-item">
                  <div class="detail-label">{{ $t('skins.update_time') }}</div>
                  <div class="detail-value">{{ formatDate(skinData.updateTime) }}</div>
                </div>
              </div>
            </div>

            <!-- 磨损滑块 -->
            <div class="mb-6">
              <div class="flex items-center mb-4">
                <i class="i-ph-gauge text-primary mr-2"></i>
                <h2 class="text-white text-xl font-bold">{{ $t('skins.wear_range') }}</h2>
              </div>
              <div class="relative h-8 bg-gradient-to-r from-emerald-500 via-yellow-500 to-red-500 rounded-lg overflow-hidden shadow-inner">
                <!-- 磨损指示器 -->
                <div
                  class="absolute w-2 h-full bg-white top-0 border-2 border-white shadow-md"
                  :style="`left: ${(skinData.wear || 0) * 100}%`"
                ></div>

                <!-- 磨损范围标签 -->
                <div class="absolute inset-0 flex justify-between text-xs text-white font-bold px-2 text-shadow">
                  <div class="self-center">{{ $t('skins.factory_new') }}</div>
                  <div class="self-center">{{ $t('skins.minimal_wear') }}</div>
                  <div class="self-center">{{ $t('skins.field_tested') }}</div>
                  <div class="self-center">{{ $t('skins.well_worn') }}</div>
                  <div class="self-center">{{ $t('skins.battle_scarred') }}</div>
                </div>
              </div>
            </div>

            <!-- 外观描述 -->
            <div v-if="skinData.content" class="mb-6">
              <div class="flex items-center mb-4">
                <i class="i-ph-note text-primary mr-2"></i>
                <h2 class="text-white text-xl font-bold">{{ $t('skins.description') }}</h2>
              </div>
              <div
                class="bg-gray-800/40 backdrop-blur-sm rounded-lg p-4 text-white/80 border border-gray-700/30 shadow-md description-content"
                v-html="skinData.content"
              ></div>
            </div>
          </div>

          <!-- 右侧 - 相关箱子和相似饰品 -->
          <div class="lg:col-span-1">
            <!-- 相似饰品 -->
            <div v-if="relatedSkins && relatedSkins.length > 0">
              <div class="flex items-center mb-4">
                <i class="i-ph-shuffle text-primary mr-2"></i>
                <h2 class="text-white text-xl font-bold">{{ $t('skins.similar') }}</h2>
              </div>
              <div class="grid grid-cols-2 gap-3">
                <div
                  v-for="skin in relatedSkins"
                  :key="skin.id"
                  @click="navigateToDetail(skin.id)"
                  class="bg-gray-800/40 hover:bg-gray-800/60 rounded-lg overflow-hidden cursor-pointer transition-all duration-300 hover:-translate-y-1 transform shadow-md hover:shadow-lg border border-gray-700/30 hover:border-primary/30 backdrop-blur-sm group"
                >
                  <div
                    class="relative aspect-square bg-gradient-to-br p-2"
                    :class="getGradientByRarity(skin.rarityColor)"
                  >
                    <img
                      :src="skin.image"
                      :alt="`${skin.weapon} | ${skin.skin}`"
                      class="w-full h-full object-contain group-hover:scale-110 transition-transform duration-300"
                      @error="handleImageError"
                    />
                    <div
                      v-if="skin.isStatTrak"
                      class="absolute top-1 right-1 bg-gradient-to-r from-orange-600 to-orange-500 text-white text-[10px] font-bold py-0.5 px-1 rounded shadow-lg z-10"
                    >
                      ST™
                    </div>

                    <!-- 稀有度指示条 -->
                    <div
                      class="absolute bottom-0 left-0 right-0 h-1"
                      :style="skin.rarityColor ? { backgroundColor: skin.rarityColor } : {}"
                    ></div>
                  </div>
                  <div class="p-2">
                    <div class="text-white/80 text-xs truncate">{{ skin.weapon }}</div>
                    <div
                      :style="skin.rarityColor ? { color: skin.rarityColor } : {}"
                      class="text-sm font-medium truncate"
                    >
                      {{ skin.skin }}
                    </div>
                    <div class="text-primary text-xs font-bold mt-1 group-hover:text-gradient transition-all duration-300">
                      {{ formatPrice(skin.price) }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { useSeoMeta, useHead } from '@unhead/vue'
import { useRoute, useRouter } from 'vue-router'
import { skinApi, type SkinDetailResponse } from '~/services/skin-api'
import type { SkinItem } from '~/types/skin'
import { transformSkinItem } from '~/utils/skin-transformer'

// 国际化
const { t, locale } = useI18n()

// 路由
const route = useRoute()
const router = useRouter()
const skinId = computed(() => route.params.id as string)

// 数据状态
const isLoading = ref(true)
const hasError = ref(false)
const errorMessage = ref('')

// 皮肤数据
const skinData = ref<SkinItem>({} as SkinItem)
const relatedSkins = ref<SkinItem[]>([])
const nextItem = ref<SkinItem | null>(null)
const prevItem = ref<SkinItem | null>(null)
const relatedCases = ref<any[]>([])

// SEO元数据
const seoTitle = computed(() => 
  skinData.value.localizedName || skinData.value.name
    ? `${skinData.value.localizedName || skinData.value.name} | CSGO开箱网站`
    : '皮肤详情 | CSGO开箱网站'
)

const seoDescription = computed(() => 
  skinData.value.description 
    ? skinData.value.description.substring(0, 160)
    : '查看CS:GO皮肤详细信息，包括价格、稀有度、外观等'
)

useSeoMeta({
  title: seoTitle,
  description: seoDescription,
  ogTitle: seoTitle,
  ogDescription: seoDescription,
  ogImage: () => skinData.value.image || '/images/item-placeholder.svg',
  twitterTitle: seoTitle,
  twitterDescription: seoDescription,
  twitterImage: () => skinData.value.image || '/images/item-placeholder.svg'
})

useHead({
  title: seoTitle,
  meta: [
    { name: 'description', content: seoDescription },
    { property: 'og:title', content: seoTitle },
    { property: 'og:description', content: seoDescription },
    { property: 'og:image', content: () => skinData.value.image || '/images/item-placeholder.svg' },
    { name: 'twitter:title', content: seoTitle },
    { name: 'twitter:description', content: seoDescription },
    { name: 'twitter:image', content: () => skinData.value.image || '/images/item-placeholder.svg' }
  ]
})

// 获取皮肤详情
const fetchSkinDetail = async () => {
  try {
    isLoading.value = true
    hasError.value = false
    
    const response = await skinApi.getSkinDetail({ id: skinId.value })
    
    if (!response.success || !response.data) {
      throw new Error(response.message || '获取皮肤详情失败')
    }

    const data = response.data
    
    // 处理主皮肤数据
    if (data.item_info) {
      skinData.value = transformSkinItem(data.item_info)
    }

    // 处理相关皮肤
    if (data.related_skins && Array.isArray(data.related_skins)) {
      relatedSkins.value = data.related_skins.map(item => transformSkinItem(item))
    }

    // 处理上一个/下一个皮肤
    if (data.next_item) {
      nextItem.value = transformSkinItem(data.next_item)
    }
    if (data.pre_item) {
      prevItem.value = transformSkinItem(data.pre_item)
    }

    // 处理相关箱子
    if (data.related_cases && Array.isArray(data.related_cases)) {
      relatedCases.value = data.related_cases
    }

  } catch (error: any) {
    console.error('[Skin Detail] 获取皮肤详情失败:', error)
    hasError.value = true
    errorMessage.value = error.message || '获取皮肤详情失败'
  } finally {
    isLoading.value = false
  }
}

// 导航到箱子详情
const navigateToCase = (caseKey: string) => {
  router.push(`/cases/${caseKey}`)
}

// 导航到其他皮肤详情
const navigateToDetail = (id: string) => {
  if (id !== skinId.value) {
    router.push(`/skins/${id}`)
  }
}

// 获取稀有度渐变样式
const getGradientByRarity = (rarityColor?: string) => {
  if (!rarityColor) return 'from-gray-800 to-gray-700'
  return 'from-gray-800 to-gray-700'
}

// 格式化价格
const formatPrice = (price: number) => {
  if (price === undefined || price === null) return '0.00'
  return price.toFixed(2)
}

// 格式化日期
const formatDate = (date: string | number) => {
  if (!date) return ''
  return new Date(date).toLocaleString(locale.value === 'zh-hans' ? 'zh-CN' : 'en-US')
}

// 处理图片错误
const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.src = '/images/item-placeholder.svg'
}

// 显示消息
const showMessage = (message: string) => {
  console.log('[Skin Detail]', message)
  // 这里可以集成toast通知系统
}

// 监听路由变化
watch(() => route.params.id, (newId) => {
  if (newId && newId !== skinId.value) {
    fetchSkinDetail()
  }
})

// 组件挂载时获取数据
onMounted(() => {
  if (skinId.value) {
    fetchSkinDetail()
  }
})
</script>

<style scoped>
/* 背景网格 */
.bg-grid {
  background-image: linear-gradient(
      to right,
      rgba(255, 255, 255, 0.05) 0.0625rem,
      transparent 0.0625rem
    ),
    linear-gradient(
      to bottom,
      rgba(255, 255, 255, 0.05) 0.0625rem,
      transparent 0.0625rem
    );
  background-size: 1.25rem 1.25rem;
}

/* 慢脉冲动画 */
.animate-pulse-slow {
  animation: pulse-slow 8s ease-in-out infinite;
}

@keyframes pulse-slow {
  0%,
  100% {
    opacity: 0.4;
  }
  50% {
    opacity: 0.7;
  }
}

.text-shadow {
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.btn-action {
  @apply flex items-center gap-1 bg-gray-800/60 hover:bg-gray-700/70 text-white/80 hover:text-white py-2 px-4 rounded-lg transition-all duration-300 text-sm backdrop-blur-sm border border-gray-700/30 hover:border-primary/30 hover:-translate-y-0.5 transform shadow-sm hover:shadow-md;
}

.detail-item {
  @apply bg-gray-800/40 backdrop-blur-sm rounded-lg p-3 border border-gray-700/30 hover:border-primary/20 transition-all duration-300 hover:bg-gray-800/60;
}

.detail-label {
  @apply text-white/60 text-sm mb-1;
}

.detail-value {
  @apply text-white font-medium;
}

.text-gradient {
  @apply bg-clip-text text-transparent;
  background-image: linear-gradient(
    to right,
    var(--color-primary),
    var(--color-secondary)
  );
}

/* 描述内容样式 */
.description-content {
  @apply text-sm text-white/70 leading-relaxed;
}

.description-content :deep(p) {
  @apply mb-3;
}

.description-content :deep(a) {
  @apply text-primary hover:underline;
}

.description-content :deep(ul),
.description-content :deep(ol) {
  @apply pl-5 mb-3;
}

.description-content :deep(ul li) {
  @apply list-disc mb-1;
}

.description-content :deep(ol li) {
  @apply list-decimal mb-1;
}

.description-content :deep(h1),
.description-content :deep(h2),
.description-content :deep(h3) {
  @apply font-bold mb-2 mt-4;
}

.description-content :deep(h1) {
  @apply text-xl;
}

.description-content :deep(h2) {
  @apply text-lg;
}

.description-content :deep(h3) {
  @apply text-base;
}
</style> 