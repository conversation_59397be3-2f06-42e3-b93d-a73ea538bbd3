<template>
  <div class="container mx-auto px-4 py-8">
    <!-- SEO元数据自动适配 -->
    <!-- 页面头部 -->
    <div class="mb-8">
      <h1 class="text-3xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-white to-gray-300">
        {{ $t('skins.title') }}
      </h1>
      <p class="text-white/60 mt-2">{{ $t('skins.subtitle') }}</p>
    </div>

    <!-- 搜索与筛选栏 -->
    <div class="flex flex-col md:flex-row md:items-center gap-4 mb-6">
      <div class="flex-1">
        <input
          v-model="searchQuery"
          type="text"
          :placeholder="$t('skins.search_placeholder')"
          class="w-full bg-gray-800/80 border border-gray-700/50 rounded-lg py-2 px-4 text-white focus:outline-none focus:ring-2 focus:ring-primary/50 transition-all duration-300 backdrop-blur-sm"
        />
      </div>
      <div class="flex flex-wrap gap-2">
        <!-- 类型筛选 -->
        <select v-model="selectedType" class="bg-gray-800/80 border border-gray-700/50 rounded-lg py-2 px-3 text-white">
          <option value="">{{ $t('skins.filter_type') }}</option>
          <option v-for="type in typeOptions" :key="type.value" :value="type.value">{{ type.label }}</option>
        </select>
        <!-- 稀有度筛选 -->
        <select v-model="selectedRarity" class="bg-gray-800/80 border border-gray-700/50 rounded-lg py-2 px-3 text-white">
          <option value="">{{ $t('skins.filter_rarity') }}</option>
          <option v-for="rarity in rarityOptions" :key="rarity.value" :value="rarity.value">{{ rarity.label }}</option>
        </select>
        <!-- 品质筛选 -->
        <select v-model="selectedQuality" class="bg-gray-800/80 border border-gray-700/50 rounded-lg py-2 px-3 text-white">
          <option value="">{{ $t('skins.filter_quality') }}</option>
          <option v-for="quality in qualityOptions" :key="quality.value" :value="quality.value">{{ quality.label }}</option>
        </select>
        <!-- 外观筛选 -->
        <select v-model="selectedExterior" class="bg-gray-800/80 border border-gray-700/50 rounded-lg py-2 px-3 text-white">
          <option value="">{{ $t('skins.filter_exterior') }}</option>
          <option v-for="exterior in exteriorOptions" :key="exterior.value" :value="exterior.value">{{ exterior.label }}</option>
        </select>
        <!-- StatTrak筛选 -->
        <select v-model="selectedStatTrak" class="bg-gray-800/80 border border-gray-700/50 rounded-lg py-2 px-3 text-white">
          <option value="">StatTrak</option>
          <option value="1">{{ $t('skins.stattrak_yes') }}</option>
          <option value="0">{{ $t('skins.stattrak_no') }}</option>
        </select>
        <!-- 重置按钮 -->
        <button @click="resetFilters" class="btn-filter ml-2">{{ $t('skins.reset_filters') }}</button>
      </div>
    </div>

    <!-- 皮肤网格/骨架/错误/空状态 -->
    <div v-if="isLoading" class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
      <UiCSGOSkeleton v-for="i in 18" :key="i" type="case" />
    </div>
    <div v-else-if="hasError" class="text-center text-red-400 py-12">
      <div class="mb-4">{{ $t('skins.load_error') }}</div>
      <button @click="() => fetchSkins()" class="btn-filter">{{ $t('skins.retry') }}</button>
    </div>
    <div v-else-if="skins.length === 0" class="text-center text-white/40 py-20">
      <div class="mb-2">{{ $t('skins.no_results') }}</div>
      <div class="mb-4 text-sm">{{ $t('skins.adjust_filters') }}</div>
      <button @click="resetFilters" class="btn-filter">{{ $t('skins.reset_filters') }}</button>
    </div>
    <div v-else>
      <div class="text-white/60 mb-4 flex items-center">
        <i class="i-ph-list-bullets mr-2"></i>
        {{ $t('skins.showing_results', { count: skins.length, total: totalItems }) }}
      </div>
      <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
        <SkinCard v-for="skin in skins" :key="skin.id" :skin-item="skin" />
      </div>
      <div v-if="hasMore" class="flex justify-center mt-10">
        <button @click="loadMore" class="btn-filter">{{ $t('common.load_more') }}</button>
      </div>
      <div v-else class="text-center text-white/40 mt-10">
        --- {{ $t('skins.end_of_list') }} ---
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { useSeoMeta, useHead } from '@unhead/vue'
import { skinApi, type SkinCategory, type SkinQuality, type SkinRarity, type SkinExterior } from '~/services/skin-api'
import type { SkinItem } from '~/types/skin'
import { transformSkinItem } from '~/utils/skin-transformer'
import SkinCard from '~/components/skin/SkinCard.vue'
// 骨架屏自动导入UiCSGOSkeleton

// 国际化
const { t, locale } = useI18n()

// 筛选器状态
const searchQuery = ref('')
const selectedType = ref('')
const selectedRarity = ref('')
const selectedQuality = ref('')
const selectedExterior = ref('')
const selectedStatTrak = ref('')

// 数据状态
const skins = ref<SkinItem[]>([])
const totalItems = ref(0)
const isLoading = ref(true)
const hasError = ref(false)
const hasMore = ref(false)
const page = ref(1)
const pageSize = 30

// 选项数据
const typeOptions = ref<{ value: string, label: string }[]>([])
const rarityOptions = ref<{ value: string, label: string }[]>([])
const qualityOptions = ref<{ value: string, label: string }[]>([])
const exteriorOptions = ref<{ value: string, label: string }[]>([])

// 获取筛选选项
const fetchOptions = async () => {
  const [catRes, qualRes, rarRes, extRes] = await Promise.all([
    skinApi.getCategories(),
    skinApi.getQualities(),
    skinApi.getRarities(),
    skinApi.getExteriors()
  ])
  if (catRes.success) {
    typeOptions.value = catRes.data!.map((c: SkinCategory) => ({ value: String(c.cate_id), label: c.cate_name }))
  }
  if (rarRes.success) {
    rarityOptions.value = rarRes.data!.map((r: SkinRarity) => ({ value: String(r.rarity_id), label: r.rarity_name }))
  }
  if (qualRes.success) {
    qualityOptions.value = qualRes.data!.map((q: SkinQuality) => ({ value: String(q.quality_id), label: q.quality_name }))
  }
  if (extRes.success) {
    exteriorOptions.value = extRes.data!.map((e: SkinExterior) => ({ value: String(e.exterior_id), label: e.exterior_name }))
  }
}

// 获取皮肤数据
const fetchSkins = async (reset = false) => {
  if (reset) {
    page.value = 1
    skins.value = []
  }
  isLoading.value = true
  hasError.value = false
  try {
    const params: any = {
      page: page.value,
      pageSize,
      q: searchQuery.value || undefined,
      category: selectedType.value || undefined,
      rarity: selectedRarity.value || undefined,
      quality: selectedQuality.value || undefined,
      exterior: selectedExterior.value || undefined,
      statTrak: selectedStatTrak.value !== '' ? Number(selectedStatTrak.value) : undefined
    }
    const res = await skinApi.searchSkins(params)
    if (res.success && res.data) {
      const items: SkinItem[] = res.data.items.map((item: any) => transformSkinItem(item, locale.value))
      if (page.value === 1) {
        skins.value = items
      } else {
        skins.value = [...skins.value, ...items]
      }
      totalItems.value = res.data.total
      hasMore.value = (page.value * pageSize) < res.data.total
    } else {
      hasError.value = true
    }
  } catch (e) {
    hasError.value = true
  } finally {
    isLoading.value = false
  }
}

// 加载更多
const loadMore = () => {
  if (hasMore.value && !isLoading.value) {
    page.value++
    fetchSkins()
  }
}

// 重置筛选
const resetFilters = () => {
  searchQuery.value = ''
  selectedType.value = ''
  selectedRarity.value = ''
  selectedQuality.value = ''
  selectedExterior.value = ''
  selectedStatTrak.value = ''
  fetchSkins(true)
}

// 监听筛选变化自动刷新
watch([
  searchQuery, selectedType, selectedRarity, selectedQuality, selectedExterior, selectedStatTrak, locale
], () => {
  fetchSkins(true)
})

onMounted(async () => {
  await fetchOptions()
  await fetchSkins(true)
})

// SEO元数据
useSeoMeta({
  title: t('skins.title'),
  description: t('skins.subtitle'),
  ogTitle: t('skins.title'),
  ogDescription: t('skins.subtitle')
})
useHead({
  htmlAttrs: { lang: locale.value },
  link: [
    { rel: 'alternate', hreflang: 'zh-hans', href: 'https://site.com/zh-hans/skins' },
    { rel: 'alternate', hreflang: 'en', href: 'https://site.com/en/skins' },
    { rel: 'alternate', hreflang: 'x-default', href: 'https://site.com/zh-hans/skins' }
  ]
})
</script>

<style scoped>
.btn-filter {
  @apply bg-gray-800/50 hover:bg-gray-700/70 text-white/80 py-2 px-4 rounded-lg text-sm transition-all duration-300 border border-gray-700/30 backdrop-blur-sm flex items-center;
}
</style> 