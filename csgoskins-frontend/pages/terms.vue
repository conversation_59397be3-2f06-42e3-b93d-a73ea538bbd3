<template>
  <div class="min-h-screen relative">
    <!-- 背景层 -->
    <div class="absolute inset-0 overflow-hidden">
      <div class="absolute inset-0 bg-gradient-to-b from-background-darker via-background to-background-dark z-0" />
      <div class="particle-container absolute inset-0 z-0" />
      <div class="absolute top-0 left-0 w-full h-full overflow-hidden opacity-15 z-0">
        <div class="light-beam light-beam-1" />
        <div class="light-beam light-beam-3" />
        <div class="light-beam light-beam-2" />
      </div>
      <div class="absolute inset-0 bg-grid-pattern opacity-[0.04] z-0" />
    </div>

    <!-- PC版内容 -->
    <div class="container mx-auto px-4 py-6 relative z-10" v-if="!isMobile">
      <div class="max-w-6xl mx-auto">
        <!-- 页面头部 -->
        <div class="page-header mb-12">
          <h1 class="text-4xl font-bold text-white mb-4">{{ $t('terms.title') }}</h1>
          <p class="text-gray-300 text-lg">{{ $t('terms.description') }}</p>
          <div class="text-sm text-gray-400 mt-2">
            {{ $t('terms.last_updated') }}: {{ $t('terms.update_date') }}
          </div>
        </div>

        <!-- 目录导航 -->
        <div class="toc-section mb-8">
          <div class="bg-white/5 backdrop-blur-md border border-white/10 rounded-2xl p-6">
            <h2 class="text-xl font-semibold text-white mb-4">{{ $t('terms.toc.title') }}</h2>
            <nav class="space-y-2">
              <a
                v-for="section in sections"
                :key="section.id"
                :href="`#${section.id}`"
                class="block text-gray-300 hover:text-primary transition-colors duration-200 py-1"
              >
                {{ $t(`terms.sections.${section.id}.title`) }}
              </a>
            </nav>
          </div>
        </div>

        <!-- 内容区域 -->
        <div class="content-section space-y-8">
          <section
            v-for="section in sections"
            :key="section.id"
            :id="section.id"
            class="bg-white/5 backdrop-blur-md border border-white/10 rounded-2xl p-8"
          >
            <h2 class="text-2xl font-semibold text-white mb-6">{{ $t(`terms.sections.${section.id}.title`) }}</h2>
            <div class="prose prose-invert max-w-none">
              <div v-html="$t(`terms.sections.${section.id}.content`)" class="text-gray-300 leading-relaxed"></div>
            </div>
          </section>
        </div>

        <!-- 联系方式 -->
        <div class="contact-section mt-12">
          <div class="bg-white/5 backdrop-blur-md border border-white/10 rounded-2xl p-8 text-center">
            <h2 class="text-2xl font-semibold text-white mb-4">{{ $t('terms.contact.title') }}</h2>
            <p class="text-gray-300 mb-6">{{ $t('terms.contact.description') }}</p>
            <div class="flex justify-center space-x-6">
              <a
                href="mailto:<EMAIL>"
                class="flex items-center space-x-2 text-primary hover:text-primary-light transition-colors duration-200"
              >
                <i class="i-heroicons-envelope w-5 h-5" />
                <span><EMAIL></span>
              </a>
              <a
                href="/contact"
                class="flex items-center space-x-2 text-primary hover:text-primary-light transition-colors duration-200"
              >
                <i class="i-heroicons-chat-bubble-left-right w-5 h-5" />
                <span>{{ $t('terms.contact.contact_form') }}</span>
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 移动版内容 -->
    <div class="container mx-auto px-4 py-4 relative z-10" v-else>
      <div class="max-w-4xl mx-auto">
        <!-- 页面头部 -->
        <div class="page-header mb-8">
          <h1 class="text-2xl font-bold text-white mb-3">{{ $t('terms.title') }}</h1>
          <p class="text-gray-300 text-sm">{{ $t('terms.description') }}</p>
          <div class="text-xs text-gray-400 mt-2">
            {{ $t('terms.last_updated') }}: {{ $t('terms.update_date') }}
          </div>
        </div>

        <!-- 目录导航 -->
        <div class="toc-section mb-6">
          <div class="bg-white/5 backdrop-blur-md border border-white/10 rounded-xl p-4">
            <h2 class="text-lg font-semibold text-white mb-3">{{ $t('terms.toc.title') }}</h2>
            <nav class="space-y-1">
              <a
                v-for="section in sections"
                :key="section.id"
                :href="`#${section.id}`"
                class="block text-gray-300 hover:text-primary transition-colors duration-200 py-1 text-sm"
              >
                {{ $t(`terms.sections.${section.id}.title`) }}
              </a>
            </nav>
          </div>
        </div>

        <!-- 内容区域 -->
        <div class="content-section space-y-6">
          <section
            v-for="section in sections"
            :key="section.id"
            :id="section.id"
            class="bg-white/5 backdrop-blur-md border border-white/10 rounded-xl p-6"
          >
            <h2 class="text-xl font-semibold text-white mb-4">{{ $t(`terms.sections.${section.id}.title`) }}</h2>
            <div class="prose prose-invert max-w-none">
              <div v-html="$t(`terms.sections.${section.id}.content`)" class="text-gray-300 leading-relaxed text-sm"></div>
            </div>
          </section>
        </div>

        <!-- 联系方式 -->
        <div class="contact-section mt-8">
          <div class="bg-white/5 backdrop-blur-md border border-white/10 rounded-xl p-6 text-center">
            <h2 class="text-xl font-semibold text-white mb-3">{{ $t('terms.contact.title') }}</h2>
            <p class="text-gray-300 mb-4 text-sm">{{ $t('terms.contact.description') }}</p>
            <div class="flex flex-col space-y-3">
              <a
                href="mailto:<EMAIL>"
                class="flex items-center justify-center space-x-2 text-primary hover:text-primary-light transition-colors duration-200"
              >
                <i class="i-heroicons-envelope w-4 h-4" />
                <span class="text-sm"><EMAIL></span>
              </a>
              <a
                href="/contact"
                class="flex items-center justify-center space-x-2 text-primary hover:text-primary-light transition-colors duration-200"
              >
                <i class="i-heroicons-chat-bubble-left-right w-4 h-4" />
                <span class="text-sm">{{ $t('terms.contact.contact_form') }}</span>
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 国际化
const { t } = useI18n()

// 设备检测 - 使用响应式状态
const isMobile = ref(false)

// 在客户端检测设备类型
onMounted(() => {
  const checkMobile = () => {
    isMobile.value = window.innerWidth < 768
  }
  
  checkMobile()
  window.addEventListener('resize', checkMobile)
  
  onUnmounted(() => {
    window.removeEventListener('resize', checkMobile)
  })
})

// SEO配置
useSeoMeta({
  title: () => t('terms.seo.title'),
  description: () => t('terms.seo.description'),
  keywords: () => t('terms.seo.keywords'),
  ogTitle: () => t('terms.seo.title'),
  ogDescription: () => t('terms.seo.description'),
  twitterTitle: () => t('terms.seo.title'),
  twitterDescription: () => t('terms.seo.description')
})

// 页面章节
const sections = [
  { id: 'acceptance', title: 'terms.sections.acceptance.title' },
  { id: 'services', title: 'terms.sections.services.title' },
  { id: 'user_accounts', title: 'terms.sections.user_accounts.title' },
  { id: 'payment_terms', title: 'terms.sections.payment_terms.title' },
  { id: 'prohibited_activities', title: 'terms.sections.prohibited_activities.title' },
  { id: 'intellectual_property', title: 'terms.sections.intellectual_property.title' },
  { id: 'privacy_policy', title: 'terms.sections.privacy_policy.title' },
  { id: 'disclaimer', title: 'terms.sections.disclaimer.title' },
  { id: 'limitation_liability', title: 'terms.sections.limitation_liability.title' },
  { id: 'termination', title: 'terms.sections.termination.title' },
  { id: 'governing_law', title: 'terms.sections.governing_law.title' },
  { id: 'changes_terms', title: 'terms.sections.changes_terms.title' }
]
</script>

<style scoped>
/* 粒子效果 */
.particle-container {
  background-image: radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
}

/* 动态光线效果 */
.light-beam {
  position: absolute;
  width: 2px;
  height: 100%;
  background: linear-gradient(to bottom, transparent, var(--color-primary), transparent);
  animation: lightBeam 8s infinite linear;
}

.light-beam-1 {
  left: 10%;
  animation-delay: 0s;
}

.light-beam-2 {
  left: 50%;
  animation-delay: 2.5s;
}

.light-beam-3 {
  left: 90%;
  animation-delay: 5s;
}

@keyframes lightBeam {
  0% {
    transform: translateY(-100%);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(100%);
    opacity: 0;
  }
}

/* 网格背景 */
.bg-grid-pattern {
  background-image: linear-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px),
                    linear-gradient(90deg, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
  background-size: 50px 50px;
}

/* 页面头部样式 */
.page-header {
  text-align: center;
  position: relative;
}

.page-header::after {
  content: '';
  position: absolute;
  bottom: -1rem;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, var(--color-primary), var(--color-secondary));
  border-radius: 2px;
}

/* 目录导航样式 */
.toc-section nav a {
  position: relative;
  padding-left: 1rem;
}

.toc-section nav a::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 4px;
  background: var(--color-primary);
  border-radius: 50%;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.toc-section nav a:hover::before {
  opacity: 1;
}

/* 内容区域样式 */
.content-section section {
  transition: all 0.3s ease;
}

.content-section section:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* 平滑滚动 */
html {
  scroll-behavior: smooth;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .page-header h1 {
    font-size: 1.75rem;
  }
  
  .content-section section {
    margin-bottom: 1.5rem;
  }
}

/* 链接样式 */
a {
  color: var(--color-primary);
  text-decoration: none;
  transition: color 0.2s ease;
}

a:hover {
  color: var(--color-primary-light);
}

/* 列表样式 */
.prose ul {
  list-style-type: disc;
  padding-left: 1.5rem;
  margin: 1rem 0;
}

.prose ol {
  list-style-type: decimal;
  padding-left: 1.5rem;
  margin: 1rem 0;
}

.prose li {
  margin: 0.5rem 0;
}

/* 标题样式 */
.prose h3 {
  color: white;
  font-size: 1.25rem;
  font-weight: 600;
  margin: 1.5rem 0 1rem 0;
}

.prose h4 {
  color: white;
  font-size: 1.1rem;
  font-weight: 600;
  margin: 1.25rem 0 0.75rem 0;
}

/* 强调文本 */
.prose strong {
  color: white;
  font-weight: 600;
}

.prose em {
  color: var(--color-primary);
  font-style: italic;
}
</style> 