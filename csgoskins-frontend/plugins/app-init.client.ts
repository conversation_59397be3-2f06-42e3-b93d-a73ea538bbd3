/**
 * 应用初始化插件
 * 在客户端初始化时恢复用户的语言设置
 */
export default defineNuxtPlugin(() => {
  // 只在客户端执行
  if (!process.client) return

  // 🎯 开发模式下的日志控制
  if (import.meta.dev) {
    // 创建一个过滤器来减少日志噪音
    const originalConsoleLog = console.log
    const originalConsoleWarn = console.warn

    console.log = (...args) => {
      const message = args[0]
      if (typeof message === 'string') {
        // 只显示关键错误信息
        if (message.includes('❌') ||
            message.includes('ERROR') ||
            message.includes('CRITICAL') ||
            message.includes('✅ GSAP') ||
            message.includes('✅ 开始箱子动画') ||
            message.includes('✅ 找到caseElement')) {
          originalConsoleLog(...args)
        }
      }
    }

    console.warn = (...args) => {
      const message = args[0]
      if (typeof message === 'string') {
        // 只显示关键警告信息
        if (message.includes('❌') ||
            message.includes('✅ GSAP') ||
            message.includes('✅ 开始箱子动画') ||
            message.includes('✅ 找到caseElement') ||
            message.includes('⚠️ GSAP不可用')) {
          originalConsoleWarn(...args)
        }
      } else {
        originalConsoleWarn(...args)
      }
    }
  }

  // 延迟执行，确保i18n已经初始化
  nextTick(() => {
    const LANGUAGE_STORAGE_KEY = 'user-locale'
    
    try {
      // 从localStorage读取保存的语言
      const savedLocale = localStorage.getItem(LANGUAGE_STORAGE_KEY)
      if (savedLocale && (savedLocale === 'en' || savedLocale === 'zh-hans')) {
        // 获取当前i18n实例
        const { $i18n } = useNuxtApp()
        
        if ($i18n?.locale && $i18n.locale.value !== savedLocale) {
          $i18n.locale.value = savedLocale
          document.documentElement.setAttribute('lang', savedLocale)
        }
      }
    } catch (error) {
      console.error('应用初始化语言恢复失败:', error)
    }
  })
}) 