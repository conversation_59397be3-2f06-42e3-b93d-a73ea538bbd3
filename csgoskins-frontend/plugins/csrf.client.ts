import { ref } from 'vue'
import { defineNuxtPlugin, useCookie } from '#app'

export default defineNuxtPlugin(() => {
  const csrfToken = ref('')
  const isTokenValid = ref(true)

  // 全局CSRF Token管理器
  const csrfManager = {
    async getToken(): Promise<string> {
      if (csrfToken.value && isTokenValid.value) {
        return csrfToken.value
      }

      try {
        // 尝试从多个来源获取token
        const sources = [
          () => document.querySelector('meta[name="csrf-token"]')?.getAttribute('content'),
          () => useCookie('csrftoken').value,
          async () => {
            const response = await $fetch('/api/csrf-token/')
            return (response as any).body?.token
          }
        ]

        for (const getTokenFromSource of sources) {
          const token = await getTokenFromSource()
          if (token) {
            csrfToken.value = token
            isTokenValid.value = true
            return token
          }
        }

        throw new Error('无法获取CSRF Token')

      } catch (error) {
        console.error('[CSRF] 获取Token失败:', error)
        isTokenValid.value = false
        throw error
      }
    },

    invalidateToken() {
      isTokenValid.value = false
      csrfToken.value = ''
    }
  }

  // 拦截所有$fetch请求，自动添加CSRF Token
  // Save original $fetch and cast to any to allow override
  const originalFetch: any = (globalThis as any).$fetch
  // Override $fetch globally with CSRF handling
  ;(globalThis as any).$fetch = async (request: any, options: any = {}) => {
    const url = typeof request === 'string' ? request : (request as any).url || request
    const method = options.method?.toUpperCase() || 'GET'

    // 对需要CSRF保护的请求自动添加Token
    if (['POST', 'PUT', 'PATCH', 'DELETE'].includes(method) && 
        typeof url === 'string' && url.startsWith('/api/')) {
      
      try {
        const token = await csrfManager.getToken()
        
        options.headers = {
          'X-CSRFToken': token,
          'X-Requested-With': 'XMLHttpRequest',
          ...options.headers
        }
        
        options.credentials = options.credentials || 'include'

      } catch (error) {
        console.warn('[CSRF] 自动添加Token失败:', error)
      }
    }

    try {
      return await originalFetch(request, options)
    } catch (error: any) {
      // 如果是CSRF错误，标记token无效并重试一次
      if (error.data?.detail?.includes('CSRF') || error.statusCode === 403) {
        console.log('[CSRF] 检测到CSRF错误，刷新Token重试')
        
        csrfManager.invalidateToken()
        
        if (['POST', 'PUT', 'PATCH', 'DELETE'].includes(method)) {
          try {
            const newToken = await csrfManager.getToken()
            options.headers = {
              ...options.headers,
              'X-CSRFToken': newToken
            }
            return await originalFetch(request, options)
          } catch (retryError) {
            console.error('[CSRF] 重试失败:', retryError)
          }
        }
      }
      
      throw error
    }
  }

  // 提供给组件使用
  return {
    provide: {
      csrf: csrfManager
    }
  }
})
