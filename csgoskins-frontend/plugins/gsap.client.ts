// GSAP客户端插件
export default defineNuxtPlugin(() => {
  // 确保只在客户端运行
  if (process.client) {
    // 动态导入GSAP
    import('gsap').then((module) => {
      const { gsap } = module
      
      // 注册TextPlugin
      import('gsap/TextPlugin').then((textModule) => {
        const { TextPlugin } = textModule
        gsap.registerPlugin(TextPlugin)
        console.warn('✅ GSAP和TextPlugin已加载', gsap.version)
        
        // 将gsap挂载到全局
        window.gsap = gsap
      }).catch(error => {
        console.error('❌ TextPlugin加载失败:', error)
      })
      
    }).catch(error => {
      console.error('❌ GSAP加载失败:', error)
    })
  }
})
