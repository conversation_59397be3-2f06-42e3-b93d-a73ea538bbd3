import { defineNuxtPlugin } from '#app'

// 🚀 根据WebSocket API文档重新实现的Socket插件
// 文档规定：所有消息格式为 [messageType, action, data, socketId?]
// 所有消息通过 ws_channel 频道发送

export default defineNuxtPlugin(async () => {
  if (!process.client) return

  console.warn('[🎰SOCKET] 🚀 开始初始化Socket.IO插件')

  const config = useRuntimeConfig()
  const socketStore = useSocketStore()

  try {
    console.warn('[🎰SOCKET] 📦 正在导入Socket.IO客户端...')
    // 动态导入Socket.IO客户端
    const socketIO = await import('socket.io-client')
    const io = socketIO.default
    console.warn('[🎰SOCKET] ✅ Socket.IO客户端导入成功')

    // 使用当前页面的origin作为Socket地址
    const socketUrl = window.location.origin

    console.warn('[🎰SOCKET] 🔗 Socket.IO连接配置:', {
      socketUrl,
      origin: window.location.origin,
      protocol: window.location.protocol,
      host: window.location.host
    })

    console.warn('[🎰SOCKET] 🔌 正在创建Socket.IO连接...')
    // 创建Socket.IO连接
    const socket = io(socketUrl, {
      transports: ['polling', 'websocket'],
      forceNew: false,
      reconnection: true,
      reconnectionAttempts: 5,
      reconnectionDelay: 1000,
      reconnectionDelayMax: 5000,
      timeout: 10000,
      autoConnect: true,
      path: '/socket.io',
      query: {
        page: 'ws',
        EIO: '4'
      }
    })
    
    // Socket.IO连接成功
    socket.on('connect', () => {
      console.warn('[🎰SOCKET] ✅ Socket.IO连接成功, ID:', socket.id)

      socketStore.setConnected(true)
      socketStore.setSocketId(socket.id)
      socketStore.setSocket(socket)

      // 加入监听频道和请求初始数据
      socket.emit('join', 'ws_channel')
      socket.emit('monitor', 'get_stats')
      socket.emit('case_records')

      // 派发全局连接事件
      window.dispatchEvent(new CustomEvent('socket:connected', {
        detail: { socketId: socket.id }
      }))
    })

    // Socket.IO连接断开
    socket.on('disconnect', (reason: string) => {
      console.warn('[🎰SOCKET] ❌ Socket.IO连接断开:', reason)
      socketStore.setConnected(false)
      socketStore.setSocketId(null)

      window.dispatchEvent(new CustomEvent('socket:disconnected', {
        detail: { reason }
      }))
    })

    // Socket.IO连接错误
    socket.on('connect_error', (error: any) => {
      console.error('[🎰SOCKET] ⚠️ Socket.IO连接错误:', error)
      socketStore.setConnectionError(error.message)
    })
    
    // 🎯 监听ws_channel频道的数组格式消息
    socket.on('ws_channel', (message: any) => {
      console.warn('[🎰SOCKET] 📨 收到ws_channel消息:', message)

      // 验证消息格式：必须是数组格式 [messageType, action, data, socketId?]
      if (!Array.isArray(message)) {
        console.warn('[🎰SOCKET] ⚠️ 消息格式错误，应为数组格式:', message)
        return
      }

      if (message.length < 3) {
        console.warn('[🎰SOCKET] ⚠️ 消息数组长度不足，至少需要3个元素:', message)
        return
      }

      const [messageType, action, data, socketId] = message

      // 验证messageType和action为字符串
      if (typeof messageType !== 'string' || typeof action !== 'string') {
        console.warn('[🎰SOCKET] ⚠️ messageType和action必须为字符串:', { messageType, action })
        return
      }

      console.warn(`[🎰SOCKET] 🎯 解析消息 [${messageType}, ${action}]`, data)

      // 根据messageType分发消息处理
      handleWebSocketMessage(messageType, action, data, socketId)
    })
    
    // Socket对象已经在connect事件中设置到store了
    
    // 🔧 处理WebSocket消息的核心函数
    function handleWebSocketMessage(messageType: string, action: string, data: any, socketId?: string) {
      try {
        // 更新统计信息
        socketStore.statistics.messagesReceived++
        socketStore.lastMessage = new Date()
        
        switch (messageType) {
          case 'boxroom':
            // 房间级别状态变化
            handleBoxroomMessage(action, data)
            break
            
          case 'boxroomdetail':
            // 房间详情变化 (动画同步消息)
            handleBoxroomDetailMessage(action, data, socketId)
            break
            
          case 'box':
            // 个人开箱消息
            handleBoxMessage(action, data)
            break
            
          case 'monitor':
            // 监控系统消息
            handleMonitorMessage(action, data)
            break
            
          case 'case_records':
            // 开箱记录消息
            handleCaseRecordsMessage(action, data)
            break
            
          case 'online_number':
            // 在线人数更新
            handleOnlineNumberMessage(action, data)
            break
            
          default:
            console.warn(`[🎰SOCKET] ⚠️ 未知消息类型: ${messageType}`, { action, data })
        }
        
        // 派发通用的socket消息事件
        window.dispatchEvent(new CustomEvent('socket:message', { 
          detail: { 
            messageType, 
            action, 
            data, 
            socketId,
            timestamp: Date.now()
          } 
        }))
        
      } catch (error) {
        console.error(`[🎰SOCKET] ❌ 处理消息失败 [${messageType}, ${action}]:`, error)
        socketStore.statistics.errors++
      }
    }
    
    // 🏠 处理房间级别消息 (boxroom)
    function handleBoxroomMessage(action: string, data: any) {
      console.log(`[🎰SOCKET] 🏠 房间消息 [boxroom, ${action}]`, data)
      
      switch (action) {
        case 'new':
          // 新房间创建
          window.dispatchEvent(new CustomEvent('socket:room_created', { detail: data }))
          break
          
        case 'update':
          // 房间状态更新 (包括玩家加入/离开)
          window.dispatchEvent(new CustomEvent('socket:room_updated', { detail: data }))
          break
          
        case 'start':
          // 对战开始
          window.dispatchEvent(new CustomEvent('socket:battle_started', { detail: data }))
          break
          
        case 'cancel':
          // 房间取消
          window.dispatchEvent(new CustomEvent('socket:room_cancelled', { detail: data }))
          break
          
        default:
          console.warn(`[🎰SOCKET] ⚠️ 未知房间操作: ${action}`, data)
      }
      
      // 同步到Store (保持向后兼容)
      socketStore.handleBattleMessage(action, data)
    }
    
    // 🎮 处理房间详情消息 (boxroomdetail) - 动画同步核心
    function handleBoxroomDetailMessage(action: string, data: any, socketId?: string) {
      console.log(`[🎰SOCKET] 🎮 房间详情 [boxroomdetail, ${action}]`, data)
      
      switch (action) {
        case 'round_start':
          // 回合开始 - 支持时间戳同步
          window.dispatchEvent(new CustomEvent('socket:round_start', { 
            detail: { data, socketId, timestamp: Date.now() } 
          }))
          break
          
        case 'opening_start':
          // 开箱动画触发 - 支持时间戳同步
          window.dispatchEvent(new CustomEvent('socket:opening_start', { 
            detail: { data, socketId, timestamp: Date.now() } 
          }))
          break
          
        case 'animation_progress':
          // 动画进度同步
          window.dispatchEvent(new CustomEvent('socket:animation_progress', { 
            detail: { data, socketId, timestamp: Date.now() } 
          }))
          break
          
        case 'round_result':
          // 回合结果
          window.dispatchEvent(new CustomEvent('socket:round_result', { 
            detail: { data, socketId, timestamp: Date.now() } 
          }))
          break
          
        case 'battle_end':
          // 对战结束
          window.dispatchEvent(new CustomEvent('socket:battle_end', { 
            detail: { data, socketId, timestamp: Date.now() } 
          }))
          break
          
        case 'time_sync_request':
          // 时钟同步请求 (新增功能)
          window.dispatchEvent(new CustomEvent('socket:time_sync_request', { 
            detail: { data, socketId, timestamp: Date.now() } 
          }))
          break
          
        // 兼容性消息 (保持向后兼容)
        case 'round':
        case 'end':
          console.log(`[🎰SOCKET] 📰 兼容性消息 [${action}]`, data)
          window.dispatchEvent(new CustomEvent(`socket:legacy_${action}`, { 
            detail: { data, socketId } 
          }))
          break
          
        default:
          console.warn(`[🎰SOCKET] ⚠️ 未知房间详情操作: ${action}`, data)
      }
      
      // 同步到Store (保持向后兼容)
      socketStore.handleBattleDetailMessage(action, data, socketId)
    }
    
    // 📦 处理个人开箱消息 (box)
    function handleBoxMessage(action: string, data: any) {
      console.log(`[🎰SOCKET] 📦 开箱消息 [box, ${action}]`, data)
      
      switch (action) {
        case 'new':
          // 新开箱结果 (延迟15秒发送)
          window.dispatchEvent(new CustomEvent('socket:case_opened', { detail: data }))
          break
          
        case 'details':
          // 箱子详情更新 (已注释功能)
          window.dispatchEvent(new CustomEvent('socket:case_details', { detail: data }))
          break
          
        default:
          console.warn(`[🎰SOCKET] ⚠️ 未知开箱操作: ${action}`, data)
      }
    }
    
    // 📊 处理监控系统消息 (monitor)
    function handleMonitorMessage(action: string, data: any) {
      console.log(`[🎰SOCKET] 📊 监控消息 [monitor, ${action}]`, data)
      
      switch (action) {
        case 'update':
          // 监控统计数据更新
          socketStore.updateMonitorData(data)
          window.dispatchEvent(new CustomEvent('socket:monitor_update', { detail: data }))
          break
          
        default:
          console.warn(`[🎰SOCKET] ⚠️ 未知监控操作: ${action}`, data)
      }
    }
    
    // 📝 处理开箱记录消息 (case_records)
    function handleCaseRecordsMessage(action: string, data: any) {
      console.log(`[🎰SOCKET] 📝 开箱记录 [case_records, ${action}]`, data)
      
      switch (action) {
        case 'update':
          // 开箱记录更新
          if (Array.isArray(data)) {
            socketStore.updateCaseRecords(data)
            window.dispatchEvent(new CustomEvent('socket:case_records_update', { detail: data }))
          } else {
            console.warn('[🎰SOCKET] ⚠️ 开箱记录数据应为数组格式:', data)
          }
          break
          
        default:
          console.warn(`[🎰SOCKET] ⚠️ 未知开箱记录操作: ${action}`, data)
      }
    }
    
    // 👥 处理在线人数消息 (online_number)
    function handleOnlineNumberMessage(action: string, data: any) {
      console.log(`[🎰SOCKET] 👥 在线人数 [online_number, ${action}]`, data)
      
      switch (action) {
        case 'update':
          // 在线人数更新
          if (typeof data === 'number') {
            socketStore.updateStatsData({ online_number: data })
            window.dispatchEvent(new CustomEvent('socket:online_number_update', { detail: data }))
          } else {
            console.warn('[🎰SOCKET] ⚠️ 在线人数数据应为数字:', data)
          }
          break
          
        default:
          console.warn(`[🎰SOCKET] ⚠️ 未知在线人数操作: ${action}`, data)
      }
    }
    
    // 清理函数
    window.addEventListener('beforeunload', () => {
      if (socket) {
        console.warn('[🎰SOCKET] 🧹 页面卸载，断开Socket.IO连接')
        socket.disconnect()
      }
    })

    console.warn('[🎰SOCKET] ✅ Socket.IO插件初始化完成')

  } catch (error) {
    console.error('[🎰SOCKET] ❌ Socket.IO初始化失败:', error)
    socketStore.setConnectionError('Socket.IO初始化失败')
  }
})