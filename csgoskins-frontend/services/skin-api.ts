// services/skin-api.ts
interface SkinItem {
  id: string
  name: string
  image: string
  value: number
  rarity: string
  type: string
  quality: string
  [key: string]: any
}

interface RandomSkinsParams {
  num?: number
  excludeTypes?: string
  domain?: string
}

interface SkinSearchParams {
  page?: number
  pageSize?: number
  q?: string
  category?: string
  quality?: string
  rarity?: string
  exterior?: string
  statTrak?: number
  min_price?: number
  max_price?: number
  sort?: string
}

interface SkinCategory {
  cate_id: number
  cate_name: string
  cate_name_en?: string
  cate_name_zh_hans?: string
  cate_name_zh?: string
}

interface SkinQuality {
  quality_id: number
  quality_name: string
  quality_name_en?: string
  quality_name_zh_hans?: string
  quality_name_zh?: string
  quality_color?: string
}

interface SkinRarity {
  rarity_id: number
  rarity_name: string
  rarity_name_en?: string
  rarity_name_zh_hans?: string
  rarity_name_zh?: string
  rarity_color?: string
}

interface SkinExterior {
  exterior_id: number
  exterior_name: string
  exterior_name_en?: string
  exterior_name_zh_hans?: string
  exterior_name_zh?: string
  exterior_color?: string
}

interface SkinDetailParams {
  id: string
}

interface SkinDetailResponse {
  item_info: any
  related_skins: any[]
  next_item?: any
  pre_item?: any
  related_cases: any[]
}

interface SkinApiResponse<T = any> {
  success: boolean
  data: T | null
  message: string
}

interface SkinSearchResponse {
  items: any[]
  total: number
  page: number
  limit: number
}

class SkinApi {
  /**
   * 获取随机皮肤数据
   */
  async getRandomSkins(params: RandomSkinsParams = {}): Promise<SkinApiResponse<SkinItem[]>> {
    try {
      const defaultParams = {
        num: 12,
        excludeTypes: 'CSGO_Type_Spray,CSGO_Tool_Sticker',
        domain: 'www.csgo.com',
        ...params
      }

      const response = await $fetch<{
        code: number
        body: {
          items: SkinItem[]
        }
        message: string
      }>('/api/package/items/random', {
        params: defaultParams
      })

      if (response.code === 0) {
        const skinsData = response.body?.items || []
        
        if (Array.isArray(skinsData) && skinsData.length > 0) {
          return {
            success: true,
            data: skinsData,
            message: '获取随机皮肤成功'
          }
        } else {
          return {
            success: false,
            data: null,
            message: '暂无皮肤数据'
          }
        }
      } else {
        console.error('[Skin API] 获取随机皮肤失败:', response)
        return {
          success: false,
          data: null,
          message: response.message || '获取随机皮肤失败'
        }
      }
    } catch (error: any) {
      console.error('[Skin API] 获取随机皮肤请求出错:', error)
      return {
        success: false,
        data: null,
        message: error.message || '获取随机皮肤请求出错'
      }
    }
  }

  /**
   * 搜索皮肤
   */
  async searchSkins(params: SkinSearchParams = {}): Promise<SkinApiResponse<SkinSearchResponse>> {
    try {
      const defaultParams = {
        page: 1,
        pageSize: 30,
        ...params
      }

      const response = await $fetch<{
        code: number
        body: SkinSearchResponse
        message: string
      }>('/api/package/skins/search', {
        params: defaultParams
      })

      if (response.code === 0) {
        return {
          success: true,
          data: response.body,
          message: '搜索皮肤成功'
        }
      } else {
        console.error('[Skin API] 搜索皮肤失败:', response)
        return {
          success: false,
          data: null,
          message: response.message || '搜索皮肤失败'
        }
      }
    } catch (error: any) {
      console.error('[Skin API] 搜索皮肤请求出错:', error)
      return {
        success: false,
        data: null,
        message: error.message || '搜索皮肤请求出错'
      }
    }
  }

  /**
   * 获取皮肤详情
   */
  async getSkinDetail(params: SkinDetailParams): Promise<SkinApiResponse<SkinDetailResponse>> {
    try {
      const response = await $fetch<{
        code: number
        body: {
          items: SkinDetailResponse
        }
        message: string
      }>('/api/package/skins/detail', {
        params
      })

      if (response.code === 0) {
        return {
          success: true,
          data: response.body?.items,
          message: '获取皮肤详情成功'
        }
      } else {
        console.error('[Skin API] 获取皮肤详情失败:', response)
        return {
          success: false,
          data: null,
          message: response.message || '获取皮肤详情失败'
        }
      }
    } catch (error: any) {
      console.error('[Skin API] 获取皮肤详情请求出错:', error)
      return {
        success: false,
        data: null,
        message: error.message || '获取皮肤详情请求出错'
      }
    }
  }

  /**
   * 获取皮肤分类
   */
  async getCategories(): Promise<SkinApiResponse<SkinCategory[]>> {
    try {
      const response = await $fetch<{
        code: number
        body: {
          items: SkinCategory[]
        }
        message: string
      }>('/api/package/skins/category')

      if (response.code === 0) {
        return {
          success: true,
          data: response.body?.items || [],
          message: '获取皮肤分类成功'
        }
      } else {
        console.error('[Skin API] 获取皮肤分类失败:', response)
        return {
          success: false,
          data: null,
          message: response.message || '获取皮肤分类失败'
        }
      }
    } catch (error: any) {
      console.error('[Skin API] 获取皮肤分类请求出错:', error)
      return {
        success: false,
        data: null,
        message: error.message || '获取皮肤分类请求出错'
      }
    }
  }

  /**
   * 获取皮肤品质
   */
  async getQualities(): Promise<SkinApiResponse<SkinQuality[]>> {
    try {
      const response = await $fetch<{
        code: number
        body: {
          items: SkinQuality[]
        }
        message: string
      }>('/api/package/skins/quality')

      if (response.code === 0) {
        return {
          success: true,
          data: response.body?.items || [],
          message: '获取皮肤品质成功'
        }
      } else {
        console.error('[Skin API] 获取皮肤品质失败:', response)
        return {
          success: false,
          data: null,
          message: response.message || '获取皮肤品质失败'
        }
      }
    } catch (error: any) {
      console.error('[Skin API] 获取皮肤品质请求出错:', error)
      return {
        success: false,
        data: null,
        message: error.message || '获取皮肤品质请求出错'
      }
    }
  }

  /**
   * 获取皮肤稀有度
   */
  async getRarities(): Promise<SkinApiResponse<SkinRarity[]>> {
    try {
      const response = await $fetch<{
        code: number
        body: {
          items: SkinRarity[]
        }
        message: string
      }>('/api/package/skins/rarity')

      if (response.code === 0) {
        return {
          success: true,
          data: response.body?.items || [],
          message: '获取皮肤稀有度成功'
        }
      } else {
        console.error('[Skin API] 获取皮肤稀有度失败:', response)
        return {
          success: false,
          data: null,
          message: response.message || '获取皮肤稀有度失败'
        }
      }
    } catch (error: any) {
      console.error('[Skin API] 获取皮肤稀有度请求出错:', error)
      return {
        success: false,
        data: null,
        message: error.message || '获取皮肤稀有度请求出错'
      }
    }
  }

  /**
   * 获取皮肤外观
   */
  async getExteriors(): Promise<SkinApiResponse<SkinExterior[]>> {
    try {
      const response = await $fetch<{
        code: number
        body: {
          items: SkinExterior[]
        }
        message: string
      }>('/api/package/skins/exterior')

      if (response.code === 0) {
        return {
          success: true,
          data: response.body?.items || [],
          message: '获取皮肤外观成功'
        }
      } else {
        console.error('[Skin API] 获取皮肤外观失败:', response)
        return {
          success: false,
          data: null,
          message: response.message || '获取皮肤外观失败'
        }
      }
    } catch (error: any) {
      console.error('[Skin API] 获取皮肤外观请求出错:', error)
      return {
        success: false,
        data: null,
        message: error.message || '获取皮肤外观请求出错'
      }
    }
  }
}

// 导出单例实例
export const skinApi = new SkinApi()

// 保持向后兼容性
export const SkinApiService = SkinApi
export { SkinApi as default }
export type { 
  SkinItem, 
  RandomSkinsParams, 
  SkinApiResponse,
  SkinSearchParams,
  SkinSearchResponse,
  SkinCategory,
  SkinQuality,
  SkinRarity,
  SkinExterior,
  SkinDetailParams,
  SkinDetailResponse
} 