import { defineStore } from 'pinia'
import { ref, computed, readonly } from 'vue'
import { authApi, type LoginCredentials, type RegisterData, type UserData, type ChangePasswordData } from '~/services/auth-api'

export interface User {
  uid: number
  email: string
  username: string
  nickname: string
  avatar?: string
  level: number
  balance: number
  points: number
  activePoints: number
  tradeUrl?: string
  isVip: boolean
  isAgent: boolean
  dateJoined: string
  loginTime: string
  loginIp?: string
}

export const useUserStore = defineStore('user', () => {
  // 状态
  const user = ref<UserData | null>(null)
  const isAuthenticated = ref(false)
  const authMode = ref<'cookie' | 'token'>('cookie')  // 默认使用cookie认证
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  // 计算属性
  const userInfo = computed(() => user.value)
  const isLoggedIn = computed(() => isAuthenticated.value && user.value !== null)
  const userBalance = computed(() => user.value?.asset?.balance || 0)
  
  // 格式化余额显示 - 只返回数字部分，图标由UI组件处理
  const formattedBalance = computed(() => {
    const balance = user.value?.asset?.balance
    
    // 如果没有余额数据，返回默认值
    if (!balance && balance !== 0) {
      return '0.00'
    }
    
    // 如果余额是字符串且已包含货币符号，提取数字部分
    if (typeof balance === 'string' && balance.includes('$')) {
      const numStr = balance.replace('$', '').trim()
      const numBalance = parseFloat(numStr)
      return !isNaN(numBalance) ? numBalance.toFixed(2) : '0.00'
    }
    
    // 如果是数字，格式化为两位小数
    if (typeof balance === 'number') {
      return balance.toFixed(2)
    }
    
    // 如果是字符串但不包含$，尝试转换为数字后格式化
    if (typeof balance === 'string') {
      const numBalance = parseFloat(balance)
      if (!isNaN(numBalance)) {
        return numBalance.toFixed(2)
      }
    }
    
    // 默认返回
    return '0.00'
  })
  const userPoints = computed(() => user.value?.asset?.points || 0)
  const userActivePoints = computed(() => user.value?.asset?.active_point || 0)
  const userLevel = computed(() => user.value?.level || 0)
  const isVip = computed(() => user.value?.is_vip ?? false)
  const isAgent = computed(() => user.value?.is_agent ?? false)
  const userAvatar = computed(() => {
    // 优先使用Steam头像，其次使用profile头像
    return user.value?.steam?.avatarmedium || 
           user.value?.steam?.avatar || 
           user.value?.profile?.avatar || 
           '/images/default-avatar.png'
  })
  const userNickname = computed(() => {
    // 优先使用Steam昵称，其次使用系统昵称
    return user.value?.steam?.personaname || 
           user.value?.nickname || 
           '未设置昵称'
  })
  const steamTradeUrl = computed(() => user.value?.asset?.tradeurl || '')
  const userEmail = computed(() => user.value?.email || '')
  const userId = computed(() => user.value?.uid || '')

  // 获取当前用户信息
  const fetchCurrentUser = async (): Promise<boolean> => {
    try {
      isLoading.value = true
      error.value = null
      
      // console.log('[👤USER] 开始获取用户信息...')
      
      const userData = await authApi.getCurrentUser()
      
      if (userData) {
        user.value = userData
        isAuthenticated.value = true
        
        console.log('[👤USER] 用户信息获取成功:', {
          uid: userData.uid,
          nickname: userData.nickname,
          email: userData.email,
          balance: userData.asset?.balance
        })
        
        return true
      } else {
        console.warn('[👤USER] 获取用户信息失败: 用户未登录')
        clearAuth()
        return false
      }
    } catch (error: any) {
      console.error('[👤USER] 获取用户信息异常:', error)
      clearAuth()
      return false
    } finally {
      isLoading.value = false
    }
  }

  // 检查登录状态
  const checkLoginStatus = async (): Promise<boolean> => {
    try {
      const isLoggedIn = await authApi.checkLogin()
      
      if (isLoggedIn && !user.value) {
        // 如果已登录但没有用户信息，获取用户信息
        await fetchCurrentUser()
      } else if (!isLoggedIn) {
        // 如果未登录，清除本地状态
        clearAuth()
      }
      
      return isLoggedIn
    } catch (error) {
      console.error('[👤USER] 检查登录状态失败:', error)
      clearAuth()
      return false
    }
  }

  // 更新用户信息
  const updateUserInfo = (userData: Partial<UserData>): void => {
    if (user.value) {
      user.value = { ...user.value, ...userData }
    }
  }

  // 更新余额
  const updateBalance = (newBalance: number): void => {
    if (user.value?.asset) {
      user.value.asset.balance = newBalance
    }
  }

  // 更新积分
  const updatePoints = (newPoints: number): void => {
    if (user.value?.asset) {
      user.value.asset.points = newPoints
    }
  }

  // 更新活跃积分
  const updateActivePoints = (newActivePoints: number): void => {
    if (user.value?.asset) {
      user.value.asset.active_point = newActivePoints
    }
  }

  // 设置Steam交易链接
  const setSteamTradeUrl = async (tradeUrl: string): Promise<boolean> => {
    try {
      const response = await authApi.setSteamTradeUrl(tradeUrl)
      
      if (response.success) {
        // 更新本地状态
        if (user.value?.asset) {
          user.value.asset.tradeurl = tradeUrl
        }
        return true
      } else {
        error.value = response.message || '设置Steam交易链接失败'
        return false
      }
    } catch (error: any) {
      console.error('[👤USER] 设置Steam交易链接失败:', error)
      return false
    }
  }

  // 设置昵称
  const setNickname = async (nickname: string): Promise<boolean> => {
    try {
      const response = await authApi.setNickname(nickname)
      
      if (response.code === 0) {
        // 更新本地状态
        if (user.value) {
          user.value.nickname = nickname
        }
        return true
      } else {
        error.value = response.message || '设置昵称失败'
        return false
      }
    } catch (error: any) {
      console.error('[👤USER] 设置昵称失败:', error)
      return false
    }
  }

  // 设置头像
  const setAvatar = async (avatarData: string): Promise<boolean> => {
    try {
      const response = await authApi.setAvatar(avatarData)
      
      if (response.success && response.body?.avatar_url) {
        // 更新本地状态 - 使用API返回的头像URL
        if (user.value?.profile) {
          user.value.profile.avatar = response.body.avatar_url
        }
        return true
      } else {
        error.value = response.message || '设置头像失败'
        return false
      }
    } catch (error: any) {
      console.error('[👤USER] 设置头像失败:', error)
      return false
    }
  }

  // 清除认证状态
  const clearAuth = (): void => {
    user.value = null
    isAuthenticated.value = false
    error.value = null
    
    // 清除本地存储
    if (process.client) {
      localStorage.removeItem('authMode')
      localStorage.removeItem('token')
    }
    
    console.log('[👤USER] 认证状态已清除')
  }

  // 登出
  const logout = async (): Promise<void> => {
    try {
      await authApi.logout()
    } catch (error) {
      console.warn('[👤USER] 登出API调用失败，但继续清除本地状态')
    } finally {
      clearAuth()
      
      // 重定向到首页
      if (process.client) {
        await navigateTo('/')
      }
    }
  }

  // 初始化用户状态
  const initUserState = async (): Promise<void> => {
    if (process.client) {
      try {
        // 检查登录状态
        const isLoggedIn = await checkLoginStatus()
        
        if (isLoggedIn) {
          console.log('[👤USER] 用户状态初始化成功')
        } else {
          console.log('[👤USER] 用户未登录')
        }
      } catch (error) {
        console.warn('[👤USER] 用户状态初始化失败:', error)
        clearAuth()
      }
    }
  }

  // 刷新用户数据
  const refreshUserData = async (): Promise<boolean> => {
    return await fetchCurrentUser()
  }

  // 检查用户权限
  const hasPermission = (permission: string): boolean => {
    if (!user.value) return false
    
    switch (permission) {
      case 'vip':
        return user.value.is_vip ?? false
      case 'agent':
        return user.value.is_agent ?? false
      case 'active':
        return user.value.is_active ?? false
      case 'chat':
        return !(user.value.extra?.ban_chat ?? false)
      case 'deposit':
        return !(user.value.extra?.ban_deposit ?? false)
      case 'withdraw':
        return !(user.value.extra?.ban_withdraw ?? false)
      default:
        return false
    }
  }

  // 获取用户统计信息
  const getUserStats = computed(() => {
    if (!user.value || !user.value.asset) return null
    
    return {
      level: user.value.level,
      balance: user.value.asset.balance,
      points: user.value.asset.points,
      activePoints: user.value.asset.active_point,
      diamond: user.value.asset.diamond || 0,
      freeBoxCount: user.value.extra?.box_free_count || 0,
      steamLevel: user.value.steam?.level || 0,
      joinDate: user.value.date_joined,
      lastLogin: user.value.login_time
    }
  })

  return {
    // 状态
    user: readonly(user),
    isAuthenticated: readonly(isAuthenticated),
    authMode,
    isLoading: readonly(isLoading),
    error: readonly(error),
    
    // 计算属性
    userInfo,
    isLoggedIn,
    userBalance,
    formattedBalance,
    userPoints,
    userActivePoints,
    userLevel,
    isVip,
    isAgent,
    userAvatar,
    userNickname,
    steamTradeUrl,
    userEmail,
    userId,
    getUserStats,
    
    // 方法
    fetchCurrentUser,
    checkLoginStatus,
    updateUserInfo,
    updateBalance,
    updatePoints,
    updateActivePoints,
    setSteamTradeUrl,
    setNickname,
    setAvatar,
    clearAuth,
    logout,
    initUserState,
    refreshUserData,
    hasPermission
  }
})
